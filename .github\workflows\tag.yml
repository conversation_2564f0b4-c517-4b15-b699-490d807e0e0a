name: Tag Release

on:
    pull_request:
        branches:
            - main

permissions:
    contents: write

jobs:
  build-and-publish:
    runs-on: ubuntu-latest

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v3

      - name: 🐍 Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Read version from pyproject.toml
        id: get_version
        run: |
          VERSION=$(grep -Po '(?<=version = ")[^"]*' pyproject.toml)
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT

      - name: Create and push Git tag
        run: |
          git config user.name "github-actions"
          git config user.email "<EMAIL>"
          git tag v${{ steps.get_version.outputs.VERSION }}
          git push origin v${{ steps.get_version.outputs.VERSION }}            