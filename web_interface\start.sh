#!/bin/bash

echo "========================================"
echo "  BTDB2 农场策略模拟器 Web界面"
echo "========================================"
echo

echo "正在检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误：未找到Python3，请先安装Python 3.7+"
    exit 1
fi

echo "正在安装依赖..."
pip3 install -r requirements.txt

echo
echo "正在启动服务器..."
echo "请在浏览器中访问: http://localhost:5000"
echo
echo "按 Ctrl+C 停止服务器"
echo

python3 server.py
