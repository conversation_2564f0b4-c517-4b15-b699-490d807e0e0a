name: Run Tests

on:
  push:
    branches:
      - develop

jobs:
  build-and-publish:
    runs-on: ubuntu-latest

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v3

      - name: 🐍 Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: 🔧 Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install build pytest
          pip install .[analysis]

      - name: ✅ Run tests
        run: |
          pytest tests