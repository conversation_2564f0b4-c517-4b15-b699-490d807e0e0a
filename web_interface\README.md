# BTDB2 农场策略模拟器 - Web界面

这是一个为《气球塔防对战2》(Bloons TD Battles 2)设计的农场策略模拟器的Web界面。

## 🌟 功能特点

- **直观的Web界面**：使用Vue.js构建的现代化用户界面
- **实时模拟**：集成真实的b2sim Python库进行精确模拟
- **可视化分析**：使用Chart.js提供丰富的图表分析
- **优质土壤优化**：已更新为15%折扣（原20%）
- **响应式设计**：支持桌面和移动设备

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 如果需要完整功能，请安装b2sim库
pip install b2sim
```

### 2. 启动服务器

```bash
python server.py
```

### 3. 访问界面

打开浏览器访问：http://localhost:5000

## 📋 使用说明

### 模拟器页面

1. **设置初始状态**
   - 初始现金：游戏开始时的现金数量
   - 初始经济：游戏开始时的经济值
   - 开始/结束回合：模拟的回合范围

2. **配置农场**
   - 点击"添加农场"创建新农场
   - 设置每个农场的升级路径（上路/中路/下路）
   - 优质土壤（下路1级）提供15%升级费用折扣

3. **选择经济策略**
   - 选择不同的经济发送类型
   - 影响经济增长速度

4. **运行模拟**
   - 点击"开始模拟"运行计算
   - 查看实时结果和图表

### 分析页面

- **农场效率分析**：查看不同农场类型的投资回报率
- **优质土壤影响**：对比折扣前后的成本差异

## 🔧 技术架构

### 前端
- **Vue.js 3**：响应式框架
- **Bootstrap 5**：UI组件库
- **Chart.js**：图表库
- **Font Awesome**：图标库

### 后端
- **Flask**：Python Web框架
- **b2sim**：核心模拟引擎
- **Flask-CORS**：跨域支持

## 📊 农场升级路径说明

### 上路（香蕉产量）
- 0级：基础农场
- 1级：更多香蕉
- 2级：香蕉种植园
- 3级：香蕉研究设施
- 4级：香蕉中心
- 5级：香蕉研究设施

### 中路（银行系统）
- 0级：基础农场
- 1级：有价值的香蕉
- 2级：猴子银行
- 3级：IMF贷款
- 4级：猴子经济学

### 下路（效率优化）
- 0级：基础农场
- 1级：EZ收集
- 2级：优质土壤（提供15%升级折扣）
- 3级：长寿香蕉
- 4级：有价值的香蕉
- 5级：猴子华尔街

## 🎯 优质土壤更新

**重要更新**：优质土壤的升级费用折扣已从20%调整为15%。

- **之前**：0.8倍成本（20%折扣）
- **现在**：0.85倍成本（15%折扣）

这个调整影响所有上路和中路的升级成本计算。

## 🐛 故障排除

### 如果b2sim库不可用
- 系统会自动使用简化的模拟逻辑
- 功能略有限制但基本可用
- 建议安装完整的b2sim库获得最佳体验

### 常见问题
1. **端口占用**：如果5000端口被占用，修改server.py中的端口号
2. **跨域问题**：确保Flask-CORS正确安装
3. **模拟结果异常**：检查输入参数是否合理

## 📝 开发说明

### 文件结构
```
web_interface/
├── index.html          # 主页面
├── app.js             # Vue.js应用
├── styles.css         # 样式文件
├── server.py          # Flask后端
├── requirements.txt   # Python依赖
└── README.md         # 说明文档
```

### 自定义开发
- 修改`app.js`添加新功能
- 编辑`styles.css`调整界面样式
- 扩展`server.py`添加新的API端点

## 📄 许可证

本项目基于原始b2sim项目构建，请遵循相应的开源许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**享受你的BTDB2农场策略优化之旅！** 🎮🍌
