[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "b2sim"
version = "2.3.8"
description = "A library for the simulation of eco & farm based strategies in Bloons TD Battles 2"
authors = [
  { name="redlaserbm", email="<EMAIL>" }
]
readme = "README.md"
license = {file = "LICENSE"}
requires-python = ">=3.10"

[project.optional-dependencies]
analysis = [
    "pandas",
    "matplotlib"
]

[tool.setuptools]
package-dir = {"" = "src"}
include-package-data = true

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"b2sim.templates" = ["*.csv"]

[project.urls]
Homepage = "https://github.com/redlaserbm/BTDB2_SIMULATOR/"