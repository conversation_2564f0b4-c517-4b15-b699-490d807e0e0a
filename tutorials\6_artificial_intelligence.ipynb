{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Artificial Intelligence\n", "\n", "Using the neat algorithm, b2sim can automatically optimize flowcharts using neural networks. In this tutorial document, we showcase how this works. "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import b2sim.engine as b2\n", "import b2sim.analysis as dd\n", "from math import log"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## NEAT Algorithm For Idiots\n", "\n", "The end-user should have at least a baseline understanding of how the NEAT algorithm works before proceeding. I offer such a baseline understanding of how the algorithm works as follows: First, a population of genomes are tasked to operate the simulator, making decisions in real time concerning what eco sends to use and what farms to buy. When they finish operating the simulator until the target time, they are graded on their performance. The best performers of this population are picked to mate and produce offspring bots that share similar characteristics to their parents. The new populaton of offspring genomes is then asked to operate the simulator themselves where again, upon completion of simulations, the best genomes are picked to reproduce for the next generation of genomes. When the last generation is finished, the winning genome with the best fitness is picked. This winner is the winner the AI has determined to be the winning strategy."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic Experiment Design\n", "\n", "Start by declaring the initial game state parameters and the ending time you want to optimize until. To perform optimization with AI, we're going to initialize the *AI* class with the initial game state as the initialization argument."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["rounds = b2.Rounds(0.1)\n", "\n", "farms = [\n", "    b2.initFarm(rounds.getTimeFromRound(7), upgrades = [3,2,0]),\n", "    b2.initFarm(rounds.getTimeFromRound(13.9), upgrades = [3,2,0])\n", "]\n", "\n", "initial_state_game = {\n", "    'Cash': 0,\n", "    'Eco': 600,\n", "    'Rounds': rounds, #Determines the lengths of the rounds in the game state\n", "    'Farms': farms,\n", "    'Game Round': 13.99\n", "}\n", "\n", "target_time = rounds.getTimeFromRound(20)\n", "\n", "ai = dd.AI(initial_state_game)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fitness Functions\n", "\n", "To train the AI, you must give it a fitness function which the AI can use to evaluate the effectiveness of a given bot. There are two main fitness functions to get you started:\n", "1. cashGen - Grades bots based on the amount of cash it generates over some time period after the optimization interval. This is useful for greedily optimizing for income generation.\n", "2. terminalCash - Grades bots based on the amount of cash held at some point in time. This point in time can be immediately at the end of the optimization interval, or it could be over some time in the future. \n", "\n", "These fitness functions take two arguments: A GameState class instance, and a *parameters* dictionary containing additional arguments. For more info on how this works, you should read through the function definitions in `fitness.py`. For our first example, we will utilize cashGen fitness.\n", "\n", "Below, I set the parameters so that the bots will be graded based on the amount of cash the simulation generates over the next 4 rounds after the end of the optimization interval. For this particular example, that means I'm grading the AI based on cash generated from the start of R20 to the end of R24. I'm also asking the AI to generate strategies which produce a minimum of 1000 eco by simulation end."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["parameters = {\n", "    'Unit Type': 'Rounds', # Unit of time to measure cash generation over\n", "    'Units To Measure': 3, # How many rounds (or seconds) after the simulation end should we track cash generation?\n", "    'Minimum Eco': 1000, # Genomes which produce less than this amount of eco will be penalized\n", "    'Eco Tolerance': 100 # Genomes which produce less than the minimum eco minus the eco tolerance will be graded zero. \n", "    # The tolerance variable allows genomes to only be partially penalized if they miss the minimum eco by a small amount.\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Training\n", "\n", "We are now ready to train the AI. As a fair warning, good training sessions take some time. I recommend doing something else in the background while waiting for the AI to return its results. The `increment_value` determines how frequently genomes are polled for input on what action to take next. You can increase this value for faster training at the risk of the AI failing to return good results."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " ****** Running generation 0 ****** \n", "\n", "Population's average fitness: 9064.13900 stdev: 7081.89208\n", "Best fitness: 16304.69579 - size: (2, 8) - species 1 - id 67\n", "Average adjusted fitness: 0.556\n", "Mean genetic distance 1.150, standard deviation 0.309\n", "Population of 150 members in 1 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1    0   150  16304.696    0.556     0\n", "Total extinctions: 0\n", "Generation time: 9.804 sec\n", "\n", " ****** Running generation 1 ****** \n", "\n", "Population's average fitness: 12612.12460 stdev: 5920.53987\n", "Best fitness: 16312.56607 - size: (2, 8) - species 1 - id 283\n", "Average adjusted fitness: 0.773\n", "Mean genetic distance 1.214, standard deviation 0.315\n", "Population of 150 members in 1 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1    1   150  16312.566    0.773     0\n", "Total extinctions: 0\n", "Generation time: 9.957 sec (9.881 average)\n", "\n", " ****** Running generation 2 ****** \n", "\n", "Population's average fitness: 13551.60960 stdev: 5083.31234\n", "Best fitness: 16400.02082 - size: (3, 7) - species 1 - id 336\n", "Average adjusted fitness: 0.826\n", "Mean genetic distance 1.241, standard deviation 0.341\n", "Population of 150 members in 1 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1    2   150  16400.021    0.826     0\n", "Total extinctions: 0\n", "Generation time: 10.088 sec (9.950 average)\n", "\n", " ****** Running generation 3 ****** \n", "\n", "Population's average fitness: 13574.66616 stdev: 4809.82666\n", "Best fitness: 16400.02082 - size: (3, 7) - species 1 - id 336\n", "Average adjusted fitness: 0.828\n", "Mean genetic distance 1.563, standard deviation 0.397\n", "Population of 150 members in 2 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1    3   110  16400.021    0.828     1\n", "     2    0    40         --       --     0\n", "Total extinctions: 0\n", "Generation time: 10.115 sec (9.991 average)\n", "\n", " ****** Running generation 4 ****** \n", "\n", "Population's average fitness: 13418.69884 stdev: 5199.06143\n", "Best fitness: 16401.28158 - size: (3, 8) - species 1 - id 657\n", "Average adjusted fitness: 0.814\n", "Mean genetic distance 1.685, standard deviation 0.379\n", "Population of 150 members in 2 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1    4    74  16401.282    0.823     0\n", "     2    1    76  16356.048    0.806     0\n", "Total extinctions: 0\n", "Generation time: 10.215 sec (10.036 average)\n", "Saving checkpoint to neat-checkpoint-4\n", "\n", " ****** Running generation 5 ****** \n", "\n", "Population's average fitness: 13001.08963 stdev: 6004.78003\n", "Best fitness: 16401.28158 - size: (3, 8) - species 1 - id 657\n", "Average adjusted fitness: 0.793\n", "Mean genetic distance 1.651, standard deviation 0.323\n", "Population of 150 members in 2 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1    5    69  16401.282    0.829     1\n", "     2    2    81  16371.505    0.758     0\n", "Total extinctions: 0\n", "Generation time: 10.046 sec (10.038 average)\n", "\n", " ****** Running generation 6 ****** \n", "\n", "Population's average fitness: 13246.18908 stdev: 5574.16680\n", "Best fitness: 16401.28158 - size: (3, 8) - species 1 - id 657\n", "Average adjusted fitness: 0.804\n", "Mean genetic distance 1.803, standard deviation 0.443\n", "Population of 150 members in 3 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1    6    82  16401.282    0.757     2\n", "     2    3    62  16375.167    0.851     0\n", "     3    0     6         --       --     0\n", "Total extinctions: 0\n", "Generation time: 10.461 sec (10.098 average)\n", "\n", " ****** Running generation 7 ****** \n", "\n", "Population's average fitness: 12894.28696 stdev: 5972.77443\n", "Best fitness: 16401.80573 - size: (2, 3) - species 2 - id 1048\n", "Average adjusted fitness: 0.737\n", "Mean genetic distance 1.811, standard deviation 0.436\n", "Population of 150 members in 3 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1    7    75  16401.282    0.759     3\n", "     2    4    30  16401.806    0.838     0\n", "     3    1    45  16314.745    0.615     0\n", "Total extinctions: 0\n", "Generation time: 10.192 sec (10.110 average)\n", "\n", " ****** Running generation 8 ****** \n", "\n", "Population's average fitness: 13355.14219 stdev: 5604.74432\n", "Best fitness: 16401.80573 - size: (2, 3) - species 2 - id 1048\n", "Average adjusted fitness: 0.821\n", "Mean genetic distance 1.752, standard deviation 0.436\n", "Population of 151 members in 3 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1    8    54  16401.282    0.822     4\n", "     2    5    49  16401.806    0.888     1\n", "     3    2    48  16388.105    0.751     0\n", "Total extinctions: 0\n", "Generation time: 10.208 sec (10.121 average)\n", "\n", " ****** Running generation 9 ****** \n", "\n", "Population's average fitness: 12805.97330 stdev: 6039.94958\n", "Best fitness: 16401.80573 - size: (2, 3) - species 2 - id 1048\n", "Average adjusted fitness: 0.780\n", "Mean genetic distance 1.900, standard deviation 0.476\n", "Population of 149 members in 4 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1    9    63  16401.282    0.810     5\n", "     2    6    37  16401.806    0.776     2\n", "     3    3    38  16388.913    0.753     0\n", "     4    0    11         --       --     0\n", "Total extinctions: 0\n", "Generation time: 10.380 sec (10.147 average)\n", "Saving checkpoint to neat-checkpoint-9\n", "\n", " ****** Running generation 10 ****** \n", "\n", "Population's average fitness: 11587.70920 stdev: 6653.82208\n", "Best fitness: 16401.80573 - size: (2, 3) - species 1 - id 1048\n", "Average adjusted fitness: 0.653\n", "Mean genetic distance 1.875, standard deviation 0.447\n", "Population of 149 members in 4 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   10    27  16401.806    0.759     0\n", "     2    7    24  16366.595    0.686     3\n", "     3    4    44  16394.780    0.712     0\n", "     4    1    54  16352.114    0.456     0\n", "Total extinctions: 0\n", "Generation time: 10.084 sec (10.175 average)\n", "\n", " ****** Running generation 11 ****** \n", "\n", "Population's average fitness: 13121.17330 stdev: 5686.46565\n", "Best fitness: 16401.92173 - size: (2, 3) - species 3 - id 1676\n", "Average adjusted fitness: 0.785\n", "Mean genetic distance 1.866, standard deviation 0.473\n", "Population of 149 members in 4 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   11    32  16401.806    0.656     1\n", "     2    8    36  16340.398    0.797     4\n", "     3    5    39  16401.922    0.874     0\n", "     4    2    42  16401.282    0.813     0\n", "Total extinctions: 0\n", "Generation time: 9.977 sec (10.177 average)\n", "\n", " ****** Running generation 12 ****** \n", "\n", "Population's average fitness: 12552.85801 stdev: 6110.75726\n", "Best fitness: 16402.12570 - size: (3, 7) - species 4 - id 1794\n", "Average adjusted fitness: 0.763\n", "Mean genetic distance 1.966, standard deviation 0.480\n", "Population of 151 members in 6 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   12    28  16349.313    0.678     2\n", "     2    9    35  16401.806    0.795     5\n", "     3    6    40  16402.126    0.860     0\n", "     4    3    39  16402.126    0.718     0\n", "     5    0     7         --       --     0\n", "     6    0     2         --       --     0\n", "Total extinctions: 0\n", "Generation time: 9.873 sec (10.155 average)\n", "\n", " ****** Running generation 13 ****** \n", "\n", "Population's average fitness: 13361.33081 stdev: 5452.05238\n", "Best fitness: 16402.12570 - size: (3, 7) - species 4 - id 1794\n", "Average adjusted fitness: 0.762\n", "Mean genetic distance 1.978, standard deviation 0.537\n", "Population of 150 members in 6 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   13    32  16401.806    0.784     3\n", "     2   10    25  16364.112    0.840     6\n", "     3    7    25  16402.126    0.762     1\n", "     4    4    20  16402.126    0.883     1\n", "     5    1    45  16281.786    0.824     0\n", "     6    1     3  15772.922    0.481     0\n", "Total extinctions: 0\n", "Generation time: 10.048 sec (10.148 average)\n", "\n", " ****** Running generation 14 ****** \n", "\n", "Population's average fitness: 12668.32906 stdev: 6134.46720\n", "Best fitness: 16402.12570 - size: (3, 7) - species 4 - id 1794\n", "Average adjusted fitness: 0.649\n", "Mean genetic distance 1.927, standard deviation 0.493\n", "Population of 151 members in 6 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   14    32  16401.806    0.730     4\n", "     2   11     8  16401.922    0.877     0\n", "     3    8    18  16402.126    0.727     2\n", "     4    5    31  16402.126    0.713     2\n", "     5    2    60  16339.601    0.847     0\n", "     6    2     2      0.000    0.000     1\n", "Total extinctions: 0\n", "Generation time: 9.891 sec (10.116 average)\n", "Saving checkpoint to neat-checkpoint-14\n", "\n", " ****** Running generation 15 ****** \n", "\n", "Population's average fitness: 12013.62378 stdev: 6538.23117\n", "Best fitness: 16402.12570 - size: (3, 7) - species 4 - id 1794\n", "Average adjusted fitness: 0.603\n", "Mean genetic distance 1.966, standard deviation 0.560\n", "Population of 151 members in 6 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   15    34  16401.922    0.714     0\n", "     2   12    29  16401.806    0.570     1\n", "     3    9    18  16348.220    0.814     3\n", "     4    6    38  16402.126    0.780     3\n", "     5    3    27  16390.857    0.739     0\n", "     6    3     5      0.000    0.000     2\n", "Total extinctions: 0\n", "Generation time: 10.115 sec (10.123 average)\n", "\n", " ****** Running generation 16 ****** \n", "\n", "Population's average fitness: 12613.02033 stdev: 6335.72200\n", "Best fitness: 16402.12570 - size: (3, 7) - species 4 - id 1794\n", "Average adjusted fitness: 0.712\n", "Mean genetic distance 2.045, standard deviation 0.593\n", "Population of 148 members in 7 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   16    44  16401.922    0.856     1\n", "     2   13    28  16401.806    0.679     2\n", "     3   10    16  16388.913    0.762     4\n", "     4    7    10  16402.126    0.821     4\n", "     5    4    18  16390.857    0.756     1\n", "     6    4    19  16353.057    0.398     0\n", "     7    0    13         --       --     0\n", "Total extinctions: 0\n", "Generation time: 10.239 sec (10.101 average)\n", "\n", " ****** Running generation 17 ****** \n", "\n", "Population's average fitness: 12932.15752 stdev: 6028.99679\n", "Best fitness: 16402.12570 - size: (2, 3) - species 1 - id 1856\n", "Average adjusted fitness: 0.760\n", "Mean genetic distance 2.108, standard deviation 0.641\n", "Population of 150 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   17    32  16402.126    0.847     0\n", "     2   14    19  16401.806    0.762     3\n", "     3   11    22  16388.913    0.657     5\n", "     4    8    22  16330.770    0.680     5\n", "     5    5    19  16390.857    0.917     2\n", "     6    5    25  16402.126    0.879     0\n", "     7    1     5  16315.614    0.582     0\n", "     8    0     6         --       --     0\n", "Total extinctions: 0\n", "Generation time: 9.922 sec (10.074 average)\n", "\n", " ****** Running generation 18 ****** \n", "\n", "Population's average fitness: 12524.69725 stdev: 6324.43816\n", "Best fitness: 16402.12570 - size: (3, 7) - species 4 - id 1794\n", "Average adjusted fitness: 0.710\n", "Mean genetic distance 2.101, standard deviation 0.634\n", "Population of 149 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   18    27  16401.922    0.922     1\n", "     2   15    21  16401.806    0.907     4\n", "     3   12    18  16388.913    0.606     6\n", "     4    9    17  16402.126    0.692     6\n", "     5    6    19  16339.863    0.768     3\n", "     6    6    24  16375.167    0.753     1\n", "     7    2    11  16283.945    0.392     1\n", "     8    1    12  16390.857    0.640     0\n", "Total extinctions: 0\n", "Generation time: 9.938 sec (10.047 average)\n", "\n", " ****** Running generation 19 ****** \n", "\n", "Population's average fitness: 13130.66367 stdev: 5821.33081\n", "Best fitness: 16402.12570 - size: (2, 5) - species 1 - id 2681\n", "Average adjusted fitness: 0.807\n", "Mean genetic distance 2.077, standard deviation 0.613\n", "Population of 149 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   19    25  16402.126    0.905     2\n", "     2   16    19  16401.806    0.690     5\n", "     3   13    12  16401.282    0.633     7\n", "     4   10    17  16402.126    0.870     7\n", "     5    7    22  16353.057    0.813     4\n", "     6    7    24  16375.167    0.760     2\n", "     7    3    12  16353.057    0.888     0\n", "     8    2    18  16390.857    0.894     1\n", "Total extinctions: 0\n", "Generation time: 10.006 sec (10.009 average)\n", "Saving checkpoint to neat-checkpoint-19\n", "\n", " ****** Running generation 20 ****** \n", "\n", "Population's average fitness: 13466.69219 stdev: 5666.95804\n", "Best fitness: 16402.12570 - size: (2, 5) - species 1 - id 2681\n", "Average adjusted fitness: 0.837\n", "Mean genetic distance 2.041, standard deviation 0.526\n", "Population of 152 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   20    12  16402.126    0.810     3\n", "     2   17    22  16401.806    0.871     6\n", "     3   14    13  16401.282    0.885     8\n", "     4   11    23  16402.126    0.852     8\n", "     5    8    24  16353.057    0.778     5\n", "     6    8    23  16375.167    0.674     3\n", "     7    4    23  16359.833    0.960     0\n", "     8    3    12  16390.857    0.868     2\n", "Total extinctions: 0\n", "Generation time: 10.085 sec (10.009 average)\n", "\n", " ****** Running generation 21 ****** \n", "\n", "Population's average fitness: 13650.05573 stdev: 5384.03986\n", "Best fitness: 16402.12570 - size: (2, 5) - species 4 - id 2681\n", "Average adjusted fitness: 0.837\n", "Mean genetic distance 2.024, standard deviation 0.532\n", "Population of 150 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   21    18  16401.922    0.872     4\n", "     2   18    20  16401.806    0.758     7\n", "     3   15    19  16401.282    0.888     9\n", "     4   12    16  16402.126    0.922     9\n", "     5    9    18  16353.057    0.664     6\n", "     6    9    25  16375.167    0.846     4\n", "     7    5    22  16390.857    0.935     0\n", "     8    4    12  16322.033    0.811     3\n", "Total extinctions: 0\n", "Generation time: 10.202 sec (10.032 average)\n", "\n", " ****** Running generation 22 ****** \n", "\n", "Population's average fitness: 13084.42791 stdev: 5918.11955\n", "Best fitness: 16402.12570 - size: (2, 2) - species 3 - id 3200\n", "Average adjusted fitness: 0.785\n", "Mean genetic distance 2.011, standard deviation 0.530\n", "Population of 151 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   22    24  16401.922    0.674     5\n", "     2   19    25  16401.806    0.840     8\n", "     3   16    25  16402.126    0.911    10\n", "     4   13    14  16402.126    0.851    10\n", "     5   10    23  16359.833    0.791     7\n", "     6   10    11  16375.167    0.882     5\n", "     7    6    15  16390.857    0.760     1\n", "     8    5    14  16340.398    0.568     4\n", "Total extinctions: 0\n", "Generation time: 10.027 sec (10.047 average)\n", "\n", " ****** Running generation 23 ****** \n", "\n", "Population's average fitness: 13977.06951 stdev: 4899.24272\n", "Best fitness: 16402.12570 - size: (2, 2) - species 3 - id 3200\n", "Average adjusted fitness: 0.859\n", "Mean genetic distance 1.994, standard deviation 0.518\n", "Population of 151 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   23    21  16402.126    0.850     6\n", "     2   20    21  16401.806    0.720     9\n", "     3   17    15  16402.126    0.936    11\n", "     4   14     8  16359.833    0.892    11\n", "     5   11    30  16359.833    0.841     8\n", "     6   11    22  16375.167    0.885     6\n", "     7    7    18  16390.857    0.787     2\n", "     8    6    16  16340.398    0.963     5\n", "Total extinctions: 0\n", "Generation time: 10.203 sec (10.063 average)\n", "\n", " ****** Running generation 24 ****** \n", "\n", "Population's average fitness: 13922.70083 stdev: 5198.75411\n", "Best fitness: 16402.12570 - size: (2, 5) - species 1 - id 2681\n", "Average adjusted fitness: 0.845\n", "Mean genetic distance 1.970, standard deviation 0.486\n", "Population of 149 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   24    20  16402.126    0.910     7\n", "     2   21    21  16401.806    0.718    10\n", "     3   18    12  16402.126    0.785    12\n", "     4   15    30  16375.167    0.829    12\n", "     5   12    20  16359.833    0.870     9\n", "     6   12     8  16402.126    0.880     7\n", "     7    8    18  16390.857    0.922     3\n", "     8    7    20  16359.833    0.844     6\n", "Total extinctions: 0\n", "Generation time: 10.232 sec (10.097 average)\n", "Saving checkpoint to neat-checkpoint-24\n", "\n", " ****** Running generation 25 ****** \n", "\n", "Population's average fitness: 13532.53631 stdev: 5544.79143\n", "Best fitness: 16402.12570 - size: (3, 7) - species 4 - id 2692\n", "Average adjusted fitness: 0.840\n", "Mean genetic distance 1.974, standard deviation 0.497\n", "Population of 150 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   25    23  16402.126    0.765     8\n", "     2   22    16  16401.806    0.699    11\n", "     3   19    18  16402.126    0.851    13\n", "     4   16     8  16402.126    0.789    13\n", "     5   13    26  16359.833    0.918    10\n", "     6   13    26  16349.270    0.974     8\n", "     7    9    16  16390.857    0.794     4\n", "     8    8    17  16364.112    0.932     7\n", "Total extinctions: 0\n", "Generation time: 9.962 sec (10.082 average)\n", "\n", " ****** Running generation 26 ****** \n", "\n", "Population's average fitness: 13622.06418 stdev: 5418.20391\n", "Best fitness: 16402.12570 - size: (3, 7) - species 4 - id 2692\n", "Average adjusted fitness: 0.831\n", "Mean genetic distance 1.931, standard deviation 0.508\n", "Population of 150 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   26    21  16402.126    0.827     9\n", "     2   23    19  16401.806    0.939    12\n", "     3   20    27  16402.126    0.750    14\n", "     4   17     8  16402.126    0.819    14\n", "     5   14    23  16359.833    0.801    11\n", "     6   14    17  16359.833    0.861     9\n", "     7   10    15  16390.857    0.773     5\n", "     8    9    20  16364.112    0.876     8\n", "Total extinctions: 0\n", "Generation time: 10.144 sec (10.072 average)\n", "\n", " ****** Running generation 27 ****** \n", "\n", "Population's average fitness: 13626.21240 stdev: 5421.12305\n", "Best fitness: 16402.12570 - size: (3, 7) - species 3 - id 2692\n", "Average adjusted fitness: 0.844\n", "Mean genetic distance 1.956, standard deviation 0.511\n", "Population of 150 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   27    18  16402.126    0.852    10\n", "     2   24    19  16401.806    0.760    13\n", "     3   21    24  16402.126    0.763    15\n", "     4   18    17  16349.313    0.971    15\n", "     5   15    25  16359.833    0.856    12\n", "     6   15    18  16375.167    0.919    10\n", "     7   11    10  16390.857    0.795     6\n", "     8   10    19  16364.112    0.835     9\n", "Total extinctions: 0\n", "Generation time: 10.136 sec (10.094 average)\n", "\n", " ****** Running generation 28 ****** \n", "\n", "Population's average fitness: 13229.78252 stdev: 5836.60878\n", "Best fitness: 16402.12570 - size: (2, 5) - species 1 - id 3778\n", "Average adjusted fitness: 0.808\n", "Mean genetic distance 1.965, standard deviation 0.500\n", "Population of 149 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   28    19  16402.126    0.787    11\n", "     2   25    19  16401.806    0.801    14\n", "     3   22    23  16402.126    0.763    16\n", "     4   19    15  16349.313    0.895    16\n", "     5   16    21  16359.833    0.775    13\n", "     6   16    20  16375.167    0.865    11\n", "     7   12    14  16390.857    0.766     7\n", "     8   11    18  16364.112    0.814    10\n", "Total extinctions: 0\n", "Generation time: 10.094 sec (10.109 average)\n", "\n", " ****** Running generation 29 ****** \n", "\n", "Population's average fitness: 14069.39436 stdev: 4800.76629\n", "Best fitness: 16402.12570 - size: (2, 5) - species 1 - id 3778\n", "Average adjusted fitness: 0.862\n", "Mean genetic distance 2.006, standard deviation 0.517\n", "Population of 150 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   29    17  16402.126    0.928    12\n", "     2   26    23  16401.806    0.701    15\n", "     3   23    22  16402.126    0.904    17\n", "     4   20    22  16349.313    0.905    17\n", "     5   17    22  16359.833    0.836    14\n", "     6   17     8  16375.167    0.807    12\n", "     7   13    17  16390.857    0.964     8\n", "     8   12    19  16364.112    0.850    11\n", "Total extinctions: 0\n", "Generation time: 10.184 sec (10.127 average)\n", "Saving checkpoint to neat-checkpoint-29\n", "\n", " ****** Running generation 30 ****** \n", "\n", "Population's average fitness: 13107.12872 stdev: 5925.71779\n", "Best fitness: 16432.50618 - size: (2, 3) - species 2 - id 4284\n", "Average adjusted fitness: 0.806\n", "Mean genetic distance 2.026, standard deviation 0.580\n", "Population of 151 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   30    18  16402.126    0.811    13\n", "     2   27    20  16432.506    0.713     0\n", "     3   24    17  16402.126    0.714    18\n", "     4   21    22  16359.833    0.743    18\n", "     5   18    23  16359.833    0.872    15\n", "     6   18    14  16375.167    0.833    13\n", "     7   14    17  16390.857    0.923     9\n", "     8   13    20  16364.112    0.835    12\n", "Total extinctions: 0\n", "Generation time: 10.104 sec (10.129 average)\n", "\n", " ****** Running generation 31 ****** \n", "\n", "Population's average fitness: 13064.25879 stdev: 6017.28331\n", "Best fitness: 16432.50618 - size: (2, 3) - species 2 - id 4284\n", "Average adjusted fitness: 0.801\n", "Mean genetic distance 1.994, standard deviation 0.578\n", "Population of 150 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   31    18  16402.126    0.799    14\n", "     2   28    28  16432.506    0.701     1\n", "     3   25    23  16402.126    0.851    19\n", "     4   22    23  16359.833    0.792    19\n", "     5   19    10  16390.857    0.750    16\n", "     6   19    13  16375.167    0.874    14\n", "     7   15    17  16390.857    0.864    10\n", "     8   14    18  16364.112    0.780    13\n", "Total extinctions: 0\n", "Generation time: 10.082 sec (10.117 average)\n", "\n", " ****** Running generation 32 ****** \n", "\n", "Population's average fitness: 14177.80326 stdev: 4808.95465\n", "Best fitness: 16432.50618 - size: (2, 3) - species 2 - id 4284\n", "\n", "Species 4 with 23 members is stagnated: removing it\n", "Average adjusted fitness: 0.873\n", "Mean genetic distance 2.030, standard deviation 0.569\n", "Population of 150 members in 7 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   32    18  16402.126    0.844    15\n", "     2   29    22  16432.506    0.754     2\n", "     3   26    25  16402.126    0.899    20\n", "     5   20    23  16322.033    0.850    17\n", "     6   20    19  16375.167    0.872    15\n", "     7   16    22  16390.857    0.975    11\n", "     8   15    21  16375.167    0.916    14\n", "Total extinctions: 0\n", "Generation time: 10.221 sec (10.136 average)\n", "\n", " ****** Running generation 33 ****** \n", "\n", "Population's average fitness: 13292.38788 stdev: 5723.73033\n", "Best fitness: 16432.50618 - size: (2, 3) - species 2 - id 4284\n", "Average adjusted fitness: 0.807\n", "Mean genetic distance 1.969, standard deviation 0.530\n", "Population of 148 members in 7 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   33    19  16402.126    0.836    16\n", "     2   30    24  16432.506    0.715     3\n", "     3   27    24  16402.126    0.793    21\n", "     5   21    23  16340.398    0.929    18\n", "     6   21    19  16375.167    0.715    16\n", "     7   17    18  16390.857    0.886    12\n", "     8   16    21  16375.167    0.775    15\n", "Total extinctions: 0\n", "Generation time: 10.110 sec (10.127 average)\n", "\n", " ****** Running generation 34 ****** \n", "\n", "Population's average fitness: 13001.85852 stdev: 6065.22676\n", "Best fitness: 16432.50618 - size: (2, 3) - species 2 - id 4284\n", "Average adjusted fitness: 0.795\n", "Mean genetic distance 1.964, standard deviation 0.517\n", "Population of 150 members in 7 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   34    21  16402.126    0.832    17\n", "     2   31    21  16432.506    0.790     4\n", "     3   28    22  16402.126    0.708    22\n", "     5   22    28  16344.856    0.721    19\n", "     6   22    18  16375.167    0.818    17\n", "     7   18    18  16390.857    0.818    13\n", "     8   17    22  16375.167    0.882    16\n", "Total extinctions: 0\n", "Generation time: 9.983 sec (10.102 average)\n", "Saving checkpoint to neat-checkpoint-34\n", "\n", " ****** Running generation 35 ****** \n", "\n", "Population's average fitness: 14083.95570 stdev: 4942.01454\n", "Best fitness: 16432.50618 - size: (2, 3) - species 2 - id 4284\n", "\n", "Species 5 with 28 members is stagnated: removing it\n", "Average adjusted fitness: 0.849\n", "Mean genetic distance 2.078, standard deviation 0.581\n", "Population of 149 members in 7 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   35    21  16402.126    0.781    18\n", "     2   32    27  16432.506    0.769     5\n", "     3   29    28  16402.126    0.878    23\n", "     6   23    24  16375.167    0.913    18\n", "     7   19    20  16390.857    0.785    14\n", "     8   18    28  16375.167    0.970    17\n", "     9    0     1         --       --     0\n", "Total extinctions: 0\n", "Generation time: 10.257 sec (10.132 average)\n", "\n", " ****** Running generation 36 ****** \n", "\n", "Population's average fitness: 13662.92728 stdev: 5301.37946\n", "Best fitness: 16432.50618 - size: (2, 3) - species 2 - id 4284\n", "Average adjusted fitness: 0.833\n", "Mean genetic distance 2.111, standard deviation 0.591\n", "Population of 151 members in 7 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     1   36    21  16402.126    0.845    19\n", "     2   33    28  16432.506    0.802     6\n", "     3   30    23  16402.126    0.781    24\n", "     6   24    23  16375.167    0.935    19\n", "     7   20    21  16390.857    0.724    15\n", "     8   19    23  16375.167    0.888    18\n", "     9    1    12  14073.448    0.856     0\n", "Total extinctions: 0\n", "Generation time: 10.181 sec (10.135 average)\n", "\n", " ****** Running generation 37 ****** \n", "\n", "Population's average fitness: 13182.70073 stdev: 5668.02804\n", "Best fitness: 16432.50618 - size: (2, 3) - species 2 - id 4284\n", "\n", "Species 6 with 23 members is stagnated: removing it\n", "\n", "Species 1 with 21 members is stagnated: removing it\n", "Average adjusted fitness: 0.798\n", "Mean genetic distance 2.124, standard deviation 0.642\n", "Population of 151 members in 5 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     2   34    35  16432.506    0.700     7\n", "     3   31    28  16402.126    0.744    25\n", "     7   21    32  16390.857    0.877    16\n", "     8   20    32  16375.167    0.882    19\n", "     9    2    24  14126.423    0.784     0\n", "Total extinctions: 0\n", "Generation time: 10.317 sec (10.153 average)\n", "\n", " ****** Running generation 38 ****** \n", "\n", "Population's average fitness: 13527.34152 stdev: 5368.15478\n", "Best fitness: 16432.50618 - size: (2, 3) - species 2 - id 4284\n", "\n", "Species 8 with 32 members is stagnated: removing it\n", "Average adjusted fitness: 0.808\n", "Mean genetic distance 2.233, standard deviation 0.659\n", "Population of 150 members in 5 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     2   35    54  16432.506    0.928     8\n", "     3   32    38  16402.126    0.799    26\n", "     7   22    34  16390.857    0.881    17\n", "     9    3    23  16316.360    0.624     0\n", "    10    0     1         --       --     0\n", "Total extinctions: 0\n", "Generation time: 10.139 sec (10.158 average)\n", "\n", " ****** Running generation 39 ****** \n", "\n", "Population's average fitness: 11232.59926 stdev: 7052.08032\n", "Best fitness: 16432.50618 - size: (2, 3) - species 2 - id 4284\n", "Average adjusted fitness: 0.550\n", "Mean genetic distance 2.235, standard deviation 0.621\n", "Population of 149 members in 5 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     2   36    46  16432.506    0.644     9\n", "     3   33    38  16402.126    0.703    27\n", "     7   23    38  16390.857    0.799    18\n", "     9    4    24  16316.360    0.603     1\n", "    10    1     3      0.000    0.000     0\n", "Total extinctions: 0\n", "Generation time: 9.770 sec (10.116 average)\n", "Saving checkpoint to neat-checkpoint-39\n", "\n", " ****** Running generation 40 ****** \n", "\n", "Population's average fitness: 12405.18825 stdev: 6294.18889\n", "Best fitness: 16432.50618 - size: (2, 3) - species 2 - id 4284\n", "Average adjusted fitness: 0.604\n", "Mean genetic distance 2.219, standard deviation 0.589\n", "Population of 150 members in 6 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     2   37    42  16432.506    0.788    10\n", "     3   34    42  16402.126    0.805    28\n", "     7   24    34  16390.857    0.819    19\n", "     9    5    28  16340.856    0.607     0\n", "    10    2     3      0.000    0.000     1\n", "    11    0     1         --       --     0\n", "Total extinctions: 0\n", "Generation time: 9.815 sec (10.087 average)\n", "\n", " ****** Running generation 41 ****** \n", "\n", "Population's average fitness: 12638.56791 stdev: 6366.44209\n", "Best fitness: 16432.50618 - size: (2, 3) - species 2 - id 4284\n", "\n", "Species 7 with 34 members is stagnated: removing it\n", "Average adjusted fitness: 0.470\n", "Mean genetic distance 2.208, standard deviation 0.555\n", "Population of 150 members in 6 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     2   38    55  16432.506    0.779    11\n", "     3   35    54  16402.126    0.867    29\n", "     9    6    24  16340.856    0.706     1\n", "    10    3     2      0.000    0.000     2\n", "    11    1     2      0.000    0.000     0\n", "    12    0    13         --       --     0\n", "Total extinctions: 0\n", "Generation time: 10.110 sec (10.090 average)\n", "\n", " ****** Running generation 42 ****** \n", "\n", "Population's average fitness: 11751.57794 stdev: 6550.49466\n", "Best fitness: 16582.83934 - size: (4, 2) - species 9 - id 5878\n", "\n", "Species 3 with 54 members is stagnated: removing it\n", "Average adjusted fitness: 0.402\n", "Mean genetic distance 2.130, standard deviation 0.574\n", "Population of 149 members in 5 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     2   39    66  16432.506    0.672    12\n", "     9    7    44  16582.839    0.639     0\n", "    10    4     2      0.000    0.000     3\n", "    11    2     2      0.000    0.000     1\n", "    12    1    35  16294.942    0.702     0\n", "Total extinctions: 0\n", "Generation time: 10.086 sec (10.077 average)\n", "\n", " ****** Running generation 43 ****** \n", "\n", "Population's average fitness: 11018.60974 stdev: 6962.61490\n", "Best fitness: 16603.41743 - size: (5, 4) - species 9 - id 6125\n", "Average adjusted fitness: 0.396\n", "Mean genetic distance 2.132, standard deviation 0.549\n", "Population of 151 members in 5 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     2   40    70  16432.506    0.800    13\n", "     9    8    36  16603.417    0.524     0\n", "    10    5     2      0.000    0.000     4\n", "    11    3     2      0.000    0.000     2\n", "    12    2    41  16294.942    0.658     1\n", "Total extinctions: 0\n", "Generation time: 9.821 sec (10.048 average)\n", "\n", " ****** Running generation 44 ****** \n", "\n", "Population's average fitness: 10002.42179 stdev: 7301.17818\n", "Best fitness: 16603.41743 - size: (5, 4) - species 9 - id 6125\n", "Average adjusted fitness: 0.354\n", "Mean genetic distance 2.167, standard deviation 0.562\n", "Population of 150 members in 5 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     2   41    69  16432.506    0.698    14\n", "     9    9    31  16603.417    0.376     1\n", "    10    6     2      0.000    0.000     5\n", "    11    4     2      0.000    0.000     3\n", "    12    3    46  16325.638    0.697     0\n", "Total extinctions: 0\n", "Generation time: 9.775 sec (10.027 average)\n", "Saving checkpoint to neat-checkpoint-44\n", "\n", " ****** Running generation 45 ****** \n", "\n", "Population's average fitness: 11754.99102 stdev: 6676.37008\n", "Best fitness: 20082.30965 - size: (6, 6) - species 9 - id 6394\n", "Average adjusted fitness: 0.353\n", "Mean genetic distance 2.217, standard deviation 0.517\n", "Population of 150 members in 8 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     2   42    55  16432.506    0.626    15\n", "     9   10    32  20082.310    0.518     0\n", "    10    7     2      0.000    0.000     6\n", "    11    5     2      0.000    0.000     4\n", "    12    4    39  16325.638    0.621     1\n", "    13    0     9         --       --     0\n", "    14    0     8         --       --     0\n", "    15    0     3         --       --     0\n", "Total extinctions: 0\n", "Generation time: 9.786 sec (9.980 average)\n", "\n", " ****** Running generation 46 ****** \n", "\n", "Population's average fitness: 11243.96618 stdev: 6849.63878\n", "Best fitness: 20082.30965 - size: (6, 6) - species 9 - id 6394\n", "Average adjusted fitness: 0.389\n", "Mean genetic distance 2.290, standard deviation 0.551\n", "Population of 151 members in 10 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     2   43    45  16432.506    0.620    16\n", "     9   11    23  20082.310    0.520     1\n", "    10    8     1      0.000    0.000     7\n", "    11    6     2      0.000    0.000     5\n", "    12    5    23  16324.894    0.638     2\n", "    13    1    12  16304.559    0.180     0\n", "    14    1    27  16379.986    0.653     0\n", "    15    1    16  16295.644    0.503     0\n", "    16    0     1         --       --     0\n", "    17    0     1         --       --     0\n", "Total extinctions: 0\n", "Generation time: 9.749 sec (9.937 average)\n", "\n", " ****** Running generation 47 ****** \n", "\n", "Population's average fitness: 12343.52880 stdev: 6108.95463\n", "Best fitness: 20082.30965 - size: (6, 6) - species 9 - id 6394\n", "Average adjusted fitness: 0.514\n", "Mean genetic distance 2.383, standard deviation 0.589\n", "Population of 151 members in 11 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     2   44    38  16432.506    0.649    17\n", "     9   12    10  20082.310    0.496     2\n", "    10    9     2      0.000    0.000     8\n", "    11    7     2      0.000    0.000     6\n", "    12    6     7  16324.894    0.585     3\n", "    13    2    26  16312.655    0.596     0\n", "    14    2    20  16379.986    0.675     1\n", "    15    2    18  16361.972    0.747     0\n", "    16    1    11  13973.947    0.696     0\n", "    17    1    10  13991.452    0.697     0\n", "    18    0     7         --       --     0\n", "Total extinctions: 0\n", "Generation time: 9.920 sec (9.897 average)\n", "\n", " ****** Running generation 48 ****** \n", "\n", "Population's average fitness: 11728.54801 stdev: 6640.91551\n", "Best fitness: 20082.30965 - size: (6, 6) - species 18 - id 6394\n", "Average adjusted fitness: 0.505\n", "Mean genetic distance 2.461, standard deviation 0.554\n", "Population of 151 members in 15 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     2   45    26  16432.506    0.603    18\n", "     9   13    16  16344.940    0.615     3\n", "    10   10     6  13991.452    0.348     0\n", "    11    8     2      0.000    0.000     7\n", "    12    7     3  16339.601    0.656     0\n", "    13    3    14  16324.894    0.664     0\n", "    14    3    22  16379.986    0.546     2\n", "    15    3     9  16361.972    0.664     1\n", "    16    2    11  14013.652    0.443     0\n", "    17    2    13  13991.452    0.557     1\n", "    18    1    10  20082.310    0.462     0\n", "    19    0     4         --       --     0\n", "    20    0     3         --       --     0\n", "    21    0     7         --       --     0\n", "    22    0     5         --       --     0\n", "Total extinctions: 0\n", "Generation time: 10.161 sec (9.899 average)\n", "\n", " ****** Running generation 49 ****** \n", "\n", "Population's average fitness: 11315.39018 stdev: 6864.75579\n", "Best fitness: 20082.30965 - size: (6, 6) - species 18 - id 6394\n", "Average adjusted fitness: 0.484\n", "Mean genetic distance 2.408, standard deviation 0.571\n", "Population of 151 members in 15 species:\n", "   ID   age  size   fitness   adj fit  stag\n", "  ====  ===  ====  =========  =======  ====\n", "     2   46    25  16432.506    0.662    19\n", "     9   14     6  16344.940    0.738     4\n", "    10   11     5  13991.452    0.232     1\n", "    11    9     2      0.000    0.000     8\n", "    12    8     7  16205.438    0.533     1\n", "    13    4    11  16324.894    0.603     0\n", "    14    4     8  16379.986    0.673     3\n", "    15    4    13  16364.112    0.526     0\n", "    16    3    11  14062.348    0.634     0\n", "    17    3    10  13991.452    0.322     2\n", "    18    2     8  20082.310    0.253     1\n", "    19    1     7  16337.901    0.581     0\n", "    20    1     6      0.000    0.000     0\n", "    21    1    14  16361.972    0.773     0\n", "    22    1    18  16346.974    0.734     0\n", "Total extinctions: 0\n", "Generation time: 10.201 sec (9.942 average)\n", "Saving checkpoint to neat-checkpoint-49\n", "\n", "Best genome:\n", "Key: 6394\n", "Fitness: 20082.309648003004\n", "Nodes:\n", "\t0 DefaultNodeGene(key=0, bias=-1.3828553883439687, response=1.0, activation=sigmoid, aggregation=sum)\n", "\t1 DefaultNodeGene(key=1, bias=3.0007609442709082, response=1.0, activation=sigmoid, aggregation=sum)\n", "\t959 DefaultNodeGene(key=959, bias=0.4390736655689382, response=1.0, activation=sigmoid, aggregation=sum)\n", "\t981 DefaultNodeGene(key=981, bias=-1.352245851784095, response=1.0, activation=sigmoid, aggregation=sum)\n", "\t1220 DefaultNodeGene(key=1220, bias=-2.2166978194100357, response=1.0, activation=sigmoid, aggregation=sum)\n", "\t1275 DefaultNodeGene(key=1275, bias=-0.8673223026649377, response=1.0, activation=sigmoid, aggregation=sum)\n", "Connections:\n", "\tDefaultConnectionGene(key=(-4, 959), weight=2.225739648351321, enabled=False)\n", "\tDefaultConnectionGene(key=(-4, 1220), weight=-0.19583359085931185, enabled=True)\n", "\tDefaultConnectionGene(key=(-1, 0), weight=1.7565967199084334, enabled=False)\n", "\tDefaultConnectionGene(key=(-1, 959), weight=-0.06707368861411556, enabled=True)\n", "\tDefaultConnectionGene(key=(-1, 1275), weight=0.7585522046031055, enabled=True)\n", "\tDefaultConnectionGene(key=(1220, 0), weight=-0.8257643823180626, enabled=True)\n", "\tDefaultConnectionGene(key=(1220, 959), weight=0.08516548117547829, enabled=True)\n", "\tDefaultConnectionGene(key=(1275, 0), weight=1.5087146861386134, enabled=True)\n", "\n", "Output:\n", "The winning network achieved a fitness of 20082.309648003004\n"]}], "source": ["ai.train(target_time, dd.cashGen, parameters, num_generations = 50, increment_value = 0.25, log = False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interpreting the AI's Results\n", "\n", "Once we've determined a winning genome, it's time to see how it behaves! The `simulate` method tells the AI to automatically run a simulation given the previously defined game state until the designated target time. When the AI is finished, you can analyze its decision making by graphing the simulation results and also viewing its decision history."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Time</th>\n", "      <th>Type</th>\n", "      <th>Message</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>284.8</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Zero</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>284.8</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Zero</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>285.0</td>\n", "      <td>Round</td>\n", "      <td>Round 14 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>290.8</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Grouped Pinks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>297.0</td>\n", "      <td>Round</td>\n", "      <td>Round 15 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>313.4</td>\n", "      <td>Round</td>\n", "      <td>Round 16 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>346.4</td>\n", "      <td>Round</td>\n", "      <td>Round 17 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>385.7</td>\n", "      <td>Round</td>\n", "      <td>Round 18 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>411.8</td>\n", "      <td>Round</td>\n", "      <td>Round 19 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>448.2</td>\n", "      <td>Round</td>\n", "      <td>Round 20 start</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Time   Type                      Message\n", "0  284.8    Eco           Change eco to Zero\n", "1  284.8    Eco           Change eco to Zero\n", "2  285.0  Round               Round 14 start\n", "3  290.8    Eco  Change eco to Grouped Pinks\n", "4  297.0  Round               Round 15 start\n", "5  313.4  Round               Round 16 start\n", "6  346.4  Round               Round 17 start\n", "7  385.7  Round               Round 18 start\n", "8  411.8  Round               Round 19 start\n", "9  448.2  Round               Round 20 start"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue</th>\n", "      <th>Expenses</th>\n", "      <th>Profit</th>\n", "      <th>Eco Impact</th>\n", "      <th>Start Time</th>\n", "      <th>End Time</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Farm Index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>4850.0</td>\n", "      <td>0</td>\n", "      <td>4850.0</td>\n", "      <td>20965.0</td>\n", "      <td>447.0</td>\n", "      <td>448.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4850.0</td>\n", "      <td>0</td>\n", "      <td>4850.0</td>\n", "      <td>20965.0</td>\n", "      <td>447.0</td>\n", "      <td>448.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            Revenue  Expenses  Profit  Eco Impact  Start Time  End Time\n", "Farm Index                                                             \n", "0            4850.0         0  4850.0     20965.0       447.0     448.0\n", "1            4850.0         0  4850.0     20965.0       447.0     448.0"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["24520.800000000367"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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********************************/Hd8t3oQ7gaA8QsLiaNYn+9n+WxYfPdzFw9olISock9VGToHZw+roYq6JhCILooSocPae8vTrdKRsdS9TPI3rhdxCosPDSlgbgRQp/4knnnDunz59umoEFyta0ywd3YHkG2t4vOXIQOePP79AIBAIBBVKxkzYtaTk8U4ToeuUkscFsvPzySuMX7mbpwfepFh2Wvdn4kok8FCVYxfkCI5tt4PFZndaIMlNhCPGkUhOIqiEzJgxg5EjR7JixQoeeeQRXnzxRQYOHEiXLl0YP348BQUFLF68mJo1a5KWluY8b9SoUTz11FMMGzaMqVOnkp+fz7Jly0hKSvIa5NkX0tPT2bJlC926dWPy5MlYLBYWL15Mq1atfIohlJOTw+LFiwH45ptvAFiyZAkxMTHExMR4BDJ/+eWX6devH7fffjsPP/wwOTk5LFy4kKSkJCZNmlRmO8nJyfTs2ZOOHTsSGxvL7t27Wbt2rUf9DqumqVOn0r9/fzQaDaNGjWLw4MEsXLiQAQMGMHr0aLKysli6dCnNmjXzOU6SwWAgOTmZDz/8kKSkJGJjY0lJSSElJcWn8wXVm+tqcWS325n1yX4yDpzj/Ym30iDW8wlLSv2aaDUqvj3qUnSPXcjldHYBHRrVAqB9o1ocPnfVQxjaceQi0eFhNK8TVVQmhm+Peiq8O49cpH1RHcUJDw+nRo0azld0dLQs473eOJ6Gqb2sf8KKDiqdelYgEAgEAr+x2WDNGE/RKKGl9Lp1Mgx68fr1rZrx149+4XKeiRlrfQ/oGgwOq5x/7wrMjcUdXx+NOWIcARzNyvWrDX88QRwZ44wiq5qgEjJ8+HCaNm3KggULsFqt9O3bly1bthAXF8esWbNYsGABt956K998841HoOa4uDjWr19PREQETz75JCtXrmTevHncddddAfelTZs2ZGRkkJCQwKxZs3jnnXdIT09n2LBhPp1/5coVnn32WZ599lm2bNkCwEsvvcSzzz7LggULPMr26tWLLVu2UKtWLZ555hkWL17M0KFD+frrr8uN9zR16lQyMzOZN28eU6dO5euvv2bu3Lm89NJLzjLDhw/nscceY8uWLTz44IPcf//9APTu3Zu3336bc+fOMW3aND744APmz5/v8xgdLF++nHr16jF9+nTuv/9+1q5d69f5gurLdbU4evaT/Xyy9wxv/d/NRIZryLpmBKCGXoteq6GGXsu9Nzdg7qeHqBmhJTpcy+yN++nQMIYODSXRp3vzBJrXjmb6h3t5emBLLuQW8tLWwzzYpRHhYdIN+YFbGvHvb08wb/MhRt7cgF3HLvLpvrO8k9rpuo3dV7Kz4aabjGRnj6JPH/j44zXo9fqA6rIVrZjcFzVGI4waBT+daIG6z/dYbPIKR0aj0ZkWdM2awPteGanOYxdIiDkgUJpqMcfMRninP5zdK+3HNoVJ34I2NMda1a+JxtvTJ5nw5hXWoJbroWK+yUKETvmla3iYSzj6965M5g1vU/5JARhsO6zeD5+/xp8Xc5gy4S9A1Zw3gspJamoqqampXt9Tq9UcPXrU41ifPn3o06dPufX269ePffv2lTjubpkEpbvFOoKDu9O9e3d2795dbp3eaNy4sV8uuH379qVv374+l3cwc+ZMZs6cWWYZjUbDokWLWLRoUYn3xo0bx7hx40oc9/VzA+jSpYvXz0kgKI/rKhyt+k7yQx315ncex1+8p43T/PnZO5NRqw4xadUeTBYb3ZPieW6oy5xOo1bxdurN/GPDfoYv+4YIXRgjOtTjiX5JzjINYiN4J7UTz206yLvfZHJDTT3/HN6aHkkJFTDK4DCZ4Px5K/AJmzeXnnHAF1xfIq5Fn9UKn3wCEEuDXirZXdWsViufSA1Uu/Sy1XnsAgkxBwRKU+XnmPEqLO4AeRek/drJ8MhOUHvPshMKVPVr4rCSURL3AK6DW9flsQ9+BqS09XIIR+VZBYVp1AxuXZdP950lX0FroGa1XdYJv5y8VKXnjUAgEAgqN9dVOMr85+Byy+i1Gp4bmuIhFhWnfq0IVoztXGY9XZrGsfnxbn738XoTFQVjxug4duxNHngAr1kYfMUhCbk/LNTp4M03pSdqJzU2LDILRzqdjjfffNO5XZ2ozmMXSIg5IFCaKj3HLv8Bi9q59rtMgf7PX7fu+EqVvibgNTakkqjVKgxaDQVma9ABsv2J6d21WRyf7jvrd5vlxU9yp6ZBS/uGMfx8MhuLPazyzZvMb+DbRXBmL+Seg/tWQ8s7PctcOAzbZsOJb8BmgYQWcO97EFMUH8tshK0zYf/HYDFBs94weCFE1XbVkX0KPn0C/tgBukhodz/0SQNNSIRqFQgEgmqB+MYNcSIiYNUqLTAx6LrsTlc116JGq4WJE+HniEuc2meX3VVNq9UycWLwfa+MVOexCyTEHBAoTZWdY+cPwLKurv075kLXx65ff/ygyl6TIgwKCkel6ToGXZFwVIFBpJ3Z3HxsM9DHbjX0UvIXM+rKN2/M+VAnBdo/AB8+UPL9y8clN9P2D0KvpyE8GrJ+gzA3N7yMp+H3rTByJehrwOYZUl3jt0rv26zw/r2SkDR+K+Seh/UPg1oLfWdXzDgFAoFAIISj6kRJRzUXYWrJnz/raqGXdwUCgUAgqCAOrIf/pLr2H1gHzcqPmSGoGPRa5fOqFF+nODOryeQ25ksAa7nbLA2nQGWyKNqOIjTvJ71K44vnoPkdcMdzrmOxTVzbxhzY8x6MWA5NekjH7n4NlnaCUz9Cg05wbDtc+A3+7xOXFVKvmfB5GvR8GsIqiXWWQCAQVHKua1Y1QflYLLB+vY3Fiw/wyy8HsAVhEeSIceS+YLLZ4MABuHAqHLsdfsy8HGyXPbDZbBw4cIADB4Lre2WkOo9dICHmgEBpqtwc+3mVp2g0dkulE42q3DUphqIWR6WY7Rj8tP4powWfSzpc8pS2cnJ8nnmF5qo1b2w2OLIV4prBe8PgX03hrd5waJOrzJm9YDNDk56uYwlJULMB/PmDtH/qB6jdytN1rVkfKLwKFw5VxEgEAoFAgLA4CnkuX4bhwwsAKcZTbm4ukZGRAdXlWJCp3ZSjggJISQFIpsH0k0QXmUzLRUFBASkpwfe9MlKdxy6QEHNAoDRVZo7Z7bD1H7BribSvUsO0fVCz/vXtVwBUmWtSChUTHLtYmxVk/eOOIwi3z65qXh7O+YK+6PO8ei2flF7tgOs/b65du8bVq1ed++Hh4YSHh/tXSd4FMOXCzpeh9z+gbzoc/VxyQ0vdBI1vh9ws0OjAEON5bmSC5JIG0t+oYslsIotEpNws//okEAgEgoARwlGlIZ64uOBqsDuetBVb1MTHg8kiLYyMCjxZi4+Pl73OykJ1HrtAQswBgdJUiTn207su0UgdBn87AhGx17dPQVAlrkkpuAfHttnsqNV+KiUBYJDZ+seXANaONo1Ku6o5xmaxhMy8SU5O9tifPXu2TynNPbAXWU21GARdHpW267aRLIh2vyMJRwKBQCCoNAjhKMSpXRvs9kjgQtB12b3oRpGRcOECZBy4wMPvWcmX2cc+MjKSCxeC73tlpDqPXSAh5oBAaarEHDv4CWyaLm3XbgUPfw0aea1fK5IqcU3KIFLnWjp+tv8cg9vUla1ueymuZA6rnPwKzKpm0EnRHPKVdlUrGptNHR4y8+bgwYPUq1fPue+3tRFARJwkAifc5Hk8IQlOfidtR9UGqwkKsj2tjvIuQFSdojJ14PQezzryslznCwQCgaBCEDGOqhHeXNUcuJ7mVQG/eoFAIBBUDs7+Ch/9n7Sti4aH/1epRaNQ4fiFXD7+6U9stkBzfZVOtN4lHJ3JLpC9fgnPdYrDKmfh1sMKtVcSQ5FAlp1v5uSl/HLLB/pJOyy4fj+fG2AN8hMdHU2NGjWcr4CEozAd3NgBLh3xPH7pmBTDCODGdlJ2tD++dr1/8QjknIL6naX9Bp0h6wDkuolqx76E8BolRSmBQCAQKIawOKom7DxykYNnJX91b/73jideSriqCQQCgUBQAlM+rL7Htf/I/0AjliVy0Psl6Ye41Wbn3k4NZK3bXSAJ1gLIV+rUkIQLueIr+RKHKCHKJZZ8c+wiDeMaytJ2cSKLxrTr+CVMFhu6sEr0TLcwFy4fd+1nn5DEYEMtiGkAt02F/4yFRl2hcTc4+gUc/gxSP5XK62tChwchY6Z0Tng0bH5SEo0adJLKNO0tCUTrH4J+c6SYR9vnQqcJEBaAoCUQCASCgKhEd6fqSXY2NGpkJDJyDCNHjsFoNPpdR16hhbErfnDuR7gtvIxGGDMG0qZHYbeoZQ88aTQaGTNmDGPGBNb3ykx1HrtAQswBgdJU2jlms8Lrt7kC4I7/3DNNdyUmlK7JruOXFK1f7oxjpbmSDe8gBUk3BmkV7Y9VkC5MTb9kyV1KSYGsf8oNANgtJkaPGR0S88ZnzvwMb3STXgAZz0jbX74g7be8C+58Gb55FZZ1hT3/hvveg0ZdXHX0nwdJ/eHDB+HdQZL72X2rXO+rNTD6Q1BpYHk/WPcQtB0FvWZW3DgFAoFAICyOQh2TCU6etALvs3YtrFjxpt91GM1WzFZpufS3O5Jo16CW8z2rFd5/H0BHg+kq2WMcWa1W3pca4M03/e97ZaY6j10gIeaAQGkq5Ryz2+H9e12WCiNXuqwLqgChdE2UzkKmlJVyiaxqOnmDY/tKbIQO8G2czjiSfqZVq1vTgF6rJt9k4+OPPgSu/7zxmcRukJZTdpkOD0qv0tDqYfBL0qs0YhrCA2sD66NAIBAIZEEIRyFOVBQMGaLj+PGXefBB0Ol0QdU3pXdzj32dDl5+GXIKTLxz2YbRLG92FJ1Ox8svv+zcrk5U57ELJMQcEChNpZxj22ZJabkBBsyHVkOva3fkJpSuiRJCi7tVkNLClANHHEa5Hm75utJxClYKj9Og1VCgCeOZOfNIiNZf93kjEFQV0tLSSE9Px+5PZHyBQOAVIRyFOBER8MknWmCaIvVrtTBtGlzJg3efs2Oy2jmdXUC9GINM9WuZNm2aLHVVNqrz2AUSYg4IlKbSzbHDW+DbRdL2TXfCLQ9f3/4oQChdE6XjFsqdcay033auOIw2bDY7anVgD7n8/fGo11aMpVOELowrmjDuTX2Etg1iFG1LIPCHFStWMHbsWOe+RqOhTp069OvXj+eff94j+15l5vDhw7z++ut8//337Nmzh8LCQv744w8aN27sUe6rr76iV69epdYzd+5cZs4Mzo1y8+bN/PDDD6SlpQVVjzfy8/P517/+Rc+ePenZs6fs9QuqNkI4EgAQ5ZYl5bN9Z5nQrWrEmhAIBAJBiHDqR/jgPmk7rhmMWO5blGJBwCgteChliVN8VjgsjgAKLTbZgmSXhyMmpC8xjhySVCAzWq+VQo5WtCueQOArc+bMITExEaPRyHfffceKFSvYuXMn+/fvR6/XX+/uBc2uXbtYtGgRycnJtGzZkr1793ot17JlS957770Sx9977z22bt3KHXfcEXRfNm/ezNKlSxUTjtLT0wGEcCTwGyEchTgWC3z7rY3z50/Svj00adIQtVq+mOY2G5w8CaCmS2Icu/64JOtC0GazcVJqgIYN5e17qFOdxy6QEHNAoDSVZo5dyYR3+kvbYXqY+CVo5bFsDTVC6ZooIezY3UJMV1QmVr2bcFRgtgYtHPmqVzoEK6XHadBpsNtt/PHHH9ygvnbd541AUJyBAwdy8803AzBhwgTi4+OZP38+Gzdu5N57773OvQueIUOGkJ2dTXR0NAsWLChVOKpTpw4PPPBAiePp6ek0b96cTp1CM16fzWbDZDJd724IKjnirhTiXL4MPXoUcO+9iTRvnkhBQYGs9RcUQGKi9GoSW0M6JuMCqaCggMTERBIT5e97qFOdxy6QEHNAoDSVYo5ZCuG9YWC3gkYHj+0BfY3r3SvFCKVrYrQobHEkt6taKXnPNGqVM019MG36G+VEX4ExjuxmE6P7dgqJeSMQlEe3blImvWPHjnkc3759O926dSMyMpKYmBjuvvtuDh065FEmNTW1hAsYSPGIigeXV6lUTJkyhQ0bNpCSkkJ4eDitWrViy5YtJc7fuXMnnTp1Qq/X07RpU9544w2fxxMbG0t0dLTP5d354YcfOHr0KGPGjCm3rNlsdopMer2euLg4br/9drZt2wZIn83SpUsBaeyOl4MFCxbQtWtX4uLiMBgMdOzYkbVrSwaOd3xuq1evplWrVoSHh/P666+TkJAASEKXo24lLJsEVRNhcVRpiMAQ4MPZ8hZKERHSX4NCvvwRjgaqIdV57AIJMQcEShPScyz/MizuAAVXpP1734OaVSMmRlmEyjUpMAWXvr48lEpT780iKEKnwWSxcf6qUbY4jOURUbQuOn4xt9yyjvhJgXhfOiyqwvUGNAHGbxIIKpLMzEwAatVyZWr+/PPPGThwIE2aNCEtLY2CggIWL17Mbbfdxp49e7yKRb6wc+dO1q1bx+TJk4mOjmbRokWMGDGCkydPEhcXB8C+ffu44447SEhIIC0tDYvFwuzZs6lTp06wQy2X1atXA/gkHKWlpTFv3jwmTJhA586duXr1Krt372bPnj3069ePhx9+mDNnzrBt2zavLnGvvvoqQ4YMYcyYMZhMJtasWcPIkSPZtGkTgwcP9ii7fft2PvroI6ZMmUJ8fDxt27Zl2bJlTJo0iWHDhjF8+HAA2rRpI8OnIKgOCOEoxKldG+z2SCAv4DosVmkx420xEhkJeUVVL/5Ceponp0l2ZGQkeXmB970yU53HLpAQc0CgNCE3xwpzoeCytF1wBd7o7npv4L+gxYDr068KJJSuycXcQtnrdI8vXVGuauCKHbTmh5N0aFirzLK+11Y2jhhHv5/PJTvfREyEMtnOInQa1Do9E97eyZLRHRRpQ3B9cHwXREREOC1HTCYTZrOZsLAwwsPDS5Q1GAxOV0Wz2YzJZEKj0XjEEvKnbH5+ftBidk5ODhcvXsRoNPL999+Tnp5OeHg4d955p7PMjBkziI2NZdeuXcTGxgIwdOhQ2rdvz+zZs1m5cmVAbR86dIiDBw/StGlTAHr16kXbtm354IMPmDJlCgCzZs3CbrezY8cOGjZsCMCIESNo3bp1MMMuF6vVyocffkjnzp1p1qxZueU//fRTBg0axJtvvun1/S5dupCUlMS2bdu8usT9/vvvGNwsCaZMmUKHDh1YuHBhCeHo8OHD7Nu3j+TkZOexFi1aMGnSJNq0aeO1foGgLISrWjXAYUEUoS07JoDBjyCQAoFAIBB4cHAjzKsHr7SWXu6i0eCFVTKDWmVACfHIgdwuXGUlPbvpBsm90WILPK22vxm5uzaNd26fyTYG3K6v/HwyW/E2BBVLVFQUUVFRXLx40XnsxRdfJCoqyil6OKhduzZRUVHOGGkAS5cuJSoqivHjx3uUbdy4MVFRUR5uYCtWrCAqKopRo0Z5lHUXDgKlb9++JCQk0KBBA+655x4iIyPZuHEj9evXB+Ds2bPs3buX1NRUp2gEkjVLv3792Lx5c1BtO0QjR501atTg+PHjgCTeZGRkMHToUKdoBFIg6/79+wfcri988cUXnD9/3idrI4CYmBgOHDjAkSNHAmrPXTS6cuUKOTk5dOvWjT179pQo26NHD1muvUDgQAhH1QDHwk5fTjBJQwX58gsEAoGgirH3ffjoQdd+mEF6aSOh3xzoNL70cwWyk1LPFUNKyXt6vsmiSL0qLxZBA1vfAFSslVPNCC2N4iRLjQJz2WMNJqvafZ0aBHCWQFBxLF26lG3btrF27VoGDRrExYsXPaylTpw4AUgWLcVp2bIlFy9eDNgS010MclCrVi2uXJFcoC9cuEBBQQHNmzcvUc5bf+Rk9erVaDQa7rvvPp/Kz5kzh+zsbJKSkmjdujUzZszg119/9bm9TZs2ceutt6LX64mNjSUhIYFly5aRk5NTomxiYqLP9QoEviCEoxDn6lVISiokJmYif/nLRAoL/X9y6LA4MnixOCoshIkTpVeYXf4YR4WFhUycOJGJEwPre2WmOo9dICHmgEBprvscs9vhs7/DhknSvkoD0/bDP85Jr5ln4LbHK75f15Hrfk2KoWSKd6NZ2RhK7jjWMHJYRfsTh8gZ/1HBeFH1YiKwW8wcXrsgZOaNQB5yc3PJzc0lPt5lvTZjxgxyc3NZsmSJR9msrCxyc3M9hJJHH32U3Nxc3n77bY+ymZmZ5Obm0rJlS+ex1NRUcnNzWbNmjUfZgwcPBj2Ozp0707dvX0aMGMHGjRtJSUlh9OjR5OaWH/+rOMUDYDuwWr3/b2s03h982/01IZSZgoIC1q9fT9++fX2OpdS9e3eOHTvGO++8Q0pKCsuXL6dDhw4sX7683HN37NjBkCFD0Ov1vPbaa2zevJlt27YxevRor5+FIdDguAJBKQjhKMQxGuHIEQs5Ocv597+XY7H4/3TPWIZwZLHA8uXSS6cK8ygvBxaLheXLl7N8eWB9r8xU57ELJMQcECjNdZ1jdjusHQffL5P2dVEw4yjEVG/riVD7v1fS/dxktWGxVox4JIdVdCA/NPUKJQ5xJ0KnwW6zcumnz0Jm3gjkITIyksjISA+xRKfTERkZ6WGx417WEbMIQKvVEhkZ6RGzyN+ycgfr12g0zJs3jzNnzjjFr0aNGgFSXJ3i/Pbbb8THxxMZGQlI1kLZ2dklyjmslvwlISEBg8Hg1f3LW3/kYuPGjVy7ds1nNzUHsbGxjB07lg8++IBTp07Rpk0bj8xmpQlrH3/8MXq9noyMDMaNG8fAgQPp27evX22XVrdA4AsiOHaIExEBvXtrOXFiLmPGSDcFfynLVU2rhblzpe2oCOnmI+ciU6vVMreogUD6XpmpzmMXSIg5IFCa6zbHzEZY3hfO75P267aFCV+ARszzUPi/d9dH5I9D5Cm+GC02ojTyPof09tvG8fCrIl3VwBUg21e3vEB+mOm1GlQaDTHdHiQhOlzcLwQhT8+ePencuTOvvPIK06ZNo27durRr146VK1fy9NNPExMTA8D+/fvZunWrRyDmpk2bkpOTw6+//urM6HX27FnWr18fUF80Gg39+/dnw4YNnDx50mmxdejQITIyMoIbaBm8//77REREMGzYMJ/PuXTpkjMTHEgxsJo1a8apU6ecxxwCW3Z2tvNzBGmcKpXKwzIrMzOTDRs2+Ny+Q0T0JtwJBOUhhKMQJyoKvvhCB8wM6PzcQgvzPpMC5xm0JRd2Oh3MLKr6++PyP1XT6XTMnBlY3ys71XnsAgkxBwRKc13mWEE2vHYrXDsr7Te/A+5fA+qy4+hVF0Lt/15poSXfZCEqXPnlpEEGy59A4hD5LFgF4TVj0GlQabTU7HofDRIi0emUyd4mEMjJjBkzGDlyJCtWrOCRRx7hxRdfZODAgXTp0oXx48dTUFDA4sWLqVmzpodFzahRo3jqqacYNmwYU6dOJT8/n2XLlpGUlOQ1yLMvpKens2XLFrp168bkyZOxWCwsXryYVq1a+RRDKCcnh8WLFwPwzTffALBkyRJiYmKIiYkpEcj88uXLfPbZZ4wYMYKoqCif+5mcnEzPnj3p2LEjsbGx7N69m7Vr13rU37FjRwCmTp1K//790Wg0jBo1isGDB7Nw4UIGDBjA6NGjycrKYunSpTRr1sznOEkGg4Hk5GQ+/PBDkpKSiI2NJSUlhZSUFJ/HIKi+COGoivPZvrMcuyAFo4uLCi+zrMMM3CiCYwsEAoHAG3Y7rHvIJRrdPh36pl3XLglK4mFxpLBwZJQx9k9ZnmQOq2mlx1NquwqujfRhrgd7FRk3SiAIhuHDh9O0aVMWLJBic/Xt25ctW7Ywe/ZsZs2ahVarpUePHsyfP98jUHNcXBzr16/niSee4MknnyQxMZF58+Zx5MiRgIWjNm3akJGRwRNPPMGsWbOoX78+6enpnD171idR5cqVKzz77LMex1566SVAcsMrLhz95z//wWw2M3r0aL/6OXXqVDZu3MjWrVspLCykUaNGzJ07lxkzZjjLDB8+nMcee4w1a9awatUq7HY7o0aNonfv3rz99tv885//ZNq0aSQmJjJ//nwyMzP9CrC9fPlyHnvsMaZPn47JZGL27NlCOBL4hBCOQhybDX77zc6VKxdp1gxq1473ywzafYH19wE3lXjfbgdHhlB9WJE5toyLMrvd7kxBGh/vX98rO9V57AIJMQcESlPhc+zj8XCkyPS/33Nw21Rl26uEhNr/vdwxjoqLO5v3n+WRHk29Fw4Qb59YxHXK/Bqh9W9tFMjlDtOosdvt2AqucpV87Hb7dZ83AgFIAbdTU1O9vqdWqzl69KjHsT59+tCnT59y6+3Xrx/79u0rcdzdMglKj0uWmZlZ4lj37t3ZvXt3uXV6o3Hjxn7FQHv44Yd5+OGHfS7vYObMmeVapGo0GhYtWsSiRYtKvDdu3DjGjRtX4rivnxtAly5dvH5OAkF5COEoxLl4EVq1ygdqA1J2Bofvqz8Mbl2XBrElg+Pl50NtqWoOn5J/UZafn0/t2sH1vbJSnccukBBzQKA0FTrHfngL9n8sbTfsKkSjUvD1muw9lU20PoymCb67OQSC0hY6mRcDS7HtDXsZ/l4Ol7GLuSau5JmoFRmAO1dR9f6IMg5r7M8Pnmdyz2ZlVB1chie7uZA/F4/hTyD/WXG/EAgEAkFoIbKqCZw4MocUWmzYbNc3xaVAIBAIQogj22Dz36Tt6Bvh/z65vv2p5GRdNTJ06Tf0eelrRep3v4Mr7X6uhDDlTdhxd7ff/luW7G2WRniRG9nPp7IrrE2BQCAQCEINYXEU4tSuDXZ7JEFFXSyDyEiX2Xm+yRXY1GixEqELfnpERkYGlP62KlCdxy6QEHNAoDQVMseunYcPizLixDSEyd9DmAjeWxq+XJPT2QXObaXdkmR3VSu2X1GuY1HhYbSoE83h89fILay4dPXDO9TnrR1/EKZW1nVMrdPT6KlNAMLaSCAQCAQhh7A4EjhxxDgC+ReaAoFAIKiEGHPglRSwGKX9BzeArqTbc1UkO9/Eoi+OcOpyvux1O9yfQLLyVRKlXNUcrmNbD55ny/5zstRZngaaUq8mEPiYAsmqVremHgCz1Y7ZWvq1cvQ9UHlpaLsbAzxTIBAIBALlEcKRwIlarUKvlaZERQefFAgEAkGIYS6At+8Aq0naH78N4uQNghzKzNywn4XbfueuJTtlr9v9QY0S91t3i6dy08gHiLv49ciqn8i6alSkHc82pTXKkfO5ZOebFG8PXG78oNxnCXB3u3oA1IrQKtaGQCAQCASBIoSjEOfqVWjbtpCEhGlMnjyNwsJCv84v7+ldYSFMmya9CgtdTxAv5cmzICssLGTatGlMm+Z/3ys71XnsAgkxBwRKo9gcs9thw2S48Ju0f/8aaNBZvvorAT9lXgEgO9/s13m+XBONm9uTnJlMvSG3MOVYVxjcBBWQb91QFo42P97zJ+3mbFO8PZBiHDk8CZV8qNY1sSa3nP8vnc/9V9wvBAKBQBByCOEoxDEa4ddfLVy8+CrLlr2KxeKfX7+taIWnLsU332KBV1+VXhaLazG7+rsTwXXcWb+FV199lVdf9b/vlZ3qPHaBhJgDAqVRbI59/wYcWCdt3zoZWgyUr+5KgsMC1198uSbuD3WUtvBVSpiK0HkKR3K4xJUXrcsQZOxFhyWWPyGlVCoVEUWCVVljdF7TAONVWSwWPlrxBm8uWyLuFwKBQCAIOURw7BAnIgJuvVXLqVPPcP/9oNX6Z8JsLcqOVlpQR60WnnnGtZ0YH8nFXBMWmbKqabVanilqwN++V3aq89gFEmIOCJRGkTl28BPY8pS03aQn9H9BnnorGfpiFjW+4u81UdL9CZQTpgzFhSMZ2ylNeylu5aR0YHFnuzoNeSarYvGiQNwvBAKBQBDaCOEoxImKgl27dMDzAZ3vEIA0pQhHOh0871b1kHb1+DHzimwLQJ1Ox/PPB9b3yk51HrtAQswBgdLIPsfO/Awf/Z+0HdccHlgXsAVFZae4MOIr/l4TJcUIkF+YshfZBRUX1ioiNqKhmBVYocUWsMDnD442lEwcIu4XAoFAIAhlhKtaFcdSlAHE1zSyBh/MsQUCgUBQBTFehTd7StvhNWDidlAr/6M8VClu3SIndjenLCXECA9XOIXu51Hhns8eZXFVKycwY0QxVzV/PztXVjX/xFCHW56xjPYc17R6yqwCgUAgqOoI4SjEsdng/Hk7f/yRx7VreeUuqopTnsWR3Q55edLLbncTjmRayNrtdvLy8sjL87/vlZ3qPHaBhJgDAqWRbY5ZzbDsNtf+fatAXyP4DlZiAhWO/L0mSlvq/HTiiiL1OtLUO6gIVzW9AnGVfMEVlPu0Ym2I+4VAIBAIQhkhHIU4Fy/CDTfk06RJFDVqRJGfn+/X+Y4YR1qN90udny+5w0VFSduOp2pyLcby8/OJiooiKsr/vld2qvPYBRJiDgiUJug5Zi6A/MvwzgDIOSkdG7IYmvSQt6OVkOIiha/4e02UjnEEcOjsVdnqcmgaKhV0alzLebwiRJz2DWI89ivCPQ7AaJast89dLVCsDXG/EAjkJy0trULioAkE1QEhHFVhvj9+icXbjwKlWxwVRy9c1QQCgaBqkncJflkDP6+WXp9MgedvgH8lwundUpk7X4YO/3d9+xki6MMUdFVzMyhRxFWtWH6y81eNsrehQsWL97R17ldEVrUGsREe+/4KR+6ilz883rc54BKQ5KxbIAhlVqxYgUqlcr7CwsKoV68eqampnD6tnAVeRXP48GGmT59O165d0ev1qFQqMjMzvZY1Go3MmzeP5ORkIiIiqFevHiNHjuTAgQOy9GXz5s2kpaXJUldx8vPzSUtL46uvvlKkfkHVRgTHDnHi4+HcuQjy83OJj4eIiIjyTypi0fYjzu2E6HCvZSIiIDfXte0IBirXU7yIiAhyixrwp+9Vgeo8doGEmAMCpfF5juX8CYvag9Xk/X2VBjr+BTqOVaCXlRODzvVs7cSlPBrFRfp0nr//9xXxoEZOyxx3cadxfCRjbmnI6u9P8s7OP3ikR1NZ2vA1BlFFPeSKLIrnpKSFk7hfCEKZOXPmkJiYiNFo5LvvvmPFihXs3LmT/fv3o9fry68gxNm1axeLFi0iOTmZli1bsnfv3lLLjhkzho0bNzJx4kQ6dOjAmTNnWLp0KV26dGHfvn00atQoqL5s3ryZpUuXKiIe5efnk56eDkDPnj1lr19QtRHCUYijVkOdOirAtwWrO2aLtLzr36oOf+nS2GsZlQoi3aqW21VNpVIRGel/36sC1XnsAgkxBwRKU+4cs9th0zT4aYXrWONuEFa00I+uA/1fAF1UpQyEbbfbOXfVSN2aBtnrjo10PXD53+8XeLCLb//Lvvzfu4svSrqqaTUqzFa7IgKLw7ImWi+ljq8otzF3fjmVTefEWMXbccQ4UvJaifuFIJQZOHAgN998MwATJkwgPj6e+fPns3HjRu69997r3LvgGTJkCNnZ2URHR7NgwYJShaPTp0+zbt06/va3v/Hiiy86j3fr1o3evXuzbt06pk+fXkG99h2bzYbJVMqDI4HAR4SrWjXg7nb1fE4rLHdwbIFAIBBcJ2xWeG+Yp2g0aAGkboIH1kqvu5eCvmalFI0Anlz7K13mbefVz4+UX9hPtG4u3kpatihxv3W4TVWE+/mYWxoCUGgt3Y3LV/yNCf35ofP+1V+uM5x3fMk4G2jGNoGgMtKtWzcAjh075nF8+/btdOvWjcjISGJiYrj77rs5dOiQR5nU1FQaN25cok5v8YhUKhVTpkxhw4YNpKSkEB4eTqtWrdiyZUuJ83fu3EmnTp3Q6/U0bdqUN954w+fxxMbGEh0dXW65a9euAVCnTh2P43Xr1gXAYCj7IYbZbCY9PZ3mzZuj1+uJi4vj9ttvZ9u2bYD02SxduhTAw0XQwYIFC+jatStxcXEYDAY6duzI2rVrS7Tj+NxWr15Nq1atCA8P5/XXXychIQGA9PR0Z91KucUJqh7C4ijEyc2Ffv1MnDqVzv33w/PPz0an08lWv8kERRaLzJ7tuci02+1BB5QzmUxOk8jZs+Xte6hTnccukBBzQKA0pc4xUz68ditkn5D2G9wqCUXh5S+MKxP/+elPAF7/+pgzDo0SFJh8F0X8/b9XIsaRgwidhmtGi7ziVDF1x+HGZbLYsNrsPsdULAtflx4VFU/I8fBNyWsl7heCyoQj/k+tWq4A+Z9//jkDBw6kSZMmpKWlUVBQwOLFi7ntttvYs2ePV7HIF3bu3Mm6deuYPHky0dHRLFq0iBEjRnDy5Eni4uIA2LdvH3fccQcJCQmkpaVhsViYPXt2CYEnWJo2bUr9+vV56aWXaNGiBe3bt+fMmTM8+eSTJCYmMmrUqDLPT0tLY968eUyYMIHOnTtz9epVdu/ezZ49e+jXrx8PP/wwZ86cYdu2bbz33nslzn/11VcZMmQIY8aMwWQysWbNGkaOHMmmTZsYPHiwR9nt27fz0UcfMWXKFOLj42nbti3Lli1j0qRJDBs2jOHDhwPQpk0b+T4gQZVGCEchTn4+fPedGXiBBQsgLe0ZWRcTZjO88IK0/cwzeFgmFVpsTiEp8PrNvFDUwDPPyNv3UKc6j10gIeaAQGm8zjG7HVbf4xKNUkbAiLerdNReJSxq3OWRfLPF5/N8+b93T7eupDVQhC4MKFTEqskxmyLc1g0FZitR4RW3tCwoI1i1nBhkduP3hrhfVE3y8vIAKW6V42GsyWTCbDYTFhZGeHh4ibIGgwG1WnIKMZvNmEwmNBqNRywhf8rm5+cHHTcrJyeHixcvYjQa+f7770lPTyc8PJw777zTWWbGjBnExsaya9cuYmMlF9KhQ4fSvn17Zs+ezcqVKwNq+9ChQxw8eJCmTaUYar169aJt27Z88MEHTJkyBYBZs2Zht9vZsWMHDRtKVpAjRoygdevWwQy7BFqtlo8//pjRo0czZMgQ5/GOHTvy7bffEhMTU+b5n376KYMGDeLNN9/0+n6XLl1ISkpi27ZtPPDAAyXe//333z2smqZMmUKHDh1YuHBhCeHo8OHD7Nu3j+TkZOexFi1aMGnSJNq0aeO1foGgLIRwFOLo9dCmTRhnzjzOyJEQFibvJQsLg8cfd22HuQlF+SZr0MJRWFgYjxc1IHffQ53qPHaBhJgDAqXxOsfWPwInvpG2e/0Desy4Tr2rOhj9EF78/b9XIm6OQ5aqCFe18DBX1IMCU7DCkX+uZAUm3wU9CDzzmcNVrSyrqmCzqon7RdUkKioKgKysLKeb0Isvvsg//vEPJkyYwFtvveUsW7t2bfLz8/njjz+c1jlLly5l+vTpjB49mtWrVzvLNm7cmIsXL7J//35atWoFSBnQHnroIe6++242bNjgLJucnFxqhjBf6du3r8d+48aNWbVqFfXr1wfg7Nmz7N27lyeffNIpGoFkzdKvXz82b94cVNsO0chRZ40aNTh+/DgAVquVjIwMhg4d6hSNAFq2bEn//v2DatsbtWrVol27dowcOZJbb72Vo0ePMm/ePEaOHMm2bdvKDBYeExPDgQMHOHLkCM2b+28l6y4aXblyBavVSrdu3fjggw9KlO3Ro4eHaCQQBIu4M4U4NWrAL7+EA68oUn94OLziUbUKXZgak8Umy0IzPDycVzwbqDZU57ELJMQcEChNiTm282X4dY203fUxIRoFgbtHlj/3Q3//7/P9FD/8Qe6EF1BS2lGpVBi0GgrMVtksm3zVXioqq5rB7SGa0Wx1uufJibhfCEKZpUuXkpSURE5ODu+88w7/+9//PKylTpyQLFxbtGhR4tyWLVuSkZFBXl5eQAHg3cUgB7Vq1eLKlSsAXLhwgYKCAq9CTIsWLWQVjnJycujWrRszZszgr3/9q/P4zTffTM+ePXn33XeZNGlSqefPmTOHu+++m6SkJFJSUhgwYAAPPvigz+5imzZtYu7cuezdu5fCwkLncW+hRRITE/0YmUBQPiI4dhUm6CCQIkC2QCAQVB4yv4HP06RtXRT0nXNdu1OVkNslyv3unHHgPCaLzPUXqV4O4UgJqyb3HypKCFSl0SjO5XLjT+ypYHC3qlr/8+kKaVNQNcjNzSU3N5f4+HjnsRkzZpCbm8uSJUs8ymZlZZGbm+shlDz66KPk5uby9ttve5TNzMwkNzeXli1bOo+lpqaSm5vLmjVrPMoePHgw6HF07tyZvn37MmLECDZu3EhKSgqjR48mNzfX77pKi59qtXr//tBovHs/2P2Npi8DH3/8MefPn/dwUwPJuqdGjRp88803ZZ7fvXt3jh07xjvvvENKSgrLly+nQ4cOLF++vNy2d+zYwZAhQ9Dr9bz22mts3ryZbdu2MXr0aK+fRXmBugUCfxHCURWmsGgh6r7g8QUhHAkEAkElI+sQrBgkbdeoB08cBLW4xcuFvy5R/nI6u0CReh33cyWDOoN8LnG+/A5cMbYzXZtKAXH9dlUr+utv4g+1m2va2ZzSrpVUe9WNJCYIhMjISCIjIz3mnE6nIzIy0sNix72s2u27W6vVEhkZWcL9yZ+ywcY3Ko5Go2HevHmcOXPGKX41atQIkOLqFOe3334jPj7eaW1Uq1YtsrOzS5RzWC35S0JCAgaDgSNHSmbX9NafYDh/XsrkWFzkstvtWK1WLJbyv5NiY2MZO3YsH3zwAadOnaJNmzYemc1K+376+OOP0ev1ZGRkMG7cOAYOHFjChbA8gk16JKjeiFVliJOVBSpVnjNloiMYni/kFUpfXlJwzFLK5En++CqVtC2Vl+/JYV5eYH2vClTnsQskxBwQKE1eXh43RqsxLrrFdXDCF6Cvef06VYHoNMotY9ytdv25H/ryf19cIFHqQY0zqLOM9XsTd1xZx+QR2Mr6cZMYH8kr97UDXBlgK4JJPaUYK0pZOYn7haAy0bNnTzp37swrr7yC0Wikbt26tGvXjpUrV3qIQvv372fr1q0MGjTIeaxp06bk5OTw66+/Oo+dPXuW9evXB9QXjUZD//792bBhAydPnnQeP3ToEBkZGQHVWRpJSUkAJay6Nm7cSF5eHu3bty/z/EuXLnnsR0VF0axZMw+3M4fAVlxc02g0qFQqD9EqMzPTI55VeThERG/CnUBQHkE5aZssNi7lFWIrds+uFyNM40IBx0IxMty/ANcVEUxTIBAIBDJgMZLxQAT6MBV2lQbVY7uhRt3r3asKQ69VY7Iq766ktAVugR9Z23zBsSwzVND93PHA6c3/Hadr0/hySgePvqg9mx1MVhvhYcEl8vCFivosBYLKwowZMxg5ciQrVqzgkUce4cUXX2TgwIF06dKF8ePHU1BQwOLFi6lZs6aHRc2oUaN46qmnGDZsGFOnTiU/P59ly5aRlJTEnj17AupLeno6W7ZsoVu3bkyePBmLxcLixYtp1aqVh0BVGjk5OSxevBjA6W62ZMkSYmJiiImJcWZvu+uuu2jVqhVz5szhxIkTzuDYS5YsoW7duowfP77MdpKTk+nZsycdO3YkNjaW3bt3s3btWmf9IGVoA5g6dSr9+/dHo9EwatQoBg8ezMKFCxkwYACjR48mKyuLpUuX0qxZM5/GCJL7WnJyMh9++CFJSUnExsaSkpJCSkqKT+cLqjcBCUd/XMzjybW/8NOJKx7H7UgmusfnDfZ6nsB/4uPhwIEIrlzJolkz/8xN84oWumVZHEVESFZNjm2Q9wllREQEWUUNyG0qG+pU57ELJMQcEChNxJfP0rpO0Y/m4W9CbJPr26EKxqDTcNUoiS4Wq40wGS2Q3A1Z/HH1CuT/XikrFiUsjrzheOBU/EGiv/h6unuw6t2ZV7itmY9ilSPzmX/d8miztHhRwWZVE/cLQWVj+PDhNG3alAULFjBx4kT69u3Lli1bmD17NrNmzUKr1dKjRw/mz5/vEag5Li6O9evX88QTT/Dkk0+SmJjIvHnzOHLkSMDCUZs2bcjIyOCJJ55g1qxZ1K9fn/T0dM6ePeuTqHLlyhWeffZZj2MvvfQSILnhOYQdnU7Hjh07eO655/j000/54IMPiI6OZujQobzwwgsesay8MXXqVDZu3MjWrVspLCykUaNGzJ07lxkzXIkshg8fzmOPPcaaNWtYtWoVdrudUaNG0bt3b95++23++c9/Mm3aNBITE5k/fz6ZmZk+C0cAy5cv57HHHmP69OmYTCZmz54thCOBTwQkHP3tP7+gUat4O7UTtaPDUQmPbsVQqyE5WQUk+H2uw2Tc8STQGyoVJBSr2lH+rR3HGZByg9/tetavcqYfrW5U57ELJMQcECjK9rmo9halZ+7xd1St77m+/bkOuAsIBWYr0Qq5rvkTXNq3/3tPiUQpKxZlsqqVlHdSuzbmhz8uyxYLqrxVpdbtOv+Yedl34SgI5HbHK464XwhCkdTUVFJTU72+p1arOXr0qMexPn360KdPn3Lr7devH/v27Stx3N0yCUoPgJ2ZmVniWPfu3dm9e3e5dXqjcePGPru91qpVi4ULF7Jw4UKfyrszc+ZMZs6cWWYZjUbDokWLWLRoUYn3xo0bx7hx40oc9/VzA+jSpYvXz0kgKI+AhKODZ67y38dup1ntKLn7I5AJk8WG2Sp9aUSWYXHkDYfJtyXYR4cCgUAgUIa978P/XpS229wHvZ6+vv25Tujckj8UmK1E67WKtKO0e5LsYkTR7VtJ9yp3yxqDTAKVP+GK7u/ckA9+OInFWjFrFddnWTGZ3AQCgUAgCCUCejTXvE4UV/JNcvdF4IXcXOjTx0SzZs8ze/bzmEy+fe7PrHep+IYyLI5MJnj+eenlqHrcbY0BMMpg2m4ymXj++ed5/nnf+15VqM5jF0iIOSBQhHP7YcMkAGz1O/HCwQZijgFGmd293OUIf1y9Avm/98eiyR8MRQ+O5Lifl9mO3NlYfTBkj9ZLY/MnxpXDWioQdzLHWqq0zzJY+UrcLwQCgUAQyvhsinLNaHZu/33ATczbfIgZ/W/iphuiCdN43oGVeuJXHcnPh+3bzcA/mDMHnnxyGjqdrsxzLuUWsvanPwEpULn7E9nimM3wj39I29OmgU7nCjqZL0OwTrPZzD+KGpg2rfy+VyWq89gFEmIOCGTn2nl4/TZpO7wmBUOWM7OOFDuius8xOe5ZpeGPJY0v//cVlVUtwnk/VzirmtzCkQ9oi9aeJkvFWAD5ar0VaPgGcb+oIIINRiUQCATVFJ+FozbpWz1uhXZgzPLvPMqI4Njyo9dD8+ZhZGVN4O67ISys/EtmdVvVrZ/ctcyyYWEwYYJrG9wXgMEvxsLCwphQ1IAvfa9KVOexCyTEHBDISkE2LHP7Th+fQVjNumKOFSG7aOF2LzVb7ZitNo/YOqURyP+9nMIOlMyqlp1vLr1wgLgLJHLFUvI1xgiATiO1aa6ArHrgCgC+73QOdrsdlczCg7hfKMzeD+DbRXDpmLQf1wxumwptR13ffgkEAkElwec70wcTb1WyH4JSqFEDfv89HHjL73PVKqhdQ19mmfBweKtY1Y4FoBym8+Hh4bxVvIFqQnUeu0BCzAGBbFgt8MEoyL8o7Y/bCrVbEg7Veo656wxKxyEymq0+CUe+/N8Xl0eUciVzd1Xfc/IKHRrWUqQdvcyxlHyRZLRh/lscOY1NArAKiotyWQDtOZlNx0byfpbifqEg3y6BL5+HzhOhd1HmrJO7YNN0yL8EXR69vv0TCASCSoDPwtGtTeKU7IcghHA8ocw3WRR5qiYQCATVHrsdtj8H5w/4Vv7ELijMkbb7z4OGtyjXN5mxWG1o1CrF7yVKxQlyUJmCbzssd2IjXWLH3pPZsghH3myCnPF/zDZsNjtqtfLrBl2RiFdRFkfN3RLCXMotLPG+3aVKCUKNH96AwQuh3f2uYzcNgtot4at5QjgSCAQCHwjIFvarw1lEhofRqXEsAP/elckHP5yiee0onrs7hZoRIsZRZcYR48hml4JOOrKsCQQCgUAmvpoHO17y/7x2Y6DLZPn7oxBGs5U+L31Nk4RI3hsvv9jlLmLky2y1U1wgOXW5gNrRZVvxBorcfXegVsHIjvX5z09/yi5OueuAEW6WTdkFZg/Byh/8CTDtiN9orqCsaiqViq5N4/j22CXFrdsqDZnfSO5fZ/ZC7jm4bzW0vNN72f9Og5/elYRv9++w/Mvw2ZNweAuo1JB8FwyYD+FumZvP7YfNf4PTeyAyHjo/BLdP872f185Dg84ljze4RXpPIBAIBOUSkHA0b/Nv/H3gTQD8du4qczcdYkK3RHYdv8Rznx5kwci2snayOpOVBXXq5AG1MRjgwoUsIiMjZas/Lw9q13a1FRnpsjgCKUtNMMJRXl4etYsayMqSt++hTnUeu0BCzAGBV/atha/nS9vtH4AGPrqC16gLzfp6HAr1OfZj5mVOZxdwOrtA8baUDsy8/uc/fXJP8uWalAiOraAQIaf7eWno3dYJH//0JxO7NwmqPl+s0xxug5mX8nyu1/GxB2r8puRnGer/y14x50OdFOl77MMHSi936L/w526IrlvyvXUTJfHm/zaA1QyfTIb/Pg73vC29b7wK7w2DJj3hzpclK81PpoC+Jtw81rd+xjaBA+uh+988j+9fB3FNfatDIBAIqjkBCUenruTTrMhk97N95+jTsjZPDriJ/adzSH33R1k7KHCQT4FC6+78fM99rUaNVqPCbLWTZ7IEbUGWX7yBakR1HrtAQswBgQenfoSPx7v2By0ArSGoKkN5jrk/iFDChck9mPJ/fvqTkTc3kLFuz32LH5Yt/l4TuYUI9546M6XKJKx5i1+tVqtIiA7nwrVCrhrlD8TtDYer2oEzVzlxKY9GccoLLXpt6Z+lU5QKov5Q/l/2SvN+0qssrp6BzU/Cg+tg9b2e7104DEc/h4lfQr0O0rGBL8Lqe+COuZJYvu8jsJrg7qUQppPcy87tg11LfReOej0N/xkLJ76FhkVC/cnv4I+vYeQKv4YsEAgE1ZWAhCOtRu1c5Hxz9CLDO9QHoKZBS25hxSwYqguxsfD11wbOn/+D9u3BYPDhB4YfVtsGA/zxh2vbgUYtCUd7Tl7hxpjAf9QYDAb+KGrAp75XIarz2AUSYg4IPLhyAt4d4Np/bE/QolGozzG9uwWrxUqETrlsUedyjIrVDb4LL75cE3uxG7VSrmqgIkIrfeayu6oV2x/eoR5vfH08qLH4kVSNbs3jndvHL1SMcGSQOQi4R90h9L987do1rl696twPDw8nPDzc/4psNlj3kJS9rHbLku+f+kGyHHKIRiBZFqnUcHo31LhLEtsb3SaJRg6a9YFvXoGCK2DwIW5X8t0w8QvY9Rr8tkk6Ft8CJm6HusJLQiAQCHwhoBVcp8a1eO7TQ9zcqBa//JnNktHSF/4fF/OoWzP0Fq6VmbAw6N5dDTT2+RyLTVp5hfmQ/UWthsZeqjaapWCThebggk6q1Woae2ugGlCdxy6QEHNA4MRqhlUjwGYBdRhM3QsxwVvHhPoc02td96ECk7LCkVJBknVhakwWm89iQSDXREk3O4NOugZyZW4rLno5kFOg8sVqp3YNPZ0bx/JD5mWf23RYqAVqFeR0VVPgeoXS/3JycrLH/uzZs0lLS/O/om9elr7vbnnE+/u55yEywfOYJkwSg3LPu8rUauRZJrIoxkJulm/CEcCN7WGEyFonEAgEgVK+suCF9LtTCFOr2LzvLHOHpnBDTSlY5FeHL9AjKaGcswVK41g863wQjkqjz03STdliq5hsJQKBQFClWT0SLh2Rtu9bJYtoJCfKZaZy/URXwkrDXcKQPTNZUe0OKxMlYwTJ7qrm9sEoZSVTPE6Q3AKVLzjc8JSOb1WiPS+fpTOpWhXIRHvw4EFycnKcr6efftr/Ss78DN+9DkOXBR5USi5+3yq5xBXn6OdwZFvF90dQYaSlpVWJ/0mBIBQISFmoF2PgndRObJnWnfs6NXQen3VXMmlDWsnWOYEUf+juu820bv0K//rXK5jN5bsCOn4AhGnK/6I0m+GVV6SXe9WOc4PNVmI2m3nllVd45RXf+l6VqM5jF0iIOSAAYNN0OP6ltD1gPrQYKFvVcsyxr3+/QIt/fMb735+UrV/eUPrHvVL1R/gpTvhyTYq7ZCnlqqZSgaHIyks5dzgJOQSq0qyZSiPCEXOogrKcGcqIcRQsoXS/iI6OpkaNGs5XQG5qJ3ZB3gV4uRWkx0qvnJOwdSa83FoqE1VHKuOO1SK5oEXVcZXJLVYmL6vovdq+9eXzNMltrjj2ovcEpbJixQpUKpXzFRYWRr169UhNTeX06dPXu3uycfjwYaZPn07Xrl3R6/WoVCoyMzO9ls3NzWXatGnUr1+f8PBwWrZsybJly2Try+bNmwOz8POB/Px80tLS+OqrrxSpX1C1Cdpm3Gi2lnhSGa33LZjy98cv8eb/jrPvdA5Z1wp548GO9G91g/P9v370Cx/v+dPjnO5JCfx7nCulZna+idkbD/DFoSxUKhiYcgOz72pFZLhraIfOXmXWJ/v55c8c4iJ1/KVrYx7pUTmyKOTmwsaNJmA6Tz0Fjz46Ea227M/XZJEWXlofLI5MJpg+XdqeOBEcVTvODfYptMlkYnpRAxMnlt/3qkR1HrtAQswBAd+9Drvfkba7ToVbS3HZCBA55tjkVT9hs8Mz6/cx+paG5Z/gFy4hQJHMYW46Q6HFhtVmRyNzAG6Dn8GlA7kmR7JyMVlszhTzweIuwMhucVSKtqOXsR1fDQQMfrqOObseZFa11d+f5PlhrQOrpBSq3P2i7SgpXpE7q4ZDm/ukLGwADTqDMUeyTrqxvXTsj6/BboN6NxeV6QRfPCe5+2qKPpNjX0Jcc9/d1C4fg4QWJY/HN4fLx/0eWnVkzpw5JCYmYjQa+e6771ixYgU7d+5k//796PX66929oNm1axeLFi0iOTmZli1bsnfvXq/lrFYr/fv3Z/fu3Tz66KM0b96cjIwMJk+ezJUrV3jmmWeC7svmzZtZunSpIuJRfn4+6enpAPTs2VP2+gVVm4CEo3yThX9+9huf/nqWK/mmEu8fnzfYt3rMVlrWrcHImxvwyKqfvJbpkZTAiyPbOPfDNZ6p4R9fs5esa4W8N74zFpudGf/5hafX7WPR/dIN6JrRzINv/8DtzeJ4flhrfjt3jSfX/kINvVaBBbL86HTQsKGGixdHM2gQaIqN3xv+uKppNDB6tGvbgUM48ieLjPf6NYwuasCXvlclqvPYBRJiDlRzzvwMW56SthvdBn1my96EHHPMoNOQVwGuPhXhTmQ0Wz0eHAWDwyrI3xTsgV6T745forsC7v5OFzLZXdU81ZeICrJsckdOscoX3ANwXzOaPR6UBptVrVLeLwpzPYWX7BNw9ldJ0IlpABGxnuXVWsmCKL65tJ/QApr1hY1T4c5XwGaGzTMgZYSUUQ2g9Uj4aj58MgVunwZZB+H716H/C773M7wGXMksGSvp8nHQRvg56OrJwIEDuflmScybMGEC8fHxzJ8/n40bN3LvvfeWc3boM2TIELKzs4mOjmbBggWlCkfr1q3j22+/5e2332bcuHEATJo0iXvuuYfnnnuOCRMmULu2j5ZwFYjNZsNkKvmbXSDwh4Aebc3b/BvfHrvE3KEp6MLU/HNEG6b3TaJODT0L723ncz29WtTmb/1bMCDlhlLL6MLU1I7WO1/uqeGPZl3j698vMH9Ea9o3rEWnxrGkDWnFf389w/mrUnaVDXvPYLba+Nc9bUmqE82QtjeS2jWR5TsrxxOGmBg4cUJPXt5q/vOf1T6p+g7hSOuDq5peD6tXSy/3qsOKntiag4xxpNfrWb16NatX+9b3qkR1HrtAQsyBaszFo/BmT2nbECvFNdLIHxhajjkWHlYxP1LPKpD1rPijDUWyXfkpTvhyTRyiVEK0ywVIiTT2KsCgrSBXNTkEKj+fVSmZ5cwbdyTXcW7LLYRWyvvFmZ/hjW7SCyDjGWn7Sz9EneFvQXwS/HuIFAuuYRe461XX+/qa8OB6SZR6owdk/AN6PAk3j/W9jZsGwZanPUWuS8dg6z9kdR2uTnTrJl3zY8eOeRzfvn073bp1IzIykpiYGO6++24OHTrkUSY1NdVrIHhv8YhUKhVTpkxhw4YNpKSkEB4eTqtWrdiyZUuJ83fu3EmnTp3Q6/U0bdqUN954w+fxxMbGEh0dXW65HTt2ADBq1CiP46NGjcJoNPLJJ5+Ueb7ZbCY9PZ3mzZuj1+uJi4vj9ttvZ9s2KdZWamoqS5cuBfBwEXSwYMECunbtSlxcHAaDgY4dO7J27doS7Tg+t9WrV9OqVSvCw8N5/fXXSUiQHk6kp6c761bKLU5Q9QhoFfvFofO8dG87ujSNY8baX+ncOJbG8ZHUq2Vgw97TDG1fT7YOfnf8Eh2f20ZNg5YuTeP42x0tqBUppeTccyKbGvow2tSPcZa/vVk8apWKn09mMyDlBn4+cYXOibEe5t/dk+J5/etj5OSbPYQoB4WFhRQWFjr3r127Jtt4KgKTUzgK3OQ9TCaLI4FAIKh25F6At3q79h/+uuST9xDC4e6jBO6xfFbuypR1feANOX/MO7ruiBGkhDihAnq1SODLwxdkFXY8gmPLHEC6tFWBw/rn1z9zgna7U/lot+Nv/ClnAOsA7YJUKhVR4WHkFloq1LIqZEnsBmk5vpefvq/ksYhYuOftss+7IQXGlRQKfKbfHCmr5ZJOUONG6djVM5JIdcdzgddbjXHE/6lVy+Uu+PnnnzNw4ECaNGlCWloaBQUFLF68mNtuu409e/YEnDVw586drFu3jsmTJxMdHc2iRYsYMWIEJ0+eJC4uDoB9+/Zxxx13kJCQQFpaGhaLhdmzZ1OnTp1yavePwsJCNBoNOp3O43hEhGS59tNPPzFx4sRSz09LS2PevHlMmDCBzp07c/XqVXbv3s2ePXvo168fDz/8MGfOnGHbtm289957Jc5/9dVXGTJkCGPGjMFkMrFmzRpGjhzJpk2bGDzY0+Nn+/btfPTRR0yZMoX4+Hjatm3LsmXLmDRpEsOGDWP48OEAtGnTpkQ7AoE3AhKOsgvMNIyT/kGiwsPILpCeknVqHMs/NuyXrXM9WiQwIOUGGsQaOHEpnxczDpP67g+sm3wbGrWKC7mFxEd5BuwL06iJMWi5kCsJPxdyC6lfy9MMNaHonAu5Rq/C0bx585z+n5URq01aGQUT58FhrWRRLNOOQCAQVEGsZlhzPxQW/ZiatAtiQtst2mG1oTTBPMzwFSXEnQgFAiJ7xCDy0xXOH1QqlWJZ4YqvMBq4rbV2n7hM16bxsrbnDcdn99n+sxWWnEWv1ZBbaCkx1+xFqpRI4BSC6GvC+G1SkoJz+yDMAHVaQePbFG86Ly8PkIQFh+WIyWTCbDYTFhbmEXjcUdZgMKBWF8UaNZsxmUxoNBoPSzR/yubn5zuFjUDJycnh4sWLGI1Gvv/+e9LT0wkPD+fOO+90lpkxYwaxsbHs2rWL2FjpYcnQoUNp3749s2fPZuXKlQG1fejQIQ4ePEjTplJ82l69etG2bVs++OADpkyZAsCsWbOw2+3s2LGDhg2le+6IESNo3VreWGQtWrTAarXy3XffcfvttzuPOyyRygsY/umnnzJo0CDefPNNr+936dKFpKQktm3bxgMPPFDi/d9//x2DweDcnzJlCh06dGDhwoUlhKPDhw+zb98+kpOTPfo/adIk2rRp47V+gaAsAlrFNYyN4NTlfACa1o7k01/PAPD5ofPU8DEwti8MaXsj/ZLrcNMNNejf6gbe+Usnfvkzh++OX5KtDW88/fTTHmlIDx48qGh7ZZGVBSpVHipVAvHxCc4bRVn4kxI2Lw8SEqSXe9VhRTehT/edDajfrvrzSEhIICHBt75XJarz2AUSYg5UQz57Ev78Udq+axHUSS67fJDIMceUFI7crVOUEEbsxdKTKRFHySFOmIqCb5eHP9dEpXKL06NwVrhLeSZmfSLfw73iNIiNQK+V1g55hYGNxV8b5xoGac15/mqhIvPLGw6XPLlFSnG/UIBV90jBt0H6ZzuzFzr8BW55SBKN8i/Dks5lVhEsUVFRREVFcfHiReexF198kaioKKfo4aB27dpERUVx8qQrw+XSpUuJiopi/PjxHmUbN25MVFSUhxvYihUriIqKKuFG5S4cBErfvn1JSEigQYMG3HPPPURGRrJx40bq168PwNmzZ9m7dy+pqalO0Qgka5Z+/fqxefPmoNp2iEaOOmvUqMHx45LrodVqJSMjg6FDhzpFI4CWLVvSv3//gNv1xujRo6lZsybjxo1j27ZtZGZm8uabb/Laa68BUFBQUOb5MTExHDhwgCNHjgTUvrtodOXKFXJycujWrRt79uwpUbZHjx6yXHuBwEFAwtE9Hetz6OxVACb1aMa/d50g6R+fMWfTQR7q3kTWDrrTMC6C2EgdmZekG2pCVDgXcws9ylisNrILzE6rIm9lHNZICVHefcjDw8M90pD64vOqPBe5dOli+cUAm+Opl681X5Re7mjDpLMzL+X72sEy6r/occOsTlTnsQskxByo4tjtcPkPOH8A/vu4Zwa1jn+pkC4EO8fCtcpbAkHFBMeW88e8Q5Nyd+XzVZzw55ookeLdXU/TuwmD/951ooTY5n/dpZ/fvoHkthLsdfDVamdouxud2/58fsFYBUUUxYxSYj6L+4XMHPsCLG4BgXcshIIrrn2bBS4F9gO+urF06VK2bdvG2rVrGTRoEBcvXvSwljpx4gQgWbQUp2XLlly8eDFgQdRdDHJQq1YtrlyRruWFCxcoKCigefPmJcp5608w3HDDDWzcuJHCwkLuuOMOEhMTmTFjBosXLwYkobAs5syZQ3Z2NklJSbRu3ZoZM2bw66+/+tz+pk2buPXWW9Hr9cTGxpKQkMCyZcvIySnpMpqYmOjf4ASCcgjIVW1CN5c4dHvzeL74aw/2n86hcXwkN91QQ7bOFedsTgFX8k3UjpYEnw6NYrhqtLDvzxxa168JwLfHLmGz22nfMAaA9o1qsSDjMGarzWkmv/PIRZokRHp1Uws1YmNh3ToDf/65n+7dPZXm0nBm9vBhYWQwwP79rm0H997cgDe+Pk6wWY0NBgP7ixrwpe9Vieo8doGEmANVhNM/wdHt3t87+AmcLxa3I3moInEz7HZ7CUtSOeZYRbmqKRETpkRwbAXaCHeL1ZNvKj9rmy/XxF178Tdrmz+oKBnDymy1owuTwZ/KSxVOt7sKiv8TrdcSHqam0GIj32QhNlJX/klBoi8nrlKgopS4XyhACZGz4uN25ubmAni4is2YMYNp06YRFub5XZKVlQV4Xv9HH32UiRMnlsi054gx5F42NTWV0aNHlygrh+dE586dnVnVhg4dyu23387o0aM5fPhwuWJJcUrziLBavf9PlZZlMFgRPFC6d+/O8ePH2bdvH3l5ebRt25YzZyTvm6SkpHLPPXbsGJ988glbt25l+fLlvPzyy7z++utMmDChzHN37NjBkCFD6N69O6+99hp169ZFq9Xy7rvv8v7775coL75HBHLjl3D07dGLzNp4gPWTu3qkIK1fK4IaBi0jXvuW54e1pnOib0FA8wotTushgFOX8zlwJoeYCB0xBi2vfnGEASk3kBAVzsnL+cz77BCN4yLpniT5zTerHU2PpAT+vu5Xnh/WGovVxuyNB7irzY3UqSGJS3e3u5FXPz/CU2t/5ZGeTTl87hrvfpPJs3dWDtO9sDAYNkwN+OG773RVK7+oWg2tvFQdHyk9RTBb7R6im7+o1WpaeWugGlCdxy6QEHOgCnD5OLw7CCw+ZASLrA31b5ayBMnMC5sPsXHvGT6dejtxbrH95Jhj7sKC1WYPKj5ecdzX9Uq6EmnUKqw2u7wWR0U3U3VRnKACs9WnMfhzTVSoFM8MVlwYLDBbgwpc7UtbgY4lkN+BBp2GQout4lzVtMq4qon7RdUkMjKyxDGdTlciuHJpZbVaLVptyQfd/pQNNr5RcTQaDfPmzaNXr14sWbKEv//97zRq1AiQ4uoU57fffiM+Pt7Z51q1apGdnV2inMNqyV8SEhIwGAxe3b+89UcONBoN7dq1c+5//vnngORWVx6xsbGMHTuWsWPHkpubS/fu3UlLS3MKR6UJax9//DF6vZ6MjAwPa693333X5377EsZEICgNv4Sjd775g1GdGniIRg5q6LWMvqUhy3cc91k4+vXPHO5/6zvn/txPJT/dER3q8/ywFA6dvcrHP/3JVaOZ2tF6uifF80S/Fh6pg18d1Y5ZnxxgzFvfoVapGJByg0eAxBp6Le+N78ysT/Zz5+KdxEbomNqnOaNvCe1gpcHgWOwGmjUEQK9zLSoLzNYKCWoqEAgEIcXBjfDRg9J2rURo0sN7uYh46D4DtMql0H7zf1Ishzd3HOfpgS1lrTvKzYLmy9+y6JssbxYaB0oII053MkfAYgUsXRxWOwVmqyJjKM+CJViKC4EFJis1DYFbXJcl7ugVFsG8YdBqyMZMgansZB7u1gnB/HSKKMqyl2+yBFGLoEJQqUo+RRU/nGWhZ8+edO7cmVdeeYVp06ZRt25d2rVrx8qVK3n66aeJiYkBYP/+/WzdutUjEHPTpk3Jycnh119/dWb0Onv2LOvXrw+oLxqNhv79+7NhwwZOnjzpdG07dOgQGRkZwQ3UBy5cuMD8+fNp06ZNucLRpUuXnJngQHJta9asGadOnXIecwhs2dnZzs8RpHGqVCoPy6zMzEw2bNjgc18dIqI34U4gKA+/hKNDZ6/x94E3lfp+t+YJvFW0uPWFLk3jyPzn4FLff2/8LeXWEROhY9H97css07JuDf7zSFef+xVK5OfDQw+ZOXZsBQ88AA89lOr1aYI7dj8sjsxmWLFC2k5NBUfVOo0atQpsdsnkPNCg52azmRVFDaSmlt/3qkR1HrtAQsyBSsy5/S7RCGDYG9Cw/HuS0hR3AZJjjrlbMJ3NKTuwp7+4Zw8rMFu9utvJgUHnPdOVbPX7EcDa32vizNqmSFa1ksfk+oy8PZxyuN0F6pZoD8CVyOBss2KEHMdceDHjd+7r5HoI6Vx7BShLifuFAtjtsGESaIqseyxG2DQdtEUWOFZT6ecKymXGjBmMHDmSFStW8Mgjj/Diiy8ycOBAunTpwvjx4ykoKGDx4sXUrFmTtLQ053mjRo3iqaeeYtiwYUydOpX8/HyWLVtGUlKS1yDPvpCens6WLVvo1q0bkydPxmKxsHjxYlq1auVTDKGcnBxnnKJvvvkGgCVLlhATE0NMTIxHIPMePXrQpUsXmjVrxrlz53jzzTfJzc1l06ZNzgx3pZGcnEzPnj3p2LEjsbGx7N69m7Vr13rU37FjRwCmTp1K//790Wg0jBo1isGDB7Nw4UIGDBjA6NGjycrKYunSpTRr1sznOEkGg4Hk5GQ+/PBDkpKSiI2NJSUlhZSUFJ/OF1Rv/BKOLuQWOrNtea1MreJSnvgSlpPcXFi92gQ8xHffQWrqaN+FIx/qN5ngoYek7dGjXcKRSqUiQhdGbqElqLgUJpOJh4oaGD26/L5XJarz2AUSYg5UUq6ehdeL0jRrI+DJ46ANjVgBxX/0yzLH7J7ijlLY7VBosXkEa5aLCCWsdtwewhj8EET8uSbudcsZF6is2B9KBik3yBSvyR9xUWlXv+LcGCNZFtbQBxQmtFTE/UIB2o323G9zX8kybe+vmL5UQYYPH07Tpk1ZsGABEydOpG/fvmzZsoXZs2cza9YstFotPXr0YP78+R6BmuPi4li/fj1PPPEETz75JImJicybN48jR44ELBy1adOGjIwMnnjiCWbNmkX9+vVJT0/n7NmzPokqV65c4dlnn/U49tJLLwHQqFGjEsLOf/7zH06fPk2NGjXo168fzz33HE2alJ8gaurUqWzcuJGtW7dSWFhIo0aNmDt3LjNmzHCWGT58OI899hhr1qxh1apV2O12Ro0aRe/evXn77bf55z//ybRp00hMTGT+/PlkZmb6FWB7+fLlPPbYY0yfPh2TycTs2bOFcCTwCb/uejfU0HP4/DUax5f0qwX47dxVatcI9/qeIDB0OqhTR0N29t306VN6gDh3nMtFHxZeGg3cfbdr2x29NvgnuBqNhruLGvCl71WJ6jx2gYSYA5UQmxVWjXDt37cqZEQjgAKzpzuO3HOsPHcffymuXxSYrLIKRw4LlYqKE1RgLt+qxZdr4i3rmZLCR9+Wtfn8UJYs7ZRlE+QYS15hxblxOa5NeWKV+2cejNXbkLb1eGvHH7IHexf3CwUY+tr17kGlJzU1ldTUVK/vqdVqjh496nGsT58+9OnTp9x6+/Xrx759+0ocd7dMgtJFcEdwcHe6d+/O7t27y63TG40bN/Y52PbChQtZuHChT2WLM3PmTGbOnFlmGY1Gw6JFi1i0aFGJ98aNG8e4ceNKHPf1cwPo0qWL189JICgPv4SjXi0SWLj1d3okJZRY+BnNVl7edoQ+NykTG6G6EhMD587pgQ0+n+P4svBlWaTXQ2musQZd8AEg9Xq9X763VYnqPHaBhJgDlQy7HVbcCVkHpP37VkGz8hfAFUlxaxE55pj78jLfB2EkGArMVmopUK/D0kXe4Ngu9EUBkRdvP0rvctY5/lwTFS7hQ4mscw63qaHt6zmFI7mCSHvTXhxjWf39SeYOTfFboAk0ODYo8/mV1V7xuRaIm5074n4hEAgEglDGr4jHU3o3J7vARO8FX/H618fYeuAcWw+cY9lXx+i94CuyC0w82quZUn0V+Ihj6RJsYpwIraQrFjefP3U5n837zlZYBhOBQCCoEDZNg5PfStuDFkDLu65rd7yh9Peu0mnU5f5x7xAaHK5qP5+8Imv9IFmn1IqQYqRoy4lfEQiOYMtyXtviEsbg1nWd29eMZtnaKU7HRi5ZMDcIqyN/li/uYlVFUJ5IKWIvCwQCgaAq4tcKKCE6nI8ndSXphmj+teU3Hl71Ew+v+okXM34j6YZo1j7SlYRo4ap2vXFaHAW5etGX8hTvvjd2MXn1Hl776lhQ9QsEAkHIsOc9+GmFtH3LI9B5ol+n22x2/v7xr6z45g/5++aGkpnJlKi/uAWJUsKXoehBx3fHL2OzBWf54Y37O0tBkOX6fNytU+Sw7i0NxzJApVJxS1HG21e/OFrGGeVTzjP0vgABAABJREFUllVQp8a1nG1WVMwhR3s5BWULYu7dDmZ15BCqTBYbVgXmmkAgEAgEoYjfkf3q14pgxdjO5OSbybyUhx1IjIukZoQI4qcEFy9CnTr52GzJNGgAv/120JlKsTT8CY6dnw/JydL2wYPgXrVB630xeybHCMBPJy77UH8+yUUNHDxYft+rEtV57AIJMQcqCSe/g41FgS/rtoN+z/ldxa7jl1jzo5RON/W2xHJKB05x4UXuOaa0u49SwtSdbery+aHzgBSA22EVElzdLlHAnzhE/lwTlUrlqlvhz75eLQP84bq3B4u3NYZKpcKg1ZBvsmIMIF5WIDLMQ92bkHHgfIVZQUe4za0Cs5WocGkpHYibnTvifiEQCASCUCbglBA1I7S0jYiRsSsCb9hsFD09PcGpU2UHO3PgKOGLwZHdDidOuLbdcZjPl7ZQ9mWRa7fbOVHUgK9B56oK1XnsAgkxByoBF36Hd/pL2+E14P8+gTCd39WYrPIGlS6NQ2eveuzLMcfcrV/k/vFdPO6LUuJIg1jXj+wCs1UW4ciBCjf3JJnue+6HDQoIR96aHdquHuv2nC4RYN3vusuRdxzCUTAioT8G05Hh8rv6lUV4mEt4KzC5hKNgEfcLgUAgEIQy8uYSFchOTAy8846ezMwf6N9fCp5YHi6Lo/JXXno9/PCDa9ud8jKVGH1YfOr1en4oasCXvlclqvPYBRJiDoQ4+Zfh3QHStiYcHvsJDDEBVaUPcwkVVpsdTbBB5krBZgez1YZWI/14lXuOKe1epJRFk0atQhemxmSxkW+yEBvpv/hXHPff7hF+BN/295qU95BGLpxjMMkTAL00cceg00Bexbmq+Sq8uYsxwXjyO6yqCsxWWcU+cb8QCAQCQSgjhKMQR6eDsWM1QCefz3E+DfRhYaTRQKdSqtaXk+nFaCl/waTRaOhUWgNVnOo8doGEmAMhzscTIP+StP2XjRBVO+CqDKW4r8hFeJiaQosk1ucVWogpCtYsxxzziHGkUPBqB0pZhaiQhBGTxSZ/Gyr/rIL8vSaOui02OyaLDV2Y/AG4wT93u2BwZYnzX6AKxNLG2Z7Zit1uDzq+oy9E6CTh6ExOAQ3jJGu3YI2ExP1CIBAIBKGMMqsTwXXFnxhHZRFRjml+YZDm7gKBQHDdWD8Jjn0hbd/1KjS8NajqiruvyE29GINzW8k4RJUtxpE7LnFH/nuTu+gihxuRew16nWvurP/5z6Drluov2Ud/3O3KrLuc4TvaCUbA80f7cbRnt+MUV5XG8RF8tPtUifcqQrgSCAQCgaCiEcJRiGM0wiOPWOjbdzXvvrsai6X8J3j+xDiyWGD1aulVvOryFn++LAotFgurV69m9Wrf+l6VqM5jF0iIORCi/O9F+OV9afv26dAxNegq3b9vlbCqcf+t7i6+yD3HlI4TI79Fk1t2Mpktatw/c/eAyOWJE/5cE5UKwt3cHM/lFAbU17LqdyD351OaO7ySAp43HKIelD1/PbOqBSfutKlfE0DWDH7ifiEQCASCUEYIRyHO1avwxhuFfPHFA4wb9wCFheUvKh0LabUPylFhITzwgPQqXnW5rmo+LD4LCwt54IEHeOAB3/pelajOYxdIiDkQgvy2GbbPlbbbjIK+abJU626FobjVjlv9cswx95++F64VknXNGGQPS2fhtt8VqVelcj3sCMRFqsy6UXmIE+Vd30CuyfjbpUx8soleXvQMh/hlttoxKxjM3XEdMi/l+X1uIDKMVqNGq5HWO0r/7zno27IOIK8FnbhfCAQCgSCUETGOQpywMKhVS821a3257TZQq33X+nyxOFKroW9f17Y75QUD9WXBpFar6VvUgD99rwpU57ELJMQcCBH+2AGHNkLeBTiwXjpWKxHuXKhIc0q4Y7lb1riL9nLPsTyTlc7Pf8GeZ/vJEmC6OHJmOwNPoaG8hA5+1+1WuXvw7fKury/XpLi7m9x990ZxyxxHgHW50RQtPt7bdYJHezULqA5/LYIMWg1mq6XiA3K7uew73AMDtWUS9wuBQCAQhDJCOApxYmPh8mUDsM3nc/zJqmYwwLZSqnYsjI5fyPX6vs0uPdl1ZITxXr+BbaU1UMWpzmMXSIg5UEEUZMOVTO/vHf4Mvv5nyeOpm0AXKVsXlAwwXRx3qwo55pg365T9p3PonpQQVL3e6jYq9NmoULli+MgsHjgewjiCb5d3ff25Jo66lbSWchAepkalkq5JgdlKtF4bXN2lLDG6NI3ji9+yiAiXVyQsC71Ww1Wjhfd2nSBtSCuvZeyevmpBYZA5Qx2I+4VAIBAIQhshHFVBbEWro2DjMzosjvaczKbAZPX6pPh/v19kQMoNwTUkEAgEgfLnbljex7ey3WdAmB46/F9QGdTK4+2dx+nSNE7WOkuLcaQUFpu8rkxqVdHDBgUzX+kVjq1j0GrIxiyLMFhcq/NmwSJn/eBKI59vkjeNfHE6J8YCAYqEAYYM0qil+ZRbWDGxgZQSKQUCgbykpaWRnp4uS1IDgaC6I2xhqyByfTf2bOH6YXU53+TcdsQSgIpbpAkEAkEJCnNh1QjXfo163l+1EmH6Qej9D+j+N0VEI/csVkpndpLbnclbBi6zVZ4biaNuh2Wq1WaXrW5ws7BVuR52yGW1U/xzkTu4NLgsguTKeFai/mL6XHku6L5Q3g8wx+eUX0FZ1QAe7t4EqDghxxUA3NWe+1wUCKoKK1asQKVSOV9hYWHUq1eP1NRUTp8+fb27Jxvr1q3jvvvuo0mTJkRERNCiRQv++te/kp2d7bX8xo0b6dChA3q9noYNGzJ79mzZgtpv3ryZtLQ0WeoqTn5+PmlpaXz11VeK1C+o2giLoxDn4kWoVy8fs7kTSUmwZ8+PRERElHmOK6ta+auX/Hzo1Ena/vFHcK86ITqcSJ2GPJMVi1sgTQ+XjHIWafn5+XQqauDHH8vve1WiOo9dICHmgILYbDCvnmv/4f9B3bbXrz9uVGRmMqXmmEVGcQckYcTxoKHAbEUXJv9zK6XiBDnupL5amQRyTSoixhG4W2UF305pKwylRLCycGaBLaNNdyEwWHHHm3AULOJ+IQhl5syZQ2JiIkajke+++44VK1awc+dO9u/fj16vv97dC5qHHnqIG2+8kQceeICGDRuyb98+lixZwubNm9mzZw8Gg8FZ9rPPPmPo0KH07NmTxYsXs2/fPubOnUtWVhbLli0Lui+bN29m6dKliohH+fn5pKenA9CzZ0/Z6xdUbYRwFOLYbGAy2YGDHD5c/pM+cJXxZV1kt8PBg67t4oRp1IDV4wmxh8tEOU927XY7B4saqG5motV57AIJMQcUwmaFFXe69u/993UXjfwR1IOtP9/DykGGOeblNLld1bRqFWFqFRabnQKTlZqG4OLrOHAXAxyiyG/nrmGy2GQXp3wVC3y5JsUPu7KYymQtVcpUUMJqqrQ2Ci02bDY7arXvKo036zdfKC8LrNzIYblVHHG/EIQyAwcO5OabbwZgwoQJxMfHM3/+fDZu3Mi99957nXsXPGvXri0hpHTs2JG//OUvrF69mgkTJjiP/+1vf6NNmzZs3bqVsDDpp3SNGjV44YUXePzxx7npppsqsus+YbPZMJlM5RcUCMpAuKqFODEx8NJLeh599Eu2bv3SJ1XfZXFUfv16PXz5pfTyVrXDLc39R4TNbUFTXiwJvV7Pl19+yZdf+tb3qkR1HrtAQswBhfjsKTj5rbTd+SFIvvv69qcYSv94df+xqtQck81Vzem+o1JctHBYnWz69Swj39gVdH3FXY9cFkdlizv+XBP3wNtS3fIKdqW6qgUxR8ubGe7xEAO91v4aBDlcISvKVc0hVF3JN3MxtxBwW3sFGHlb3C8ElYlu3boBcOzYMY/j27dvp1u3bkRGRhITE8Pdd9/NoUOHPMqkpqbSuHHjEnWmpaWV8JZQqVRMmTKFDRs2kJKSQnh4OK1atWLLli0lzt+5cyedOnVCr9fTtGlT3njjDZ/H4836ZtiwYQAe/T948CAHDx7koYcecopGAJMnT8Zut7N27doy2zGbzaSnp9O8eXP0ej1xcXHcfvvtzsD4qampLF261Dl2x8vBggUL6Nq1K3FxcRgMBjp27Oi1Tcfntnr1alq1akV4eDivv/46CQlS0ov09HRn3Uq5xQmqHsLiKMTR6eCJJzRAT99PKlq9qH1QjjQaKMtSMawoJay724I/T9Y1Gk21NYWszmMXSIg5oAAH1sOPb0nbrUfCwH/5fKrRbOV/v1+ga7N4osKVu/0pkTnM3RLD3Z1JqTlmtsofp8mg03Ct0KKYC1OEW7r5X05ly16/wcfg24FcE6erVUW5qsnRTilrDH2Yp3AUqeD/mgNfXP3c1y7BhiGKjwp3bm8/lMW9nRoEWaO4XwgqF5mZmQDUqlXLeezzzz9n4MCBNGnShLS0NAoKCli8eDG33XYbe/bs8SoW+cLOnTtZt24dkydPJjo6mkWLFjFixAhOnjxJXJyUiGLfvn3ccccdJCQkkJaWhsViYfbs2dSpUyfgMZ47dw6A+Ph457Gff/4ZwGl95eDGG2+kfv36zvdLIy0tjXnz5jFhwgQ6d+7M1atX2b17N3v27KFfv348/PDDnDlzhm3btvHee++VOP/VV19lyJAhjBkzBpPJxJo1axg5ciSbNm1i8ODBHmW3b9/ORx99xJQpU4iPj6dt27YsW7aMSZMmMWzYMIYPHw5AmzZt/P9wBNUSIRxVQRw/MOSIz6gNk2oxlfIjQs5UtAKBQFAmf+yA/6RK23Vaw92v+RWs5LlNB1n9/Ul6tkhgxdjOyvQR5a0e5LZocvye1qhVWG3SnuOvXHWD7xY7ftXvZhXkLfOnnMibSauUwNuyXVvv168i4g+p1Sr0WjVGs83vdgL10DLopIdcFeWqZtBpaNsghl9OZYskIYIyycvLAyAiIsJpOWIymTCbzYSFhREeHl6irMFgQF304NZsNmMymdBoNB6WaP6Uzc/PDzpmVk5ODhcvXsRoNPL999+Tnp5OeHg4d97pchufMWMGsbGx7Nq1i9hYKbvi0KFDad++PbNnz2blypUBtX3o0CEOHjxI06ZNAejVqxdt27blgw8+YMqUKQDMmjULu93Ojh07aNiwIQAjRoygdevWAY95/vz5aDQa7rnnHuexs2fPAlC3bt0S5evWrcuZM2fKrPPTTz9l0KBBvPnmm17f79KlC0lJSWzbto0HHnigxPu///67R7ylKVOm0KFDBxYuXFhCODp8+DD79u0jOTnZeaxFixZMmjSJNm3aeK1fICgL4aoW4hiN8NRTFkaM2MBHH23wKWK/P5k9LBbYsEF6eataW8ziqLjffXkLaIvFwoYNG9iwwbe+VyWq89gFEmIOyMjFI7CyaIEaXgPGZ0CYzq8qPvzxFABfHb4gd+8qNMaRe/1yzjGDm8XOHxfzgqqrrPrLs9gJFL1WGeHImfmsqP73dmWWWd6fa1I88LZcMY5c9XsuBORwF/RF3HG08+ufOYE14mf0aoO2Yl3VAFrUifJsM8isauJ+UTWJiooiKiqKixcvOo+9+OKLREVFOUUPB7Vr1yYqKoqTJ086jy1dupSoqCjGjx/vUbZx48ZERUV5uFGtWLGCqKgoRo0a5VHWXTgIlL59+5KQkECDBg245557iIyMZOPGjdSvXx+QBJW9e/eSmprqFI1Asmbp168fmzdvDqpth2jkqLNGjRocP34cAKvVSkZGBkOHDnWKRgAtW7akf//+AbX5/vvv8/bbb/PXv/6V5s2bO48XFBQAeAh+DvR6vfP90oiJieHAgQMcOXIkoH65i0ZXrlwhJyeHbt26sWfPnhJle/ToIcu1FwgcCIujEOfqVfjXvwqBYaxbB7m5uR4+td5wyxtSbv2FhVDkwktuLhSvOswR46jI4qj4grG8p3uFhYVOH2Ff+l6VqM5jF0iIOSAT5gL491BpWxsBU38GXaTf1Ri0kquU0hjN/gcF9qt+t+9dOeaY44GALkwNUrgWvjqcxbN3Br/gdH/YIK/FTlH9RX9VqJzxe5TCIUzlFJjLLOfLNSl+L3W5WskjqpUaHFtGi6OyZvdVo/R/9tXhLAa3KflkvjQCtziSxnXhWqFPgdF9yTpbbpsyZ8IT9wtBKLN06VKSkpLIycnhnXfe4X//+5+HeHLixAlAsmgpTsuWLcnIyCAvL4/ISP/v3e5ikINatWpx5coVAC5cuEBBQYGHwOOgRYsWfotWO3bsYPz48fTv35/nn3/e4z2HcFNYWFjiPKPR6CHseGPOnDncfffdJCUlkZKSwoABA3jwwQd9dhfbtGkTc+fOZe/evR598PadlpiY6FOdAoGvCIujECcsDKKi1KjVXencuavTHLUsHMGrfVkXqdXQtav08la1I8ZR1jXP4I8ONv161ikqea9fTdeuXena1be+VyWq89gFEmIOyIDdLolGV/+U9u9fA5HxZZ5SGnoFhYXi2aCMFpndyUqxOJJzjmnchC6549KoVK4f2nJb1TgwyGxxZC92L/2/Lo0AMFnKFnf8uSaOxb6j7yarrcx7arA42nnzf8cVawPgwVulzypQl0d/ZR33LH3bfzsfUJv+opfZ7U/cL6omubm55ObmesTJmTFjBrm5uSxZssSjbFZWFrm5uR5CyaOPPkpubi5vv/22R9nMzExyc3Np2bKl81hqaiq5ubmsWbPGo6wjW18wdO7cmb59+zJixAg2btxISkoKo0ePJjc31++6ShNurVbv/0sajffvdiWyD/7yyy8MGTKElJQU1q5dW0LAdbioOVzW3Dl79iw33nhjmfV3796dY8eO8c4775CSksLy5cvp0KEDy5cvL7dvO3bsYMiQIej1el577TU2b97Mtm3bGD16tNfPojwRSyDwF/E4I8SJjYVr1wzANz6f43RV86GswQDflFF1XtEC/+DZqwxtX8/rF9PZHCMNYr37ThsMBr4pq4EqTHUeu0BCzAEZ+OxJOPWdtD1kMTTpEXBVem3F/RgrMFmdmZ7kxt3SU4455vhaD3MTjiwyZVVzR24LDfAU1No2iJGtXm84AqoXmK3Y7fZSf/wEck2KZyKL1sgzV4t3sUaRwBJM8PPiIqk3miZIVgUV5ToWG+lyW72QWzEppyOK3OPyi8YYbHxJcb+omnizsNHpdOh0JV2tvZXVarVotdqgygYb36g4Go2GefPm0atXL5YsWcLf//53GjWSxOLDhw+XKP/bb78RHx/v7HOtWrXIzs4uUc5hteQvCQkJGAwGr+5f3vpTGseOHWPAgAHUrl2bzZs3ExUVVaJMu3btANi9ezedO7tiJZ45c4Y///yThx56qNx2YmNjGTt2LGPHjiU3N5fu3buTlpbGhAkTgNKFtY8//hi9Xk9GRoaHtde7777r8xjlsLYUVF/EI40qiNN0X4bvhua1ozzq8rZcVDoLjEAgqKbsWgo/FAWQ7PEUdPi/oKqT2yLFHX/deINBqR/j7hZHpSVE8Bf3+5HDQuOpj/dx/IL/T6nLQqWCG2Pkfbpa/H7n6L/NDoXlWB35W3d4mNp5n5Xj+pYm7Yy5RbJkMAbZfyh7jWEoEk3P5BjJumr0uc5g5Mrh7esBpWc1lDOrGrgCciuRRVEgCHV69uxJ586deeWVVzAajdStW5d27dqxcuVKD1Fo//79bN26lUGDBjmPNW3alJycHH799VfnsbNnz7J+/fqA+qLRaOjfvz8bNmzwiA916NAhMjIyfKrj3Llz3HHHHajVajIyMpxp64vTqlUrbrrpJt58800PC6lly5ahUqk8Aml749KlSx77UVFRNGvWzMPtzCGwFRfXNBoNKpXKo93MzEw2bNjgyxABl4joTbgTCMpDCEdVEYd5vQxLo5Z1awCuhZHNbeXl/vRVIBAI5GDXsUss/fIotj92QsYz0sGmvaHH34OuW0nhqDhKCuo//HFZVncvx7e61s3KRYn+R7h9/pNXlwzkGao4A1i79V+uz8dRt0qlcllkyRg8vPgqwGEFZ7LYAnYj8yc49i+nsrn9X1+Sk192XKjiBPLgS+8MMF5BmdVkCDQuEFRmZsyYwfnz51mxYgUgBf2+dOkSXbp0YcGCBTz33HP07t2bmjVrkpaW5jxv1KhRREZGMmzYMF599VXmzZvHLbfcQlJSUsB9SU9PB6Bbt27Mnz+f559/nl69etGqVSufzh8wYADHjx/ngQceYOfOnaxatcr52rZtm0fZF198kV9//ZU77riDt956i8cff5wXXniBCRMmeLgOeiM5OZn77ruPf/3rXyxfvpxHHnmEtWvXcv/99zvLdOzYEYCpU6eyevVqp+vh4MGDyc/PZ8CAAbz++uvMmTOHW265hWbNmvn8ORkMBpKTk/nwww957bXXWLNmDfv37/f5fEH1RghHIc7lyxARUYBG04n27TuVG60f/LM4KiiATp2kl7eq9cUWRu4LxggfFmkFBQV06tSJTp1863tVojqPXSAh5oD/3P/Wd3yy9XPUK4vSytaoByNXeA/C5idKZd2CkpYScv+YLO4mfOjsNakdGeeYu7uPXHFbXK7TKg93rNPZcv0/lB7Tr7xYRP6i1ajRFiWMKOv6BnpNKkKIkFP8KuvhlMMaB6TrkHlJ/ix9JdqsYCHH8X1y1SiJYsGGWxH3C0FlY/jw4TRt2pQFCxZgtVrp27cvW7ZsIS4ujlmzZrFgwQJuvfVWvvnmG49AzXFxcaxfv56IiAiefPJJVq5cybx587jrrrsC7kubNm2clkKzZs3inXfeIT093Rlwvjx++eUXAP71r3/x4IMPeryKB8i+8847WbduHZcvX+axxx5j3bp1PPPMMyxdurTcdqZOnUpmZibz5s1j6tSpfP3118ydO5eXXnrJWWb48OE89thjbNmyhQcffNApKvXu3Zu3336bc+fOMW3aND744APmz5/v8xgdLF++nHr16jF9+nTuv/9+1q5d69f5guqLiHEU4lgsUFBgA3azdy/YbOUvhB2LF7UPypHNBrt3u7aL41qIlXwzwocMOTabjd1FDfjS96pEdR67QELMAf+pw2U26v7hOjBxO+hrylK3ksJRceQSXkrD8aNfjjnmuGd0ahxLXKSOrQfPK/LjWwmLnbL43+8X6JtcJ+Dz7V6ewui1GsxWS5nX15dr4k1kcMzPT/edpcUN0YF12lm/dxUj3C3bWIHZKnsQdAcGrWe9vro+BhPs1rEmKW1uucdmksOV3yGEfnP0Enlu2RoDrVvcLwShSGpqKqmpqV7fU6vVHD161ONYnz596NOnT7n19uvXj3379pU47m6ZBKV/J2RmZpY41r17d+f/UFl1esPf756hQ4cydOhQv84BmDlzJjNnziyzjEajYdGiRSxatKjEe+PGjWPcuHEljvv6uQF06dLF6+ckEJSHEI5CnBo14Nlnwzl6dBMjR+IRDK007H5Exw4Ph02bXNvFcYpDRW4R7t9DjhgGZfn3h4eHs6moAV/6XpWozmMXSIg54AOFufDuAMg6BMD3eum7xoYG9dSfIPoG2ZpyFy5yCswemZiCpfgiLV9ui6Oiv2qVFGPHIVzIOcdUKpg7LMUpHJUVANp3XJ+Lu3Bnlin4trtFU3Eu58sfJNmg1XDNaCnT0tava+LW7asFkuVK5kX5rHOKXz61WnKJKzBbAxY3fblyhmIZDM1+Wn8FMuv0CmftK87NjWKd2ycu5Qddn7hfCAQCgSCUEcJRiKPXw5w5YcBgn89xPiT1oWxYGAwuo2pDMasi9yd2kT5YHIWFhTG4rAaqMNV57AIJMQeQftl//wZcOOT9/Z9WeD38YcNZ3B+b6PW9QImPdrlifXU4i7vb1ZO1fneUCpgboQsjt9Di/N6Ve445xDV7UQBouay0VCrXg4iKIlirJm/Zw8qzagHfrom3p8FPDmjBs58cUNway6ArEo6CdVUrKzh2sXkjV7D1sijLQloJbqipp2FsBCcv50tCa5D1ifuFQCAQCEIZIRxVQZxPYGWwxXbGODJ5iXEkgmMLBILy2P02bHmq/HIdx0KPp+j8whdcw8DAyKbcX/5ZfhGtd1kY5RbKa5WgfIwj6a9Bp5GEIxmFKfc04u4/+AtMVlnd++rU0MtWl4OyfqzL9Rm530mLx/2Ts+5IGe+pZX0uhmL3dSUoLhL6amEWjPjifNDlU1Y1eVJSOwOae1wzke5aIBAIBFUPIRyFOCYTLFhg5dix7YwYAf3790ajKXshb7O7fgSUh9UK27dL2717Q/Gqiz/Bc1/UOTLkHDp7tYz6rWwvaqB37/L7XpWozmMXSFT7OfB7Bnz6V2m7xWC4sZ33crFNoLWUwjaLWgAYLfL/qHX/4ah0DCKlBPXiseXknmNhGjU6jRqT1UaB2Vp0NQLH3XN6UOu6TPtwb5A1esfbcxJF4jSVI05A4Nckwoe6/afkB6PXSnGOAs0+5ksokBtqeoqE/gYqD+TBl2O98vmh89hsdtRq5QUcvYzXrNrfLwQCgUAQ0gjhKMTJzoaZM43AHbzzDuTm5hIZGenTub6su4xGuOMOaTs3F4pX7VgkHzp71Zk5xEGUXpo+q747yT8GJ3t9Mm00GrmjqAF/+l4VqM5jF0hU6zlwbj+8f69rf/gbEO57wF/FhR2Z6y/+Y3rdntOMuaWRrG1AycxRcsyx4n3Xa13CkZzowpRP5NooLsIZbybYa+yy3nUd8yUphC/XxJv2Iqc1U1niToQjPmHQWdVKR6tR06tFAl8evgCAuQJc1RLjXZ/z8Yt5NKsdpXibjgdo+UUxwYKhWt8vBAKBQBDyCOEoxAkLg/BwNSZTW1q1kjIYlIcfsbFRq6FtW9d2cZq4LcR+OnGFDg1dz5/H3NKQtT/9CUjpaL0JR2q1mrZFDfjS96pEdR67QKIqzgG73c7znx6iVb0aDGtf33uh3Avw7kDX/qRv/RKNQBlrEfeYNUq72P55Jfhgue44+l7cKkXOOeYQSAw6DVeN8rjDuRKTSZU/0S+Jhdt+ly0wub2Yhe3KsZ3pueArQCGLIx/cvPy5Ju6WNRWVTr6i2nFfE1hsPrqqBaG9tG0Q49z2FiDbvWo5sqqB6+Gae0yzQOuuivcLgUAgEFQdhHAU4sTGgtFoAPb6fI4zXoUPqxeDAfaWUXVcVDhJdaL4/XyutDByW3m1urEmWo0Ks9WOpZT4BQaDgb1lNVCFqc5jF0hUxTmw48hFlu/8A8C7cGS3w0f/B4VFLqwPfQ11WvndjlHhALeBuumUjud3oNXHH8r+UjxhgRJzTLJIKVREWGjfMAaQstplXTNSO1reuEeN4yN5ckAL/rXlcPAWR16O+WIV5Ms18SaQ+OIG5y/elgHBu1f5Nrfd2/7xj8vc07EUodnbuf52qYgmCZEcv5CnuMWiAzlFuKp4v6huBGt1JhAIBNcDX7+7xCONKog38/pgcAQ0lbKGuCaWSgVhRU/FShOOBAJB1aLcoNL/+Quc/FbaHvZm6XGNymHvqeyAzisTt68ppTJXOb535RamnMGxtUUBlBX4YewIGFw8KUIwFF+LtHezWj1+Ifi08y6LJtcxg5v7kBy4B1J21q1A4G1f3OB8paxFYIRMn48/a4y1e/4Mqi1fcVyf//56RvYA+N5wFxLFKqj6otVKFpT5+fJamgoEAkFF4PjucnyXlYawOKqCOBfSMmX2cCxm801Wjx8BapUKrUZFgRnMtopJfysQCK4v7lm37Ha7p2XjF3Pg4CfSds9noO19QbVlNMub1csdxbKeaTXkm6RU5yU+HxmQU1xwUFxkMBQFT5azDcenEBUeRkq9Guw/fVXxAOJGBcQ1Z90K9F1Owc6Bt9nnzb1KabQaX/8PgpNfHNdn1XcnuVpgYdH97V01K2ANYtBJ/yv7T+c4j4mcatUPjUZDTEwMWVlZAERERMj+3S8QCARyY7fbyc/PJysri5iYmHKTMgjhKMS5fBkSEwvIyxvIrbfCtm2fYTAYyjzHH4ujggIYWBSK5LPPJNe14rinm/WIEYAUABNKtzgqKChgYFEDn31Wft+rEtV57AKJqjgHwrUuQ1WT1UZ4WNFNZt9a2PGStN1xLPR8KqD6b6yp50yOEZBfOHL/lpLfVU0iQqdxiuyFFpts/Xf0vbhrjJxzzHHPcARPVsrdx3lPUcCiCeQLMu3tXuqLm5cv18TuRSBxfC6FFpuiWcGC/XwC0V+MZv/GFOhvbvf/t60HzwVWiR/oihbZm34963TDDJSqeL+oTtxwww0ATvFIIBAIKgsxMTHO77CyEMJRiGOxwNWrNuBrvvkGbD5Y9jhjHPlQv80GX3/t2vaGe9wF9yd2KhWEFT1FLC1jis1m4+uiBnzpe1WiOo9dIFEV54C7xZHRVCQcZf0GH4+XDibcBAPmBVy/+w/LfJOVmIiAqyoTuS1GHN+M7j9clbCYKh4HR445VlwHkDe7V8lsDXLW76Jigkz7Urc/18QzY5trSVZgthIZHvgSrSxtR67Px1+LCqPF6jFGJfD4flI4Thr/z955x8tRlf//M9t3b09ueu8VUgihE0LvhhKqSFEUAUUR+ImigIpIUUQNUr4IKkU6AUILHRIgARLSe256u7m5Ze/dvvv7Y9rZ2SlnZs7cbJLzfr3u626ZfaadKc9nngLglDE98K856xHy+1wV9gb20etFwxxg7t+ArQuB+HbgwqeBUWeK3+UywAe/B1bPBvY0AOFqYPBxwIl3ANW9VBsdTcBbtwAr3wYEHzD6LODUe4Aw0RVv+xLgzZuALd8AFfXA5B8CR/+s01aTBkEQ0KtXL3Tv3h2ZTMb6BxwOh1MGBINBy0gjGS4clTnV1cANN4SxZs3zuPhiIBwOW/7GTsRROAw8/7z6Wg+jHH5BENQaRwaFYMPhMJ6XZkCz7PsTB/K6c0T2xzEQILr9dGSyqNm5FnjoMPEDfwj43kwg6PxJOel8sU8nI7qqeRRNE/T7EPKL7exZCl/yomu7qrEcY/IlQxanWhPeOD9k+rM39llFTJVe1+Rln7t2t+GvnO6TcEA9ttwKRzJ64o52DHmFNl0+kbYWjtyKL/LY1bVNvGaVRTS8h9gtMp3LKwXxnaYo7ZPXi0wH0GMsMOG7wHPfLf1u27fAsTcDPQ8CEs3A2/8PePYi4Ecfq9O9fDXQtgP43qui2DTzWuD1G4DzHxe/T7YC/z1HFJ3OfADYsRSYeT0QqQEmXdlJK0qP3++ndsI4HA5nX4ILR2VOJAL89a8BANNt/5amxlEgAEy3ME3e5Oc1d3Vy3YKsQcRRIBDAdKsZ7KccyOvOEdnfx0Ay3gw8dpj6wbVfAFXWoa60eOnYelW8GgAiQVE48iLiRVvjyIsxFpEEjD+9vQI/mjLElS215p4Kmf7sFlkMLCqOHWJbo4lc9qqIWDhyfaPYuUtPqKDaJzoCic8nIBL0IZnJux/7JgKM2+5ttOJO3y7FArKd/eG0RmPUo5pohvMLFUcYumGfvF4MO0n80yNSIz5IIDn9PuCx44HmTUBtP2DXSmDNe8DVHwJ9JorTnHYf8PT5wMl/ECOTFj8P5NLAd2YAgRDQfRSwfTHw+YyyFI44HA5nf4V3VdsPyedLb6TdUHSTr4lmCvhLC0NyOJz9F7U2SwFd3/2J+sVlrwJd3YkMWrwqYA142FUN3rRVl0++UQ/qDynbRTqxj+gpRlF0rfAm6sGb7aPiRZFpmdPHqik2rUn3EVlagUS+3t74/ELlWs4aefvMXr7DE/syPzl+GM6b2Fd573WEE2AeceQF4YDPs06K+yXJVgCCKCoBwKZ54mtZNALEyCLBB2z5SppmPjDgKFE0khl6ArB7NZDY01lLzuFwOAc8XDgqc9JpYMaMHG66aQ4++mgOcjnrGxO99sRG5HLAnDnin5FpvVQ12XQ8Kba7/WR1o4H9HObMmYM5c+iWfX/iQF53jsjeGgPpbB5z1jR6Jo4AwC8Dz6J6w2zxzcl3AUOmMp+Hl46mVx29ADVVyovtr4048mKMnTiqBwBWEUGln8nXFLft4AHziCYvimPXxIKoDJuLdzT7xEgSkpd5fsMerNje5mi5SfRuA+piYtRUU3va05b1leEA/nzBOPSuiQCg2x9upbJDBtQZ2yaMs+o6KwhCyXhzarmc7hna2trQ2tqq/KVSKfdGM0ngvduBg84HItXiZ/EdQEW34un8ASBaJ34nT1Opmaaiu/QdL0TN4XA4nQVPVStzmpuB669PAjgaf/4zEI/HUVFRYfob9ebI+vYlmQSOPlp8HY8DeqajRKqabNsn3UlPn9QXf/9gjYn9JI6WZkCz7PsTB/K6c0T21hj4w6xl+M/nG/Cd8b3x4EUTrH9gRS4LpFoBAL5EC37sfw3XBN4Qv5vwXeDI693PQ6KoDpFHBawBL1LV1CLQkQD7Gj6yea2TymKMaTt8RQlxqlAoMGkrTdrwusaO513hQn7EU1nD/Wtnn2g3bcjvU4o6t6edizpmAsypY3vixue/BQC0JDKKEEZv2568Q9OJrgSHQ+6Mg3rheixw9mOHyJ0U3Y63crpnGD16dNH722+/HXfccYdzg7kM8MIV4onsjL+4WjYOh8Ph7B24cFTm+HxAICAgmx2KQYPoii7KN3U0XW8FARg6VH2th3yTn8zk1I5t0rRyYUijGyZBEDBUmgEL52Nf4kBed46I2Rh47JN1+GT1Ljz2vUnMO2/95/MNAICZC7e6F46SrcAjx4hdcQCMBTBWDFjA4uhkHPSdGe7sm7CvRhxFg2xr7BTZls7Hu9pSSKRzTM8z8q/l8ZjLF5DOSZ3zGMKyxpGehiHbz+YLyOTyCPrZBldbRTS52SfRkB+tUiSvl8JabSyI5o6Mq3nQrpq8vWZ+uxWHDe5qOq3b4tiCIKBndQTbW5MAxOjLUMDb4HpWXQLL6Z5h2bJl6NOnj/LeVbFuWTRq2QRc/roabQQAlT2A9l2a6bNiClplD3WauGaadinSqLK78+XicDgcji24cFTm1NcDmUwMwGrq39jpqhaLAastTEeJehHqQ3Wh6DujlINYLIbVVjPYTzmQ150jYjYG7npzOQDgha8347LDBzCdr98nKB1+qFj8IrDkZeh64a1bFdGIZGl+AB6ovRX/cryU+pBL4GWNo3RW7ILkp1HYaWxL/wV4k6om2+9Sodb5mL18B84e19v1eUbrrMfIgr9pd8KR8rCB+IyVo01COtqRUHF3MqfCkXot1a9DZLR/ac79RgIJWdyZxfYxug+IBv1ohjvhiBZ5Hpv3JKh/4+aofP8XUzDm9ncAABubOjC0u9TWnUxVY6jLaAtyO7VdTvcMVVVVqK6utp7QClk02r0WuOININal+Pt+k4FkC7B1AdBbesix/mOgkAf6TJKmORR4//eiLb/01GLth0DXYWJKG4fD4XA6BV7jaD9EdWDY3Bnp1TiSTctPv5O8KCSH44jm9jRzm7Y6C332V+Cl7wMrZwEr3yz927ZQnO6iZ4HfNmHBFWsxOPkUzkjfjT059sWTSYeatVOrTbHxKupIqeHjwXmxJhpEVUQUplo62I4d2eEN+n0ISIKaN53hxOX3qphwyK8WLH75683M7UdC3u3fKNGu3s34L1iE7kRDzsU7u1FBN5w4DIB3Bem1kMLnrEXbPJ9fZxfkLitScWDbIvEPAJo3iK+bN4lCz/PfE0Wh8x4D8jmgbYf4l5XOXd1GAENPBF77KbD5a2DjF8CbNwNjzxM7qgHAQdMBfwiYeT2wczmw5CXgy4eBI67bO+vM4XA4Byg84mh/RKc9sRvIehFK62PpOzc3nxwOx5tjJxL00xW9XT1bLFYqc+Zf9U8cNX3Fm3sA8PmRl545eB2t4PV5pdVBfRcjyOgUL7qGkULASaN64OUFW5htHz0dIBr0oy2VdT8PnQjYqBQRxKT4tvSfHLWCICj7Y3ur86K+RrV8Yh5ETMmQAXBMIo4MHiCxKCBO+3BKFjpp9rfd+kl6CIKAwwZ1wZfrmzrl3qQzorbKlq0LgH+fqb5/51fi/3GXAMf9Unz4AAAPH138u8vfAAYdI74+9zFRLPrP2WI3tVFnA6fdo04bqQEuewV48ybgkSlArCsw5RZg0pXerReHw+FwSuDCUZnT3AwMG5ZEa+t5mDIFeO21lxCJREx/o3cjbUQyCZx3nvj6pZcAPdPkDaY2Dc7q5jOZTOI8aQYvvWS97PsTB/K6c0RoxoA3whFFMOnutcDT08XXgh/4f+vVFskmkG6dFxEEpOPIPOJI45P+b/4m3HjScKbzALwVFgQUR7ywPM+QQkA0JApHHS4KNBtBpj97xfVTh+IfH67xZIxaRdrS7BMjgeT8Q/pi6dZlALzdPuo+sL9/7Uo7EQf72+2Dr0MHisIRuf/Jbc6ygtBVRw/CrS8vVm07NL5P3jMMOga4o8X4e7PvZGJdgPMfN5+m51jgqrftLRuHw+FwmMKFozInnQYaG3MA3sTs2aBq0WpUl0GPXA548031tR7y0+Ei4UhT48isLfGb0gz2dnvZzuZAXneOCM0Y8MSxtUpVS8WBx6YCKAC+IPDTb6hEIy1epRrJeB0t0M6wFTlZyydKNBRgZ1+FFKa8Os+wWgc9kYFljaOCQYStF1Ffim2L5bezT7TX6cMGqcWj3WwfK3GHRbQwrUCiRC13YmSyl/tfS88aNgIPv2fgcDgcTjnDhaMyp7ISuPLKENaseQKXXgqEQiHL3+RtFCAIhYAnnlBf60E+LdR2bItZtG0OhUJ4QpoBzbLvTxzI684RoRkDnji2ZjU3MkkxbSApPQm+8k2gtj+17aIaRF5EHBH2vRamvLLvZY0jQSiOeGFxntFrqKA+FMg7X1gCMpqJTH/2CqvGDTQYNZqIWtQ4otknRpfp0b3VgsQsor3MimMD7PavGXYizNx2VZOJMNj/tMTs1JQzgd8zcDgcDqec4cJRmROLAf/6VxDAFdS/UVLVKJ4GBoPAFRam5Zv8VDaPNqlNsCwQySkTuXwBiza3YFy/Wo39IK6wmsF+yoG87hwRmjGQzLB33CJGjkw+Dzx/GbBnvfj+omfErjYO8brYrVf2wwEfUtk8W/uEyCAL6ku2UKRpOICM2PHqPONF5zMZFvV1LOexFyOO7OwTvcv0NVOG4OGP12LGh2tx8ykjHS2jlQCjil8OUtVsijtO9rfb5h4xnf1PLjfLdvesimPzewYOh8PhlDO8q9p+iHxz5GN0Y1QRVm+KXl2wBYB6s1tJdID5Yt1uJvPjcA4kvCqOLVPkGH4xA1j9rvh6yi+BkWc4sK56X5lcAZkcW+GL9EnZ1zgSrXudxiI7rV+ub0KW0fZRnV5BjaZhtvzFTQ8AdR3cRr3oRjMR6c9uMeoiKm8jN+KgkT7iRSoiycieVQAAv0+w7I7mFBbbh/YOI6qJTO4MWKyf3XnJsOpoy+FwOBxOOcGFozInnQaefTaHe+5ZiPnzF9LVONJxAozI5YCFC8U/I9PhgB89qsW2282JDEjjPp+ACyf1A6DvBORyOSxcuBALF9It+/7EgbzuHBGaMdDMuKU6ANRXqmkOH6/cJb5Y+irw7m3i62EnA1P+nyPbWr/Py6gRr9JMYh6ksZCb5dSxPZXX7Skv2tmrTrFX5xkvHW9Z2GzuyGBna5K5fXIeXnQmsyr2TLNPzOSTqSO7i3byBWRy3ggtXkaUaZGFo0JBjFw2g9Xa7o31cwu/Z+BwOBxOOcNT1cqc5mbgkkuSACYAAOLxOCoqKsx/ZFCXQY9kEpggmkY8DhiZPm1sLzw5t0F5+kyallvt6t2gJZNJTJhgY9n3Iw7kdeeI0IyBbzezT2fqElOFo7ZkFtg0D3jhcvGDuoHARc8CPjbPDZLpHKojQSa2gGJh6pNVu5jZBVSn1KorlhsECOhbF0PAJyCbLyCRyaEG7rcPWQSajOBgcZ7RCwKJMIrK0useVl8ZVl6/v2InLp5MX2OrxL7B9S5mUYfIDVa27ewTves0GcGSyOQQCtg/Vq3a2rvZPla2tZDr88aibTj/kL6Wv3EbMK23fuRSs4wJsmxGQAm/Z+BwOBxOOcOFozLH5xNz8QuF3ujZky4vX61xZD2tIAC9e6uvjZAdFfnpOWnbzAkTBAG9pRmwrCmwL3AgrztHhHYMpLN5R86hEaSDVIjvAP59tvimui9w7ZeA3/mpX+syel3Aet2uOAZ3q2Rq04suT1rxJRoU29l7mYqYSOeYnmf0imN7EfUVCfoxeVAXzFvf5Nn4YZFOZlgc2yIay+0+CfoF+H0CcvkCkpkcaqLshFkZJqlqlKvm96kTbmzqcDw/O8j7f/m2VjR3pFEb867YdEnEkcPDkN8zcDgcDqec4cJRmVNfD+TzMQBbqH+jPJmmmDYWA7ZQmNZ2RSmuV2H85DIWi2ELzQz2Qw7kdd8fKBQK+OfHazG2dw2OHd7NkQ2zMdClIoSmdjFNzWlUgRU+5HHhxyeoH3z3RSDIpnW0DHthpFiB2cMwlU8WArwsziyfGyMhSThiJIyotXyKoylYnGf0Io5ijKKyjEraDOpagXnrm1ynwhlFv9jp5GUXq/FDs0/Mav0IgoCYJDy6FdYMu6p1Yrt6APjhsYPx6CfrLPc3qxpIQwmxeeGmZhw3ojsTu3pEAmwijvg9A4fD4XDKGV7jaD+kQHoYjFCfPoupamTh7c7okMPhdDafrG7EvW+vxPf+Nc8T+wHiKTzrOjLiOaCA50K/Uz885xGg+yhGtlW8Ou7lU4wX7cK9cJq1AoZ6XnTfUl2LV/WHBA/P69oICjddvWhQ2rG7sG8kSkUYLruRsOM2VZC2q5qj/etA27GqC6XFbcBNXUUIY3pXA1CPE1KUYhnQ4/Px6CAOh8Ph7P9w4Wg/xKjLjBuUm3w5VY38rhO7l3A4JF+s242TH/gY89Y3MbftVdFePX7z6hK23YYKBdwd+D8c6lslvj/8OmDcRezsE7DvfCb+90KQloWAmBunmRK1HTgj4YsYHqwL/+oJJF4XF46y3j4a1ALi7u1rr6RycfW2pBtRyhxW498o5YlFZz479xhe1pwyokuFmJ7WGQ+1fjRlsPKay0gcDofD2R/hwlGZ09wM9OmTRDQ6HdOmTUcyae3M5okiqlYkk8D06eKfmWntTSZtqloymcT06dMxfTrdsu9PHMjr3llc/NgXWLUjjgse+Zy5bRadcszGAOk4vrtsB1btiDubSaEAvPsb4G8TgAfHAw+Oxy8XnoCLAx8CAD6vnw6c+kdntnVnV+zyet3S3gunz5OII40S4JXwIghC0TnXq/OMvP2f+mKjKztGAbCshBGrOkTpXB7ZHFtxSt7+S7e2KummJHb2iZH44vUDmc5+4LM3HjCpUU7eiJMksaD7yg/8noHD4XA45QyvcVTmpNPA1q05AC9i5kwgl3vS8jfyjTRN9HQuB7z4ovj6SRPTpWkFpSkNK7a36djP4UVpBk+azWA/5EBe986CZZCOFrJTTj5fcJSOYGcMtCUzxl8WCsDSl4E9DaXfLXkF2LG46CO5DOw3+aF4tds1OIJ+kW3DWhjRdj5LMExlkscLKUoVCgWmhWhlW6wjpsihHiMKP3t1nhnQNaa8bktmUMWwcx7QCcIIIfwms3lU+h08JzMQpeQUKABYsb0VRw6pL/qexT4xeyBDg9WpUU6F2x23X0PMyWnX7vHAImI6prlvKeqqxrj4dDTk/jksv2fgcDgcTjnDhaMyp7ISOO+8ENat+wcuuwwIheg7g9DceIVCwD/+ob42wiziqCIsDqOm9jS2NifQuzZK2A/hH9IM7Cz7/sCBvO77A6RwlMzmlE5cdjAbA7ZqBb3xM+DrJ81n1ucQ4JS7AQCPf7YeLy9uxLLCAJyeZRtYqnUa2aeqSelk0hN8LyKayH2ZzOSZRJdpt0vMA+ELECV7eWxmcgUI/oDr84xe1M4pY3oqrxPpnGPhqGAQAcuqxpFRF9FwwAdBENetI51FZZjd7U5tLISD+tRg8ZYW3fFJde63qkHEKlXN4HM53W5dYzt2tCbRo9p+0Xw72ovXNa1059mJUU7k9cKpKMXvGTgcDodTzuxV4ejLdbvx6CfrsHhLC3a2pfDIZYcU3awWCgU8MHsVnp2/Ca2JDCYNrMMfph2EQfUVyjTNHWnc/tpSvL98JwQBOG1sT9x+1hhFzADEdqy/nbkE325uQdeKEC4/ciCumTKkU9fVKbEY8OKLQQDXUf/G6EZdj2AQuI7CtHzTl82Xdmw7ZECd8npjU0eRcBQMBnEdzQz2Qw7kde8swgEfUllv0hDCQVVw6Ug7E47sjAHDyIJ5j6miUUU3YPippdP0GAMcdo1y0G+qrMTSQgMADyKCNA7v20u344JD+zGdB0AUB2ZQo0ZGXvQI4eQlMjkmwpGMfG50W9xYC5kiSC5/Dj5PzjOCIKAi5Ed7OudJXRpVGPHm+BUEAdGgX0znc5iqZKbtmKVS2jnurbqeOe5qZyFMjelTo7xetaPNkXBkB9r9zTKK1Os6XSRRB9cHLfyegcPhcDjlzF4VjjoyOYzqVY3pk/rhmqe+Lvn+4Y/X4Ym5Dfjz9HHo1yWGP7+7Ct/715eY/fMpyg3BDf9biJ1tKfz3+5ORzRdw8wvf4taXF+NvF08AIIbYX/b4PBw9tCvuOucgrNjehlte/BbVkSAuOax/p65vZ+FBU7Wip2lA8c1uJOjH2D7VWLKllXdW45Qwd00jVu5owxVHDvQgPcDvmXBEHkHe1PEp9pB0n4rvWAq8dYv42hcAfrES8NkTObxut71iWytTe/JWqfCwxpFfEBAK+JDO5tmlkmk8Xq+EEUEQBVOfAOQL7qKBZIwaKkRDAbSnc662kZEO4FoY0cxA78wiC0fuo3ZKrbutk2XUsU2xz6ArnBmV4QAm9K/Fgo3NttfBSSF/eXt9u6mZKj2UxaVCW5C7s1KbORwOh8PZH9mrxbGnjuiOm04ZgVPH9iz5rlAo4F9z1uMnxw/FyWN6YlSvavzlwnHY0ZrCu8t2AADW7GzDx6t24Z7zDsKE/nU4dGAX3HH2GLy+aCt2SB2RXl24FZlcHveePw7De1Th7HG9ccWRg/B/n63r1HV1SjYLvPNOHk8+uRrLl69GPm/thBTU2H3LafN5YPVq8c/MtPaJvE/bWjmo7wTk83msXr0aq1fTLfv+xIG87iSX/N+XuPP1ZfhkdSNz2511s+401cFsDJSkqmmdt53LgX8eCRTyQFUv4JZ11KIR6dixr0FUvOB5j5wxL1K9yG2uCDusU8kEb+xr67OEA3Lx6gbPzjNy3RYWY8ioeHVHxrvUpYiH4otZKpmdc7/RVTrKKOLO7DagM7oLytTGVHFz0eYWz+cHqPto4+4O7+dF1DhyqnnxewYOh8PhlDNlW+NoU1MCu9pSOGqoWnSyOhLE+H61+GbDHpw9rje+2dCM6kgAB/etVaY5emg9fIKABRubcerYnliwYQ8mD+qCUEC9qB87vB4Pf7wWLR0Z1MTYFvxkTVMTcOqpCQDDAQDxeBwVFRWmv5EdO5qbl0QCGC6aRjwOGJkuiTjSfB/R1EBS7ScwfDj9su9PHMjrrseanXFMGd6Nqc2Ip8KR6qo7TdWxMwaK5tG2HXjsBPX9D94HIjWlPzKAFBm8ijjy+wTk8gXPUuG8TDMRBNFpbklkmHVcMqxxxLqrmnT2DfgFIAM0Nrdh+PDJAJyfZ4wiMYweCLDAi852WuR98M7SHZjQv85i6lLM0r7Nlp/Fud9tjSOriKaieXgclQgAo3upBcV3t6cMp6NZblrkffT+ip3MO+uVzItBVzV+z8DhcDiccmavRhyZsSsuRgx1qwwXfd6tMoxd8ZQ0TQr1mu8Dfh9qo0HTaWSb8jy0pFIptLa2Kn9tbaXdwjqfGlRX0zmORu2JDS3XiH9maCOOtGHmZk5STU0NaqxmsJ9yIK+7Fi8KlIYD3p3CSGf61YVbHNsxGgMlRabl7ZNNA09PBzLtgOADrv8aqOnjeP4rd7Th3aXbHf++BGnBWXcN06JGHLF0+NSt7tXyy6dG1sKXVtz50bGDAYjHFavzjGFUkBthQUklM4hSdRlRozwo0bnetSXFSKM1O9lfw63Gj9U+sUqbUlL5PIwGcjpGnUg7giBg8qAu4vyYHtPGnDxajWaPp7KmY8UtVRFVOAr4nc+A3zNwOBwOp1wp24ijvcndd9+NO++8c28vBgCge3egUKgA0Ez9G6N6FXpUVADNFKatUoKMupdUVFSgmWYG+yEH8rrrsavN+CmzU1gWNTZjW7O+yGyF2RjQ1gnJyE/E3/gZsH2R+Hraw0D9UNvz1TqlP/zv1/jyVycwLYAbDfkRT2WRzuaRyxfg97HxxpSualKx2YQHqUwCVKf52XkbFYfWFZptHvVE+CJS4aTtk/WFXZ9njKI8oh6mMnlpW+aGE4fh1pcXO66DZiaQmAlHts79BioGqxpHZvcB2hpAtm3bVGBo1ollHaL+XWMI+X1I58RaZqzOUXqM7lWNy48YgFQ2X9QwxA78noHD4XA45UzZRhx1qxQdHDlySGZXPKVEDHWrDKNR8302l0dzImM6jWxTnoeWW2+9FS0tLcrfsmXL3K9QJ2I34ogGbUqQcWtlXhybo8+TcxuwmHFtiwqik82SLWxtk/5LMuv9uE5m8sAHdwELnxY/mPL/gHEXMrO/s5WNcCdvlxgh2nkqLDA8p5BOaU4qzrSnI83MPqA66qqwwKrGkVHxbXbbR3vJYDEPoygPVsJIwSCiCQBqo2IquhepWF6n2rkVHmkEGKft6p2KOzEbUVSsGilEpO6YXqfj+XwC7vzOWPzpvIOV+mMcDofD4exPlK1w1K9LFN2qwpi7ZrfyWVsyg4WbmjFRepozcUAtWpPZImd07trdyBcKmNC/FgAwYUAd5q1vUp/mA/hsdSMGd6swrG8UDodRXV2t/FVVVXmwht4hP7Fn+XDN7xOK0oJKnQA5OoALRxxjPlvDtkD2oHq1BsS6xnamtknnyAtBVOt7Ddv+BvDJveKbcRcDU3/lwnapZ5dhXGw1QjhHTMUd6b+XqXCCAPzkBDGSi9Wylwg7ngsL4vnYy1QmL9OlVNt55D2qsN4pEVNOu6pZpao5FHW0mOkvEZfrYPcWw+v0Vt15kg+1TDrwcTgcDofDMWevCkftqSyWbm3B0q2i8LOpqQNLt7ZgS3MCgiDgqqMG4e8frMbsZTuwYnsrbnz+W/SoDuPk0T0AAEO7V2HK8G745cuLsHBTM75qaMLtry3FWQf3VlIyvjO+N4J+H/7fi4uwakcbXv92K56Y04AfHD14r623HVpbgSFDUqiuvgKXXnoFUinrqAE14sj69iiVAq64QvyzMk2mBZW2bRaH0huLtmrsp3DFFVfgiivoln1/4kBedyNYOwykOMq6OxaJU+eNdgwMEbbgnA1/EN/0ngCc/XdH85PRc0qzOTbOORnRyMq51SPGqKsUiV5XNdbLXtJVzSMnWbbf1p5wf54xiFKVHwi4EU6tCm8DcJxKBhCp2XoFrN0WmDa5lprZtnPuN+yqJtmftXgb/QLbJCbt38/X7baYkg0RishklqlqgLfnKNbwewYOh8PhlDN7tcbRos0tuPixL5T3f5i1HABw3sS++PMF43DNlMFIpLO49eXFaE1mcOjAOvz7yslFaVMPXjQev525FJc+9gV8goBTx/bEHWePUb6vjgTx3+9Pxm9nLsGZf/8MXWIh/PSEYbjksP6dt6IuSCaBdeuyAP6NZ54BHn10BsLhsOlv7HQlyWaBf/9bfD1jBmBmWowwyAAojWaqDIvRW5uaEigUCsqNdjabxb+lGcyYYb3s+xMH8robwfrm3cvuYUUt7R3aNhsDsvlu2IP3wzeLb0KVwMXPAX723R5ZdRUizy/RkB+JTI5tRJZcfFuJhvCgxpEgMBd2tA6vbP+LdU3oSGeVmk1u7WtrHHUk05jp0XlGfiDA4tgy6sQJiOlqXtQrU6KavEhVMxEkaM79VlfpPnVR5fXmPR3oWxeztXw0dwEVYXEdlm5tRTaXR8BP9yzRqbYTs3HMsYoKiob2nWhofs/A4XA4nHJmrwpHRwzpioY/nWH4vSAIuPHkEbjx5BGG09TGQvjbxRNM5zOqVzVeuOZIx8u5N4nFgFNPDaKh4V5ceikQDFo7lHZqHAWDwL33qq/NIAtLap/AnjexD+55ewUA8emx7BQEg0HcK82AZtn3Jw7kdTfCbT0TLaSzzjIyRYtTYcRsDBQKBVShA7PCv1Y//MF7QFUPx8up2Nb5LONBOpCXUTVetLPXil4A+zRE+cw4po/afnzF9jZMdNAO3gx526fzguvzjFFDBS+jNeT051Q2j13xFLpWsneSleLPjlvaG2M2fuyc+42u00cO6aq8jqecnzfNbgPOPLi38sCuI5NDNaVwpNi2qe54KeQZzlOqcdSRzhHRaeWZrMbvGTgcDodTzvCuamVOZSXw1lshADdT/8ZOV7VQCLiZ0vSPpgzGb2culWwX06UipLxOpHOKcBQKhXAz7Qz2Mw7kdTfCyzbMrMWLouLYDm2bjQEf8ngo+Bd0F5oBALd3ewB3dh/laD40MIs4ItJ3PClgLf2XRYtNTQlmtklY1+/RRhz1qoliQNcYNuzuYOIoa51eefukCj7PzjNqAWsGqWomzvrz8zfjt2eNdmhfKr6t853Sbt7l9tezbSaasjj3C4KAvnVRbN6TcLT82q6NevSoDsMnAPmCuI2qI96KFfL++PfnG3Dnd8bqTsNa3vayThdr+D0Dh8PhcMoZLhzth3jRVQ3QdFbT2A74fUrb245MDmyfrXP2F1i3VicjSFinNBVHM7lwOjbNB756HMgX23gP76DeL9Z3uytzCRYLxpGVdtHzGTOMahzJCPAmIkV2eMnUrm83NWNcv1oGttXXUUaiQgnEibc2GsQGeFucmUXElJHI4HWaz4ieVVi0uQXpnLd1oFJZsQC3j2HHCPfFsa2PR8/GqIQgCIiFAoinsrbmQbPsevTroqbbWaVvsrp/kbfhZ6sbcfjgrhZTczgcDofDMYILR2VONgt8800ejY3bMGYM0K9fL/h85uHkSvtjCvv5PLBNqr3ZqxdgZposZqr7BDbkRzqRL7oBzefz2CbNoFcv62Xfn9hX1r1QKODhj9dhXL8aHDmknrl9WVAEgD3tGaa2mYk7FiQyuaLaXQrZFPC/S4GNX0DvWXkBgJCO69qUt/TCrqfjsS1nYpQHqXahgA9ppfAwo+LYxGsWESlGkE7myh1tTIQjGbKwd4fRvrWJ3taNsNw+mo5QqnCUwZYtWwC4P8+UFseWahy5OLbUCNhSzjioFxZtbnEX0SS/0CuOTdRNSmRyqAjbu+VRoplsFt62c+43G3VM0jUthnUk6BeFIwfzsHvEnHlQL/z02QUARDHMbd0vO8xdu1t9qNZpc7XHvnLPwOFwOJwDEy4clTlNTcBhhyUA9AUAxONxVFRUmP/IRsRRIgH0FU0jHgfMTMfIrmoGXWZaEpmi6INEIoG+fW0s+37EvrLu7y/fqdSnMqs55hRZUASAgN+7W3bWaXBkNFOhUFy7S+G1nwBrZhvakNc2kyugcNytCMXUujf3vr0CazNdceIR3wdeXMQ4Ykpc9qBPQFr65ONVjTh1bC9mcxAE1TH/58drcMKo7qXbxwHyVvcLAk4/qCfeXLydXToZ8VpedsN96xByhHtRp0lGiaZJJF2fZ4wkRa9r0sQ8TiMSGzqIOBGOzDCLOGJ17o+4qCFGKxPLBdC9EH+1+Ii6Vh3pHPTif5xGMxlx/iF98d7ynUU1GsuVfeWegcPhcDgHJlw42mcIIEC5t+TbLh/lE3RauzQRR0DpDWiAdgb7IfvCum9r8aaGjAx5vz6/oYmpbRZ1iKiMo7h2FwDgvTuBRc+Jryd9HzjyJyUmOhIJjB8/HlvaCtj5y58iRDgC/3n7HcTzWZwtPXVv2N3BJPIFUCOxSGdpSzOb/Uw6dvI5YcmWVrzw1SZcdsRAJvOQYVWjRosAoWhfJjM518KRnsPLsr6KtlsmeT726jzDImLKLGqHxf5Vo0hKZ+DzCYgEfUhm8szHkFVheKt9QiOPeFFDTEssKC6nnTHqRtqJhfxIZfOW82Ml8/TvIp5z94WuasC+cc/A4XA4nAMTfoUqc7p3BwqFCgD0KT55G0/sKiqADKXpCBFxpCdK6d1IV1RUIEM7g/2MfWXdWUVaGFEs7uSxqy2FblVsOiiRQ511xzYtCbJ21zf/BT77i/h6/HeBM/+i+5sYgFW70rrfyVRF1NPw/IY9mDyoi/uFlSDba7N6kk/WOiZTgZZvb2Njn4iYlM8pd7+1AqeM6YmB9e6ewJO2g34fgn4BmVwBiUwOta4sq5CnRi+EL9l+REoj84Ui2NoUd3VMGQ0NOZXo83W7Hds2w6vOdkXzCPqRzFgLFXqYpdnJy96WzKK5I43amNogws6530woZhGxZtUkI+JCnHIickeDfuxBptOEHDJqTknjL9Pgo33lnoHD4XA4ByY8gXo/hOx6xJLiVLXS7zvj6SiHPaTz3xk0xlOe2PWyq1qR/R3LgNeuF1/3PRQ460Fn9qUDdUBXtZbP5j0djmyV2hb/B4iIIy+OS3LseJHORJ5zHnx/NXP7LGs06WkvTO1rZiAIAvPi5NprRk1U7bK1dpd+rS43eJ2qJs5DFL9ufnERVu1gI24CQG1UFYreXrLd9u9pdFw3wiOtTqy0q+8kISdiIRYy76rmIt2Pw+FwOByOCheO9jOSmRxe+3YrAPYFIKMWkSny93e+vpTxnDleQtYBUQsps0PrwLCNLiC6qjEuLq1d7kQ6B7RuBf55hPhBuAa46BnA7yxwU41mEHDKmB4A2EdeFAlHrBwnIjWIPCewWnYyJYu0v66xnZltbYFpphFBxGsvhBEygiTqYQ0lADhkgNofc1ebM8HXLGrHTQ0fLUbPSeTIrIWbmnHKXz+xZ9TkIUw05Mew7pUAgHjKebSj2XWahehh9fxIFtZeW7iV3qgLdYc2iorVgy/5GMnmC8hkWctSHA6Hw+EcOHDhqMxpbQXGjEmha9frcPXV1yGVMr95n7Vom/I6RhFJkkoB110n/lmYRnVEffq8ZU9pvZSeNREAxRElqVQK1113Ha67znrZ9zf2lXXXdh5ijRxZE5QKY7Nt3a6+ZltcupR0vAl45Fj1gx/MBiq7m/6GdgywjhyRBRK/3wPhiIAUdljbF4Ti9NiUh8t/8WNfoC3pLkVEL8KDZbSDUURTIZvBb275uavzjJG44/cJOKhPDQCPItY87MqnzIMYQ4zrLmPSQFFY024bVuf+qEc1vkgyUsfLjU32hVkn2o5yruukyOQicTvj7TXCLfvKPQOHw+FwDky4cFTmJJPAsmVZNDU9hP/7v4eQzZrf+LQSzs/pB1t3UMpmgYceEv8sTKOuQg3Nb9N5wnrLKSNEm3n17jybzeKhhx7CQw9ZL/v+xr6y7mRUihcpI/Jo8NoJYp+qpo5jP3IY8t7VQPsu8YMfvA90G2Fpw2wMFNXyYRz5Itse27tG+YyVo0bWCSGdclY1pkjnPkY4fSkW0XCajpNyxEtbMot/fLjGvX0UR0q4qR9jbF99HQ35Ucjn8Px/HvfsPONW/DJLnXZbPJys22WkYVhFyrrBKGKK7txvrWJ52ZVP5sqjBopL00nBOPI2azc6JhgvR9AvKE0C5OPQqu7T3mJfuWfgcDgczoEJL45d5sRiwDHHBLFx4+245BIgGAxa/wjAWeN6F0UIGREMArffrr52Q1AqxFsoALl8AX6fgGAwiNulGdAu+/7CvrLu5H26p917QgG0JrNMnaDiiCP2aXYyPw+8iJpd88U3p/wR6DuJ6ne0Y4Blyg7JQX1rMK5fLf701grmNUwEAKeM6Yn73lkJANjpMJXJ0L4gFNdQ8sB5JqMyd7W6Xf5Sj1cWvlhse92ubUE/BL8fR11wDU4c1cPxecas81lYSvW69ulv8P4vpmBIt0pH89BD7ujl5XknGnJ+m2NVTDlmUK/HzrnfLGqHhfBoJZHI9wnpHP35U9vhzw7yNvvzuytx/iF9DadjJe3ItcDiKbbXHi/YV+4ZOBwOh3NgwoWjMqeyEvjkkxCAOzyxHwoBdzAyHSDSYjK5PPw+P0KhEO5gNYMyp1AoIJ3LIyzVDNpX1r24M5kHN9aS/ZgH0RekA8NaWJC3y3f9s3F9YKb4ZsJ3gSOuo7ZhNgbIZTdyQJ1C1k868+Be+NNbK5hHMwHA0O6qiOC0Bk6JfeJ1hHHEEbldgOKIKSYRTSh2eMmOTqwg7XeksxD8QQw6+Qrc8f3DmM2DhByTX65rciAcFdeVIomE1MLMhULBdl0bciwa/VYu/uwFRimmrM79TqO97HRQDAXE7dPYlsKSLS0Y07uaej84EXd6Vosp7RXhzrv9jIZE4ejdpTs6bZ5O2FfuGTgcDodzYMJT1TjMCBKtvzM2nl7uL1z+xHwcfMe7aOnYt9rpkgLGG4tsFEilti/idSHfjnSWWct5QFzu43wL8YfgEwCA9bFxwNn/YGdfp+08a/GLtJ3K5pHPs9s+2lCJg/vWGEzo0DyK04y8iDiKFglT7uzrDT2WkWR6e+5nJw4H4H7bmI0KL+tYybYLBXbCnRafiyLLyjFq8L2b/UtzquqMGkfydbs9ncOZf/8Mn65u9GxeAHDORDHKyGid3EQzGdHUngYArN4pddUrz0w1DofD4XDKGh5xVObk88DGjQW0tragb1+grq6GWbcRQLx5bWkRX9fUOCt2KUMKR9lcQbJfQIs0g5oatstebnyySqx/886y7bhgUj+m6/71hj24560V+O1ZozG2D1sHnWRjE5t28Hp4Ua+DdL7yBRRFfJXQuhV46jwg2UJl+/BMFlNC4hPqtfleuDx7Gz6zHRFBNwZYp6qR20Vb/Nztk36tw/vPSyfix09/gy/WNSkpqu5moL6MEWlGLIQjbUpWpEiYYhRxRNYg8qL4M2G/MhJAoVBAa0szmpubXZ9nzDqfAezFO1KUWrS5BZMHdWFqHxDTKd9asp25XUAdn9r9a+fcb1Zvh4XYbjUe5IgjmY9X7cKxw7uZ/saNPk8bRcXyVuGXp47EXW8uZ3aMe8WBdL/E4XA4nH0PHnFU5jQ2AoMGdWDcuDp07VqHjg62jn1HB1BXJ/65Ne33CcrNXiafl+x3oK6uDnV17Je9XJGdK5brPv3huZjX0IQLH/mcxSIWQzgBXtxYy866XGuEbapaMQs2NutPuG0R8JdRwM5lQOsWqr9wQhSNEoUQrsjcAiFgX3AxGwNK2hRRy4d1qqAAIBJgGzGi7cA1qFuF8t2K7a2u7cuIxbfVSxTLYCkZUuRyHXGk85ksLHgRLQVIXdUyKbz7yzPcnWdMti1ZB8rJsUtG1mkJ+H0ISQ8cLnjkc3xmM9qFXGwjF3t072pbNu0gj0/t/qU599MMZ+epavTThvzFt4G25uVA2PAiZdmKyoi+wFduHIj3SxwOh8PZd+ARR/sZndUZxYigz4d0Lq9EHB2IeOEkyk6zYScaFxQVx/agXbFsP+ZRAWiSuWt34/DBXYs/7GgCnjhdfX/iHcDg4yxtzd+wB3e+thQNQl/ECyHUe1R8W4DqTLEaO2S6h88nIBL0IZnJe+KsjeypOuYs7JPLHmHcEUt7VrpgUj/MXCimZ7KrcaQ607KwwESwU9KmCPuMt49u5zOynbkH4ycS9CmFmV9ZsAVHD6tnat/NNiqo6q6pbVfj3kR7Yd1tUY+gRjhiWY9LDzK6Uq+ulRf3MNpUYB7Hw+FwOByOfbhwVObU1wPxeAzZbBqRCBCy6BAj34AH/XS3RrEYkBbT/+EgoKKEgF9AOqemqsViMaSlGQRYzGAfQI7a2VfWvbgzmZdd1Twojq1xMlJaBz2TBP4xCUhLtS3OeQQYdxGV7XjbTiwpJFAVDgDJrCNRx3QMkOlkrGuZaKI8okE/kpm8J+leADCyZxVWbG9jIiyQAglrYUQ7XnrXRpXXKZfRdqY1jjw6rmIhP4RgGP1velV8H4s5smNWV6YmpnZ3clTLR/pvlJIVDfnRmhQF62ze3j4ga5oZBb9oxcdVO9owvEeVrfkYoURRarYLq3O/fM5csb3NsQ2rOwFtqhrNPnaVqqYpSG8sDrOTd9SIzvJucb+v3DNwOBwO58CEp6qVOT4fUFEhoKYmiHA4aJnz3p4Sb4yqKOuYCAIQDIp/LNLp5dSP577aKNkXEAwGEQxaL3tn8MW63djekvR0HspTzTJbdxoSnqSqif8jXtQ4ktzScEAnsiOXAZ44DejYLb6/7BVq0UgyDqC4NpPd4ttmY4Bs9c26xpEWo1osbiDXxov6VaJdb5wneV+QwlRbklFRe50aRzvbUsycVnIYRYJ+CIIAwR8Q/1yeZ/R+TnbO8yKaktzHXkSqkkIFAHy5bjf1b5Vj1Mi2gTBIc+6nOZWQ9cgWbW62/oFsm3rK0odMds4RTkZbUbH1Tkod86TWmAfsi/cMHA6Hwzlw4MLRfkab9OTWq1a3F0wSO6KcOqan7veycCQvRznxVUMTLnr0Cxx+9/uezscL519bh4IlXra0J+1XeJh2URLNVCgAr/8M2PqN+P6cR4Ehx9uyKS+37Njm8gUloo8lAgRlHqy2jTbKIxJkmDKl81mUYaodmR2kjTjKuSx0pP01KSpsbUmy7ToHoGtFWHn93vKdTG0DpaIIy66CMpMG1CmvnYhfarqX/vdkxImbbpyGEU2aMcTy/Oy0BhGJmTwwvLsaGbV5T8KZfQv9oVJzr0AVceSi85nfJyhRTh068/IiyV17fuKaDIfD4XA49uHCUZkTjwOHHJJGz54344YbblbCmI2QI47kYpBWpNPAzTeLfxamAQC/nzYWz/3wcPz1ovG63//w2MEAVAc4nU7j5ptvxs03Wy+713y1YU+nzEe+OWW57rLj7wWkr+lFKL9s3yitw51x8Z8SUSPbXvQcsPAp8fWEy4CDL3A8i6KuUjbrHJmNgaLOZx5FHCmpah5EBJFPxL1qGx4JaeqvMFp+eclZigp60Sk1sSBqomKqV0vCeURTUUoW8Xk06Echl8GeD/+FPR/+C/GEs2hKs7bzg7tV4oSR3QF4E5EYJc5tdoUjGpFB2+UvwbBWmdFxRXPupxFffD5B6XBmJ1rGjoAoCAJuO2OU8t7OMeZUgJGPO7PoX5bijjy/TJnXXiyn+yUOh8PhcLRw4ajM6egAvvkmgx077sff/nY/Mhlz5yMuC0eUEUeZDHD//eKfhWkAQDjgx2GDuxrWJdA6wJlMBvfffz/uv9962b2Gdb0UI+QaRyzXnXWRYJKi4tgehvJ7IS7Iy648UU7ngDXvA6/8SPyizyHAmX915IXIvlfILyAgOZ92hQWzMVAUWcOwiDJQ6jgqxWEZ1iAqss8wFU6poYTSSDu39rXbxU2aDi3HS6KLF0WHReEoh9Z5L6N13stoa08xnwcAnHeIGGnqxTqEia5/WS9a52mwc4yZdYQDjItX2zn3W52aOqOpQFHdoU5sWf/8/E0ln3lSHDvUOdd+t5TT/RKHw+FwOFp49b0yJxYDJk4MYsuWm3DhhUAwGDSdXhaOKihrgwSDwE03qa/dok0ZCgaDuEmagdWye42XUTsk30q1KFiuu5fCEUnSixpH0v+KsHcOkDzuunasBZ6SRKNYPXDFm4Df2WmO7KgUDfrRlsrajsiiHQOsiyiXpmSxrHFUGlkTZZgKR6Kt88Es4khgb99IZFAL87q3LdpXZ+DzCRD8flRPPhcAkHX4LIi6e5ij4tjmdYKuOnoQPpfqDtmOOCIHOqU2nGAYVSlvl1Q2j1y+oEQ3sTz3F4niDjBK4SuaB3F9WbnDuhC3W3FnTO9qzF2721QoZJlNFtMIRzTbZG9QTvdLHA6Hw+Fo4cJRmVNZCXz9dQjAfZbTLtnSgk9XN4q/o0xVC4WA+6xNU6Mt8hsKhXCfjRkUCgUs39aGQfUVzJ8SkuKLXhtgVqzb1Q7A/rqb4WW0FBmFkc7lkc3lEWBZU0lOVfMi4kha9mjQjxiSuLXxl+IX4WrgJ18BwQiT+URDonBk13E2GwNkZI2caqd1QFkhCztLtrYo0SNs7bOvcST7dt/efjLG3fkuAPfClJW/64Wo6VUaoozgD6Ju6lUAgJzgzXnCy65U5LnNZlM1amqiQSVV0FbEkcX3pCCRzOSU2oI0535a8cVJ4Xy7uo72+rK1OVHUcdAIpwLMKWN6Yu7a3UhkOqcWYs9qNtcBr2F5z8DhcDgcDmt4qtp+xJ/eWqG87l4VNpnSO9w6SY99ug6n/+1TfPfxL1kuFoBi4SiV9TYcn3WR2gjhoKzdFWdqW7ukXnf2clPrRYsSzRQU8Fzod6jLS3WsLnkeiNYZ/s6ObQFsiz+XoCkCzUR8UaJfRMfOJ/2fuXArQ9vqZxEPCp/LTmlNNIg+khPLKpXMyN11FRWk2C62zkJUoz2bOBV2rOrtqOtg/7xpne6l3obMa2iyZ5tYbrPnAJ/cPBUXT+4HwFmdJiOBJEy0snd63rQSXzqjI1hYE43L8hyth1GKH+Cu8LYRgiDgO+N7M7fLAdAwB3jmQuD+EcAdNcDyN4q/LxSAD+4C7h8O/KEH8O+zgd1ri6fpaAJe+gHwx77A3f2BmdcBKc19zvYlwL9OBX7fHfjLaOCzv3q6WhwOh8MphQtHZU4+D7S3F9DSkkEqlTEVJOQb1xNH9cD4frVU9gsFsbZRJsOmtoD2hrBQKCCTySCTMV92mdnLdgAAvvagkDUpHHndBjiVzdtedzOGEy2xV223TiVwA2vhSHYEZAdxe2sSG3d3MJ3DDbt/h4N8DeLbcx4FBhzBzDrZ3ctuYV2zMUC+Ix3QT1fvcrysWmSX9BSpC6JXkWuxoKY4uQv0ayiJy72rzWUNHx3bRw3tqrz24rzAOlpHKzMUCgUUclkUclnXaViGbec9KK6u2A56H/hcEwvi4L61ABiLm1IaKwC0EmILy3N/zK1oTREU5PcV3wpa7We3two0D5hYBwST0WHl2lWN5bjpNDIdQI+xwBn3638/56/Al48AZz4A/OB9IFQB/PccIEMURn/5amDnCuB7rwKXPAdsmAu8foP6fbJV/E1NP+BHHwMn/Q746E/AV094uWYcDofD0cCFozKnsRGorOxAbW0IkUgIHR3WDvf5h/SlTsPq6BDT1UIh8bVbtDeEHR0dCIVCCIXolt3LWj5k9o+XKSmA6JzYXXczAkQRX+bLrrk/tds5zNK8ZH9IN1X8WrylhZntmwPPYVz7HADAP3yXAuMuZGRb3TBOnX+zMaB2shLgIwYni22jdTlG964GwGbs6EXWyKIgy+LJ5ClMLk7+5Nz1jGyrxv971WEYXF8BwG2NIyn1UFvjSDknOj+uzJzIQiaFjfdPw8b7p2FPi7NoRCsf1U2aqVEklmKbUUqy1RVPXge5Bh0NVtFSgFpg/akvNiqf0Zz7aWUBs+gcI+xqDn7NCtLOy21XNS+69BnRWXUC3cDynqHTGHYScMJvgFFnlX5XKABf/BM49iZg5BlAz7HAOQ8DbduBFVJk0q6VwJr3gLP/BvSdJD70Oe0+YMlLQOs2cZrFzwO5NPCdGUD3UcBB5wOH/Qj4fEbnrSeHw+FwuHDEYYt8k7thdweyNgudAqV1iFhCmvNCOPJamPLKtjY1oINx3QnZeiToxxSltbSNeaTbxSeO5N/cfwBPnolfr7sM1wVeAwC8lTsUj+TPZr7cAryvUwMAP5oyWJwHA+FOK2J4UV+K9NS93j7DelQBAAI+d5csvTQYn09AvZTa60U6kBPH3wwzZ93tPCy7h2VyyDPufOYmCs7OJUKuP7SrLYVNTeyc8vpKcew4TlWzEF+c1Diyi7ammtcRuWYFv70KtOmsrqocgj0NQHwHMPg49bNIjSgQbZ4vvt80T/ysz0R1msHHAYIP2PKVNM18YMBRQCCkTjP0BGD3aiDBPjqdw+FwOPrw4thlTn09sH59DK2te9C3LxCLxZjaj8WAPXvU127pUqFe2D9d04jjhnfDHmkGNMtOCkff+9c83HPewVRFOu2yYGNzUQQMC4ra2mdy6FVTYWvdTW0Txn/9yhKcOKoHejAq+Km9UffSabCVdpHLAM9cAKz9wHCSHtL/pnBf3NjyY9tdmWgg01HsRqTEYjHLMSA7jnInRC8cRNL5dFsYXs+xi3hQh4VcwtPH9sTr3271THxhKXwZ2faqxpEQDKPfDf8DAORJx4ohpNOdyuaZNi7Q2vKiODwAHDlETUnc0pxAvy4052RrFePiyf1x15vLi/YvzXFP+2Ak5rIrH82hTl63AYpUNZfijhLBafKQgnXns6JUNaaW2UEzbjqLtrY2tLa2Ku/D4TDCYZu1M+M7xf+V3Ys/r+gmCkqA+L+iW/H3/oBYo5Ccpm6AxkZ3dR4u6xlyOBwOhw4ecVTm+HzAwIECDj64Fl261DLvBCYIQG2t+MfCdK8aVeRpiqchCAJqa2tRW0u37JGiWi+N+PfcBvcLJUHe637GsI6MYp+MaErnbK87rW0AeJLhdtHCPKKJ6B5m6qB3NAGv/QR4/nvi331DTEUj9BqHf/X6DX6Q/gWeOuR5JBBBNl9gJh6R29xp8WeaMSB/6kVnMtl2cfcnNtuHXJsYI9HLyJmOMKqxY+Twxrys4cO8xlHxOBIEAb5IJXyRSsdF/63SyYrqw9ncRkYpfDLaSBDnkTvm59iKcABjpJRNu8eYmeWIzv61c+63ujQ4OS/YLTA9omcVfnHScOU9daqarbmoOK0Z54Z9IVWN5T2DW0aPHo2amhrl7+67796ry8PhcDicvQ+POOIw59QxPfH20u2OHACf5mZpp9tiuASdWWzS63SyHS1Jgykd2NbWOGK+7BIC6WRp5tHRJHZdyet085l4OXDavcWfCQIQCOPrZ77Be/ltmBxRo68SmRyCfhaauCp4xeSIGkbbRm8setnyXOv4u4kY0XNKlRpHDMcO6TixToXTumSqI+t825M1q3Rtu6pxRDedV9GCfp+AUMCHdDaPdbvi6FLRhZntoL94eyXSOVSGvbk1iSgiDDvBIuZxvR63qWq08sNPThiGFdvbMGvxNk/T4gD1mGiMp7CnPY06IuLJq6s0yyi5A4Fly5ahT58+ynvb0UaAGmkU3wlU9VQ/b98F9DxImqaH+J4klxVT0Cp7qNPENdO0G0QzcTgcDsczeMRRmROPA8cem8bAgXfgV7+6A+l02nBaJ8JIOg3ccYf4Z2LaFmRNj3Q6jTvuuAN33GG+7DJap5SlI0pazjCu0yHaV206WXdT21pxJ+vNdgG8bfsc03OCMgngidNU0WjSVcDp94t/l74oFs0MRor/AuGihQ/5fUqNKS87Y9kt/mw0Bsj9KQskTAvGKkV9Rduy4y/aZxO1Q2q8EUY1lIxOYaxqNBnZVyPK2Dv/UbddsbRolIBelQE0f/Y0mj97Gq0dCUcmaYpAp6VopjcWbXM0DyPbgiDgnvMOUt7biqwhjyOK6SNBewInzXbROzewPPdHHaSqOX1GQi9Subt+yjXFAOCDFTt1p/G2q1p5JquxHDduqaqqQnV1tfLnSDiqGyiKPus/Vj9LtgKbvwL6Hiq+7zcZSLYAWxeo06z/GCjkgT6TpGkOBTbMEdPXZdZ+CHQdxtPUOBwOpxPhwlGZ09EBfPppBhs23Im7774TmYxORIZEThJDtE9wzchkgDvvFP9MTNuCvPnMZDK48847ceed5ssuo73hdZp6oW9cfemkcLeleU3xbbvrbgeWT8y1guNXDWyLTZKRGLoCzKxfALtWiK8vfEps2zv5avFv2ElU8xAEQU2XYiQckU6jLFzsbrd3M29nDDgVp8wg/SMWUTVGyNverehIjsTiVDjGqWSCUVSQmzpEFl3VXGwbs9Sjf18xES1znkXLnGfRnmAXoanl5NHi038v6ohdeGh/pc6Ol8J1JMBYxIP+2LFz3FvV8rFVF84lcuTgjA/WUE3vVH+pDAcwsqdY8L7dg/ORHl0qVOEjnuqcedrFy3sGz0jFgW2LxD8AaN4gvm7eJA6Qw38MfHIfsOJNYMdS4JVrxOijkWeK03cbAQw9EXjtp8Dmr4GNXwBv3gyMPQ+o7iVOc9B0wB8CZl4P7Fwudlz78mHgiOv2zjpzOBzOAQpPVStzIhFg9OgAtm+/FueeCwQCxrtMFllCAXo9MBAArr1Wfc0C8kY6EAjgWmkGZssuo3WP2EYcqdazOS8ijlQSafvrTmtbtu8VH6zYiTvOHsPcriDoPNH+6l/AwqfF14dfq9/S1wTSWY8E/YinsszEBbLuS1ha7qe/3Ii7zjnI+EcajMaAnkDCMh1LT2iIhfxoSWRcR9Xo1cORl31Lc8KT4sbMIpoMBBhFmPLAiWVe40izaQd0q8ZhZ1yMpVtbkc473e5qWqYRhwyow7vLdrhImTJfNrfjn0bEiDioF2SF3v5lee53Kzzaia7pKokrnRGQM7p3NVZsbytdL49Syo8iiqOXKyzHTaexdQHw7zPV9+/8Svw/7hLgnH8CR/0MSHcAr98gRhb1Pxz47sti5LDMuY+JYtF/zha7qY06GzjtHvX7SA1w2SvAmzcBj0wBYl2BKbcAk67slFXkcDgcjsg+cmU6cKmuBpYuDQOYYTmtnEoQDtDn8ofDwAxr07aIEalq4XAYM2zMwMuII9J22oOn5tBEHNldd1PTnZiqFvCgoxEg1Qoi0y6WzQTe+Ln45eCpwCl/tG1TjWZSn5Y3NLZjVK9qBkuscuSQrvjb+6sBiBEXtDWUjMYAGeUlO2ksBQZyu8iwrhNEUhMNKq8/Wb0LU0c4qzuht12A0nbwPpdjVPtrFi3PrdLsWEYJkoTDYVz0szvw1/dWI13wro5LzGGBeFodIOrAvt0i0GE5VY3yumJVNBzQ3780537a7cJibNJywaH98OD7qy23DwttR95u7y7bgemT+pV0dmMtXgWY1L3zFpb3DJ3GoGOAO1qMvxcE4Phfi39GxLoA5z9uPp+eY4Gr3na2jBwOh8NhQvlfSTnUOIk48gInDoCMlzWOSLyJOCquceSVbdb2ZSeAVQ2cYtvFyy07Cz3jS8XOaQDQfTTw3Zdcewp5ydd56ZvNruzIFFSvERP61yqfe+XAeS0wsHJA9bpk9axRnx7vamWfLqVtB+8UyxpKHmx72XY6l3ecImtVy0cWdZ7+cqOjWnc0P/FawGDZVdAILyOOvIqUcirYOUEZq9m8kvpuhlUUGc28vt6wB+f/c65jO07myeFwOBwOxz5cONqPSCkRR3t3t7pyMDzs8EU6R9m8N865jNdPh5nWfpI2uhctybVFoKMhP2JI4qfbpaePkRrgilmAz9kNvSruCDh6aL38xvHy6iGguPg2ixpExalqUnFshtuf3C4yUcbpWFqH94yDxHoU7uoEEfZ1UuHc2ldsa5adRaqaWhNLUz8pxHbZ9Ti4b63yus1F/RaaItC2I44Maj8Z2XdaBJpGxJBrHM2iLPBd0Avd0yCPTbuRgrTRUm6FRztnw1gnjFW9ea1rbFdee9n7lHdW43A4HA7HOVw4KnN27gQEoR2CEEQwGER7e7vhtCkpfclOxFF7OxAMin8mpm0h3xC+9u1WxONxBIPWyy6jvWlsSbArEFnUVc2LiCMyVS2dQ3t7u611Nzde/NaLp88xRnVkSLS1fKL+HD4I/wLVeSm0/XuviWHqju2rtVmOkGpYsBIbSceOLL5tx7GlGgNyqppDB9QMTwtMa/AyIsXnExRB3M32MTrqWYp2WsIBnyKasDi2tMJUe3s7jhnRExvu+w7y6aQjYZPmbOh1RJCXqZTKPKR01tU74wxtqpGCeSlKh+W5nxQ75q1vovqN01Qy8qGT2VhlcfWMaEQcbaScm2gmI8o94ojpPQOHw+FwOIzhwtE+QxbZrLHDNOPDNWhLit/bjTjKZsU/VgztXqm83taSRDZrvuwk2pvHxngaCzay6fJF2l64qdlRSoepfeK17PzYWXda2wDjGkeScdlBSVGmKdgmn8PkT7+PnoK0Py9+Dug9npl5WbhoTbIRG7WtuJ0KI3pjQG/o6TmgTtGLZlALTLOJVtM6drJT7qYrVkGrNBbZZydcaJdd3jZz1ux2PPaJzMbieQmq6PXMvI2ObFuRzWaBvLhd3Gx/01o+DiKCABs1jhwcX0XDhUJnOP+Qfsq0rM7/RmmUrM795PV8waZm1/bMEATBXjFuF9qOVsRhG0WrTyRY/re8rMYNh8PhcDisKf+r6AFOly7Al19GMWvWZjQ0bEY0Gi2ZpjGewn3vrAQgRhvVxkIl0xgRjQKbN4t/OqYdcehANYKk4A9i8+bN2LxZf9m16N3KL2R0s6y1zTrqiHREEpkcotGorXWntQ2w7jYnIkfUsLRPLnfso9+iduc8AMB9oWuBEacysC/+FwTVsV20uQV3zVrm2raMmkpmvwaU0RgojmaSpmVUxwcoFb1I++5rHOl/Lo8fVmOnJJ2MgfBltOwDusaU18u3tTq2b4RcTL014cwhNKtxJI+xg25+FkIw5HkNIs/se9jZTqa+Urw2Fgp0x5iRGEgS0UmjpDn30+pWgiDg4smi4JV2cF6wWzqOJjKRhegW00QcyUKVR03VAJR/qhrLewYOh8PhcFjDhaMyJxAAJk/24fTT+2DAgD7w+Up3GXkz+cKPjkBlmL5Zns8H9Okj/umYdkwvqVhuKgv06dMHffroL7sWvZtGVoWstbZZO0BFEUfpHHw+n611p7UNsC2grEQcMa4jA6jLfUvgfwh//SgA4I3c4XixcDxT+wKEIkfksU/XM7Mto6Tq2Ii4sDMG9BxQlrBqOW9UsybCINXRrO5LRIl4cS8saJd9ZE+1C1+7wxpBekXDZX54zGAAQCLDXhSRx1hNfXcIgs/R2KGq5eMy4suqLbwqHHkXeVJ0jmOUkuvXSaO0c9xbbRdAPbYylDWO7Hab05sXzThyk0wW0UQclczPg+ae5Z6qxvKegcPhcDgc1tArDJyyRb5FDAV8GNevdm8uioLTp9N6t7sZjwpZJ9K5ojbiLIm7KFC7txAEMZQ/mck7d6p2rwU6dqs283lc738F1wZeAwCk64bh59uuRcTvXfcqVmiFgKiDGkfGttXXsn8kO6CpbB4d6WxJe2pb9hXbqvfVWV2xWGwfQCeyhsnyGzvUY3pXY+nWVk+2j5tOk4B+hFrJPBwIm3Zwun89TVWzGZ4S8PsQ8vuQzuWRyORQZ2lf/E8jeqWyec/qP4WkiDVa4YjEbq0geazGk95eww4ZULz1vS7GDZSKVRwOh8PhcOjhwlGZE48D06en0dDwIC69FLjllhsQCuk7lE4e0KXTwIMPiq9vuAEwMG0b+QatJd6B++57XLJvvOwyeo7AvW+vxJFD6jHetSimaWnPOuKIMD972Q60dSTw8Ix/AKBbd1rb6mcFqqfVlraJ7RILBZDMpM23TaEALHgKaFpb/Pnmr4CGT4s+CgC4idDmdl/2ATL3fgowS4UT/4uil7dOQTRoP1UtnU7jQekAMxoDgqbzmVcOqOwQtqe8SlVzX4PITAdgWZxZ76hxa98srclL0U4eY1vmNqAw9CRHwh1NShZZQJzVuafIvsfFt2UiQVE4YiVwAuKyNyOjREvRHPd2NC851ZE2Vc1NupdcB+ifH6/B0cPqdadhEQPcty5W9F5NVfMuV+2k0T3w6epGjO1TbT3xXoBm3HA4HA6Hs7fgwlGZ09EBvP12BsAt+M1vgJ///NqSmwk3N1qZDHDLLeLra69lJxzJTmR7IoVbpBlce23psmsxWpMfP/U1Pr/1BFfLVJKq5tGTeZmNu9psrbsZetsllc0zEUuK6gQZpRsVCkDzBrEA7xs/B9Z/bG60bqDys41NHWhCNYbe8DqiETGFMZMrIJPLKw6Ri6UXlx3e1a/Q1iCy4/xnMhlbY0DrgDrFrMbRk3MbcPtZoz1z/FmJI0Zt7VkU39ZbdRb2jVDrxjjbr2Zt58kx1u/nx3seUSbXB6I999CIUoC6/bc2J6iXyW5xbEAUx1uTWaYClXbs2znuaRZb7pSa9qAbqJb6yjAAwE+RKuX2HHL44C74Yp3YKU47bj3IVMNlhw/A5EFdMKBLhQfW3WP3esHhcDgcTmfChaMyJxIBBg8OYNeuy3HWWUAgULrLzJwhKwIB4PLL1deskJ2AdAG4XJqB3rKXYHBfvK0l6XqZtKZZ1hshxTuxYw+Qzttcd0r7Mol0jo1wJP0XIChPm0ucz7d/CXz5cPFntf2BkWcVfxaKAYf9GKjoCgBIZXKY8pu3AQCLK7ohQghFDY3tGNajyvXyy8Q028JtVIRa9kUujm0/3SgQCOiOAaNix7IDunhLCw7qW+NgqYshbZMRe+lcHuGAs7FjlL4TcZmOZYXnxZnd2jdJa1IFWfbpP/IY+2xNIzI+v8MaR+J/s+OFTAVNZtice/Tsv7tsByNR2WA+FMWfZeyKXnKNI6PjXs82DUE3qWo2T4EXTOqHj1bu8rRIucx/v38YDvn9bLQms1i4sRmHDuzCJJrJCEEQiuqZlRs044bD4XA4nL0FvzKVOdXVwNq1YQBPWk5rt5YBAITDwJPWpm2jFPNEAE/amIGcNnXzKSOwqakD/5u/idkylUYcsS8wDQCV4QDaklnkffbWnYafHD8Uf/9gDQBQ1eiwA9mZrMj5n/t3VTQKS2JGz7HA5W/YrqhOtpZ+a8l218IRKZpWaIrCJzN5plFI0aBo345jHg6HbY0BudPT7GXbcclh/W0tXzGl7tcRQ7oqr5Np58KRjPZsIwt3HYxS1UpqHEn7csYHa3DpYQOc2Vds64g7HgpfERtihR5mzrQ8xq59+mu8uXi7Z8IdWR+oI51Dbcz6N4B50XCSE0Z1x+/eELshtiYy6CpFvpjbVl/TXv8iDms1mSGLXk9/uRHHjehu67inEXaUiCPaVDWqqfShEdZYZZMF/T50qwqjNZnF+yt24OpjByvfsY6I3Bewe73gcDgcDqcz4W0b9gPcRBx5RcxhgU15XaojAXStLA7TdtrtSLHtYY0j0nLMg+5Asv36yjCqI/YFDFPbhBcQ04ojK98C3r1NfD3qbODWjeLflW/aFo0EQYAgCDh2eDcAbGqZkEJAKFC8PK7bzmu6h0VDUjQWi+LYBsWOzx7f27VtQP+cEPT7EPCJH7jZNkY+o9J1yyPholYqZO/zeXOic1/jSE2b1BIzSgF1gHFxbOfnBVodwDAikQEDulYowrKXhZLl8/OzX260nJZW9MpL07UkMu4WzoCQX1yA5dtaPbFPYpiurAOLI/HciX0lW2V0A8PhcDgcDqcELhztR5TTbVdEimb409srbP1O0TAEAQGNMDHm9nfwv3nWN/uWtiW2t7pPf9MjFmIr7AAo8uxk+3va0+zsQyowTUZdtG4FnpfyGOsGAec8bPJrffQiSMb2FlMFvO6i41o40owXlsV7jSIlRvfydttoU2qcYORMsygAbdY97GIpAstd8W3jtvNe1jhy3VWNIsSDhbBJm5LlZB4016eYG/uUF8CslO7VnGB3/rxu6lAAYmouLXZqE4alY2v1zjg27G63t3A2sSMcsWCEFHUqnzc8rI3N4XA4HA7HBVw4KnN27gQEoR2CUIuamlq0t5feNGojaezQ3g7U1op/OqYdM6xHJQCgNphDbW0tamv1l10L+dRer4bGL19ezGwZH/1krfVElJBOgHzj3dTSamvdTe0T0S8B6enzk3MbXNnUIkBQOof5WjYCfxkF5FKA4AO+NxMIsSko6sY51KIVAj666TjlO9Y1OqKSYGdHWGhvb7c1BlhtG6OULC/rBMU8FF4A7x1at9uGpiOcF9tdHmN//u5RyKeTnnYlcyKe2rk62d5ODi591x8/TJoHu4hQreho57in0bumjuiuvN6wu8NyejcNM2I0qWqOrZdiJEaW04OwzsLu9YLD4XA4nM6EC0f7DC1obW3R/YamsKmp5RbxjyUnje4BQKyl0tLSghbKGZApNtMmsEnbUWxr3leE2JX40ktVS2Zyttbd1D6hj/SpjZbMk4VtQHTcIkjhnE9OUz+8+gOgzmlNmdIIEpatybXFawfWV6BXjdi5jV1nMqk4tsPl1hsDRt2gmAsMmlNCNOQ+asqoYDCLiCyzmjXktnHqGJsVO2YVUWbWsc2LGkeAOMaS7W0AnAl3tClZsnjaZjMFWbRtfX3yusA6QJyfbczDcrvoHLdW5347I7hbVRgT+9eWzMML7JyfWaTHuz029jdY3TNwOBwOh8MaXhy7zOnSBXj77Si2bVuFww4DotFoyTS0nV/0iEaBVavU16xQnDD4sXLlSgiCoLvsWshIiV41DBcIqnMUDviQyuaZPpkvEl+kG+GcEMQqaePSrDsVgoDzJvbFl+ubmDlXZDRTNOjHfcFH1C/PfwLoPcG5bR0hwMsixIAHbeEVu/brr0SjUVtjwG0RZRkjYUWN2mEXbaHYDhULO6yL28r28zbbwdu17zRiykwIUEWpPPL5gqs6TdrNKo+xZ77ciH8t7vDUAZdrEP3jwzWYOrK7xdQSNhQSu8dukTBNOQ+WwrWMVpC1c9zTHidORV+7h6E6H/djlWp+mv1xIGeq2b1ecDgcDofTmXDhqMwJBIBTTvEBGGY4jVndDit8PmCYsWnHyA5wAT70HzSE2snTFvX9w7SxuO3VJUyXLRbyI5XNM02pIR0YOZIpmStgGKONqydMMROOCNsnNP0PJ/q/EN8c/XNg7LlM5kHCUtjRi7ZjUccHKHVgnGx3n8+nOwaMhB1WhdUNo4JYbBuDCEdFMM0XkM4569pmFIkFFKeuOm0Hbxadye64MrYNAMlsTqlTxgJ5jA3YGYCwZJmr48pKZOhVE8HCTUDI702wMss6YlbzoDn/q5dW8w2jTaM0Ou7dYCdV0434EiPG6s62FHpKEZxF9hkWIjJar3Jq9tFZeDFuOBwOh8NhBU9V248op/usqMbJo6e4M9F3Dx+AqjAbJ0u+1/WkeDWBk1QIK8jaT17VSzmi40OcuGUGAOC93ATghNtd29QTApjWOJJtE5+xcj616Ttyms4HK3Yin3fnOFmmqjGuz1Ri34OxX3TMexDRFPT7EPS77wpnhJc1jiKEiOZk3NO0nfe6qx2gdsCys43Mus1pcRP1RRu5E2OQrqlFWW47aZQ2TyERG4IXid1uZeRYfXXhFnPbjFPVWApSHA6Hw+Fw2MKFozKnowM4//wMJk6cgQcemIFMprjd7+Y9Hbj8iXmO7WcywIwZ4l+GYSdh2ckr5LJ46KEZmDGjdNn10Gsj3pZi40TLDgyLOi8ltnUiguKJJGbMoF93GvuC4E3E0URhFa7dfTcAYHF+IH6QuQkutRHJdqkRL1JFSLyqmTG4Xi0OvnZXnOo3mUzG1hhgUccHMI6sYeE0GwkBQb8PASmt5a0l25zZtlhnp86zYl/639k1jnw+wbNW9vIY++Dlp1DIZR3WOBL/W4kMLEVfPeR98NmaRqrpnRwiXggV8nIXpDRKO8c9rfZCU7Raxs1q+XwChnQTz3XtjK69ZsjHdC5fQCZXOKDFI7vXCw6Hw+FwOhMuHJU58Tjw0ktpLFhwPW688Xqk08UthGcv24FNTQkAQO9a+znx6TRw/fXiX5ptd3dEgn4Ucln8+uYbcf31pcuuh143qJ+fOJzJ8sj3oxXSDXgmV0Am50Gtl6AqHF1//fXU606DAIGt8NKyGUd98SO8HL4DAFCI1ePi9G0ABKSy7reNXmQNy+5YekV97aSimNqW/sumx/apUb6jtZ1Op3XHgFEECVnHJ+3B2HQrvJDoCSRZSW1cz6BluJ59L4WLiItoF8C6u6WrcU+OF812kcfYo3/6NQq5LD5ft9sz59vNuYcmOsUvCY+zl+2wb59yOlKosDrGyPpvNDYBUXg0Ou7d0BlpfDInSs0tvOqQSEKmxs1b36S8LqcI6s7Ci3HD4XA4HA4reI2jMicUAnr39qOp6Xyccgrg9xfX9chJjlp1JIBnrz7ctn2/Hzj/fPU1S2IhP1p9Ppx0xndQEw2WLLseevWaKiNshylZbySRySHIoF4H6afFpNS6ZBY4X9q4NOtual/6Lwj2njwDAPJ5YPlrQFKnU8uSF9Fz12fqpJe9iviDGxX75LZihRepgkXCEStxQSdqZ2j3SqzZGad2qPx+v+UYIJeddEAT6ZyjOkGAdeczN9veTJP4yfFD8fcP1jhOl7KSO9w6z2bdw2KMBFkjhzcWCmBPR4Z5xJE8xloSGaz0ieeypVtbi4ROK2gFEifjx46GdeGh/fDWku3UNZScyGOkUOHmGCMJ+n0QBHFdv1zfhClDai2PeyuhUYvTjnNO0sliQbpztN00OD3I6+/8hiZUMb7e70vQXC84HA6Hw9lbHLhX6H2E2lpgy5YIgBdMpzt+ZHfUVYRs249EgBfMTTsmGvRDCIRw+W8exKWH0bVz13N4YxrxwmmnF9mBCQX88AliVEcynUN1JGjbVoltwgmQlzcrBPACo41blApnFLmQSQLv3ga0bC7+fPN8oMM89eOezEXYOuhcPNjrIIQCm5HO5tGRzqKLgzFltNxqVzVvUnZkvHwybzfVKxKJ6I8BA59RTvHM5ApIZHKodbicRgIJi/pbZn2sWEbD6TmlXqY5uq0RZCWQKKlqTlLJTLa6PMYKhQIG3fomAKA16U2ai7ttZH3OHiSlg3oZVRP0+5Tz/wtfbcbVxw42nNaO6CVP25LIGB/3OtAKO/J57dWFW3DbmaMtFobOpuG8pHO0l/WySC4/YgD+/fkGJLM5VB3At6V2xg2Hw+FwOJ3NgXuF5nQaf3t/Nb1wpNclS9M9aX5DEw4b3NX2cpCiVDToR3s6x8wBLYo48iSdhiiOrdf2PJcBZkwGmjcYm6jtD3QfU/Lx/Mjh+Oe8YZjqr1OWP53Ns3HedFJsWKZL6dVmcdtWXbGtU8uHlXBhJgREg35kcllP07G8Fu0cp3tZOLwxRvtWz1mXl31rSxK5fEFJm2JhG1DH5asLtzg6f1nPV8DBfWuwaHOL7WOXViBRtj957rGybWM55OMrSZkmS6bk2YmsCfh8SOfy2N3OLh3opNE9MHvZDmRzdGtsN5uwNio+4GiMp5HJ5ZlEyhphFVnGOhOyWlo3UqiiLXbO4XA4HA6nc+DCEcczvnv4APxh1nJbbbP1Io60v3d6s09GYURDAbSnc57UcPCic5VecWy5mGjIVwCevUgVjQ6aDgw6tthARXdg+Cm63tX6+ZuAeYuUG/Vo0I9mZFy3hDdCTlVLZ/OuHHRAP8VG3v7Pf7UZt5w60rltnULtrOonmREN+dGazLKJ2tFsWhbLr7ddZFh2rNIVdzwobC8jO68A8MnqXZg6orut31v50vI239KctLtoxZF7Jg61Imx6dOxqCxmHAmyde7mjl2jfO3HkR1MG4+8frGHavVDu+JfN2932dNvwnIl9ccfrywCIY6kmSrdtnOwhWoGclbbDSuzncDgcDofjHbw4dpnT2Aj4fB0QhD7o1asPOjo6mNrv6AD69BH/GJvGxAF1yGeS+OIP09GnD92y66XYaFPVnBa0Loo4YpwuRTqNsjDSFm9Hnz59qNedBgFCUQRWIp0DProbWPOe+MEpfwTO+z9g4veK/0acaniXr42sYSl86UXWFLVt91AAaIynmBcJtltfqqOjQ3cMmAkBLIuHa7FdH8smrjvaWewut9vGrHtYj+qI8np7i31xR8ao7svPpCL/rMc8OcaCeTFFzeuIMkBMyaLBTGjUEg6qtyR2t5OdCBXacWrUnVCPgFRfKpMrGB73bqiOBBSR3Wrb2K2fpEW+hhkJOW7taynuJsnU9D6FF+OGw+FwOBxWcOGozMnnZTFlK7Zv38rcES4UgK1bxT/WN2wCABSAdOtubN1qb9n1ih3L0KYClEA4AUodHGYt7UtrHCXTWWzdutX2uuvaJ16Tbc/zy18HPrlX/GL4qcBhP3Y8D6XrmfL01/3TeD2BJBxQTzuu08l0Nut5E/sqr910htNLJrM7bgqFgu4YKOo2p/kNi3Q4I4GERf0nvRQ+mYhbYce0gpL7bWPl8J41rrdo31HnM3PblWHn295svJBjLKrUUbJ37NIKJHJUDQB8uX63rXnQEA74lPNQMmN97Do9q6pCBbvIrIAccZTLGx73JHaXnbxubd6TcLOolig1jjqhgxugf146EBPVaMYNh8PhcDh7C56qVubU1gLPPBPBxo0LcPzxYvFElkQiwIIF6muWCIIAIRBEryv+hh41YaThR4XFb3Rr1mhS1X7xwrfoSGdx2REDnS0X2KeTkbd4svCypimNL+d/hVDA73q/abvNRYN+DEsvQ93rd4gf1A0CLvgP4LOvBWvvT71u++zziQ5QIpNzPQ89h7c2pqYcJTM5W6mSVtht2R6JRLBAOsBoxwDLGllaHcCtsGOF16l8XtY4AqAKLy7GpZFtr7Y9OcaeXJ4D0OJZxJEgCKgIifXh0jZFWRohQBAEhAM+JDOMaqwZoEau0QlsNMsud4LL5PK2jns76V6RoB/xVBbn/XMu7jnvIFx4aH/d6WhTG83mAwCLNregI51VIpC8wnWk4n6Ck+sFh8PhcDidBReOypxQCLj4Yj+A8brfyxEVoYCz4DG/Hxivb9o1AgDB50eox2DsAbBsWxxHDTW/GdKrWaNNVQOA38xcals4IqMNWNdUIG/Ue9dEAYjrnqjuj8nDu7m3L/2XN8vg4G78D78X30RqgGs+AwJhV7Zl6yxv4o0iJaIhUTjywlFg15lM/K9X44h2uf1+P8brHGBmRX1ZbH+jyBrZAfxw5S7q4sYltj2scWTl8LoVNa2Lb0ttyB11PqOz7eScYzZeyDEWW78YgP0aR2ZRZFoOG9wVH6zYSV8E2m7b+aAfyUweqaz1dnIalEGdqmZj2eWIo0yuYHjcu4W8Fj700VpD4cgtg+srldcLNjbjqKH1Rd+zDoYhGyawToPbl/Bq3HA4HA6HwwKeqraPIztQ2qicckDr4NA4Y3o3pAO7VigpJG4oKjDNuoA1sdy9aiNKSkcb47bYgiAA+TweyN2NkJBDQfABP/wYCFda/9gArRDAssCukcMrb/8ml12NjBxeFtEderZZFn82Qt42zR3Ox46RY9enLqq8Xr0z7tg+oF/Lh1XXOSMiDKOxdO0zWH7jaKbOGzteRm7I57a0zVpztBqlXCD7wxW7bNm3gxd1xOQaR7TFsZ2IL+R1Pp5kFy2lpWdNBL1rxIc8ZkInq85n8nl1DXlOOhBz1TgcDofDKWO4cFTmdHQAV12VwbHHPolHHnkSmUyxMyk7IU7TcTIZ4Mknxb8MW40DPkFAIZdFfPF7iC9+D20d1gVn9VKPfD4Bf794AqaNdycekZE1XnZnEgAcPbQehVwWb730Pzz5ZOl+s4uyXQoF4N9nYnBhIwBg2bGPAF0GuVxiybb0P8ayxhFpn9insvP55JwGd/YNol9YpTRpUZ+M022bTCaDJ598smQMGG0XAAhL8/jL7FXuFlaHcX1rlNfxlLP9a+bvRl0KO2a1fADVcX55wRZn9mmLbzupQ+SlbZPvyDEW9omihfMaR9bTBqSUrCylcGRXIJHF5OXbW61t60So0kAbcWpnu8jntE9XNxoe93rYWfQIEXFkVr+NRczOgK5iYnlnpI/J5+u2ZBYNuw/cotB2xg2Hw+FwOJ1NWaeqPTB7FR58f3XRZ4O7VeCDXxwHQHT675q1HK8v2op0No9jh3XD76eNRbcqNWVnS3MCt72yGJ+v242KUADnHdIXt5wyQrn5LXficeCJJ9IArsSnnwLf/e50BINkDRfx5jHsUDhKp4ErrxRfT58OEKaZUMhlsfvNvwIA2v7fNdbTm6RMuN1npBMQYVyLRRteHw35Uchl8dDvfgEAmD69eL/Zty8ybundwIY5AIDfZi7HMT2mYIxjq7JtzbJL26Y95Z3D0Lcu5qmDwKQItGmqGp3TnE6ncaV0gJFjwMyZHt69CrOwrahWk12Mih0LgoDhPSqxakfcebSFTudDGa+7ttXFQgBEcSGdzTtO0TWKlFC6Lbo4Lxh1VYsQnRydpgkCpctOjrE/z/pWmYdXBH1y23lvUopuPmUE7npzOVIMC1drkY/jFdvbkMsXlG5lbvBJ+2Xl9jbD457ESUpW1EXXOdvzUpo8lM6HdaraQX1qldfrd7WzNb4PQTNuOBwOh8PZW5S1cAQAw3tU4qkfHKa8DxDFf3//xjJ8uGInHrpkIqoiQfz2tSW45qmv8dKPjwQA5PIFXPXEfHSrCuOlHx+JnW0p/OL5bxHwCbjl1JGdvi5OCIWA+no/WltPx5QpYg48SUKJOHJe4+j009XXLBEEQPD5EB08CQBAo0OYPeElO/o4gRSlWBYgBkprs0SDAQg+H8Yefhz6d4mV7Df79gu4xP8+hq5/GgAws/JC/KfxFBzCsPOZNlXtgfdW4eC+NZg6srtr21qmTeiDz9Y0unZw1dpPxWODSWcyHdt2I2r8fj9Olw4w2jFw4ujueOC9VZ4VmI66qONjaVva7plcAZlcHkGbYq9ZLR8AOGdiH9z+2lIA4r61KxxZOetuto1VFyS5xlGhIEaL2IkSNTNNjrGKsCis2e0WZnQc6RHwq23nWdsGgJqo6CzbOXbtXhm6VqoPl+atb8IRQ7ratFDKGQf3wiOfrEPQ77N13NvRD8ki1bTCndNsMpoIOVbZZKGAD4cOrMP8hj3K/GjHy/6Ek+sFh8PhcDidRdkLR36fD92rSgsqtyYzeP6rTXjwogk4UirceN/543DiXz7GNxv3YGL/OnyyehdW72zDUz84DN2qwhgD4MaThuOet1bgZycOd/y0ujOprQV27YoAmKX7vdsaR5EIMEvftGt6VEcgBELoPv0OAEDOZz3czJyMgIOOYUW2dWocPfj+avz8pOGu7AKlKTbRkA9CIITv//4R/OxEB/bjO4GP/gSkxaevt+5cjtFBMZoAfSdjlv9KoLEJj3+2Ht8Z34fJssvbPEqkQ9z5+lJ3wpFBKgkz4c5AaGRd/FxGjaihE+wikQhm6RxgZik2snOo96SfFrPIPbedw5TxomOcFEOSmZxt4ciKqnAAfp+AXL6AZCaniAx2MXJJmdQIMuqqRlxvEml23f7IMfbsvI2SffeCshFBou28F8jpWCu2tWJ+QxMOHdjFeGKHkS+D6tX+nns6jOus2RG9elSL9ymJTA7hcFj3uHeLj1IFYtHK3et6ZVq8FLT3FYyuFxwOh8PhlANlr5w0NLZj8l3v4Zh7P8AN/1uALc0JAMCSzS3I5ApF3T6Gdq9En9oovtmwBwCwYMMejOhZXZS6NmV4N7Slsli1o81wnqlUCq2trcpfW5vxtHuTp7/cgDcWbQPgvMaRl9RXhnEwUVOF6obQJOLosiMGMFkuAQL61sUAOO9Gp0UbKUFdfLV1K7D+0+K/pa8A9w8DvnocWPQ/YNH/MDolikbpYA1wyXMIhMTIArstsc2Qt3mMGEtbW6zrUjnB6yK+TIpY66Rked3OHmCzbcwi97zc9uGAT5mn285keqlcgiAo49NZdzLz773cNgG/T2nZbtc+bVqT16mCgCrgZ2ymqtFGvsj7d2tLEtMf/hxrdlpff52k/R0rdbtkdSzL54ZcvkAVjeVE2/nhsYPt/8ghZnXivOh8JgvaHZIoz6juNofD4XA4HEaUdcTR+P61uH/6OAzuVoGdbSk8+N4qXPDw53jn58diVzyFkN9X8sS5vjKEXfEUAGBXPIX6ypDm+7DynRF333037rzzTsZrw54XvtqsvB7ew3lXLS8Z3qMKiza3AACSNO2VTSIlqiPu8v3JW92zxvXG7a8tRTqbZ1bjQkZMVaNw4Fa9Czwz3dzYIVcCXQbjmXkbsbwxg8NOvw5nxrrgqqOANxdvZ1PnQuPBkBFHroUpWcDQfMzqabbReGHRNUkvssZujSMr42YFoLN5Z+leVrhpOQ8QopTO0svCTns652lntbZU1l0dIoPD3U0knBqdYkwk6EM6l3ccCWflTDsWNm0VgRbH438/b8CNNNGaNhUS8vwDAN9uasHQ7lX6pm1Z1syHJvLOxnYho34Tafo0SjspWbGQ/QdETmtp0TSQYCnuqOds72pbcTgcDofDcU5ZC0dTR6gpMqN6AeP71eLoP32AWYu2ehphc+utt+LGG29U3m/ZsgWjR4/2bH5mNDYCvXp1IJsdh0GDgCVLvkUsJkbL5KUb8rvOGYtDBpiE85vQ0QGMGye+/vZbQDLNjEImiS2PXg0AWDP0eevpKSIlZGw71UQESVSTUlMRdncoaB2YaCiAfCaJv19zOp69KYxvFy5EbP7fgUXPA4WcuCzNG9Qf1I8oNuAPAlN/DYwU6x3MWv4F5uzYjUkhMcWCZRqBViBheWyptot3qNvuW4p9g/HiVZqFKizQpQJ1dHRgnHSAffuteuwabRdALaIMiE/7a6L2hSOzFBuvU1CiIefCEY3G4FV3MsD7bRMLBdCazDItbEyOsRkvvgeAgbBpglxA3G6zAlqRQXv+8WycMiigTxIK+BDwCcjmC9jd0oZJ48TajORx7xatqGb00INFPJC8H+Qud14TtXlu3R8xul5wOBwOh1MOlLVwpKUmGsSgbhVo2N2BY4bWI53LoyWRKYo6aoyn0U2KKupWGcbCTS1FNhqlSKNuRHFMLeFwGOGw+n1rq3VbYK/I54FstgBgDdavL06JkoWj3jVRx/YLBWDNGvU1a3wCkN0jptPNXrbDspsQkfBV8h3pUAPAawu34rxD+lIvCymQhMl6IyyEI822iwZ9QAFo3r4JzQD8X/8f8NHd+j/+6QKgi3kKQol9xsW9AVVkcPJU2wijMeV1Sk3URTqTjF5kjexMNezuQD5fgM8iUq1QKGCNdIDR1h0J+X1M6vgY4bZzWEHdMLqwSOczExlYOPxGUR5ujiujTna69u0uu8kmJ8eYEkVj0/k2i/TUcu7Evpjx4VqkKNfB7mVFe/4x2xcWQ9EUL86h0aAcDZe1PO6dXG61D08+WbXLVQ06M+SGGy9+vRn3nndw0bnOi3sFpdOpUhz7wMPJ9YLD4XA4nM5inxKO2lNZbNjdgXMmhDG2bw2CfgFz1zTitIN6AQDW7opjS3MCEwfUAQAmDKjDPz5cg8Z4SklR+3R1I6rCAQwr09QuLbW1wD/+EcH69Z/hzDPF4okyZtE5tEQiwGefqa9ZEwlH0OPSewEAQiCIdC6PcMBYmJDFMD1/PKR5wr291V79HVII8PnEdLJEJsfEcdAWO46G/BACQZzxq8dw3yFNCH/wW/GLrsOAaQ9BuS2uHwpE66jnIzulLGuxaO9PB3djf2x4kUoGGAuNTGoc6UCKOHPX7sbRw+pNphaP18+kA0z32NX5jZzqGHeTjkURued021ilZMn256xpxIT+9GNbtG3tLLkqfL4XaxwB3tTIIsdYZaUYkehljSN1/HgT1aQVR7wTl8Xbnw4T+3YENUBNo8z7grrHvR52rt/abbOzTf8ayEJzOGZoN9yLlQCA9nQWVTqp4iw7n8nn7ANZLzG6XnA4HA6HUw6UtXB016xlOGFUD/SpjWJnWxIPzF4Nv0/A2eN6ozoSxAWT+uEPs5ajJhZEVTiI219bgon9azFRclaOHdYNw7pX4efPLcStp43CrngKf353JS47YoCpeFFOhELAddf5ARxV8p1cm5S204oefj9wVKlpZgSCAUT6qml+ybS5cGT21F77mV3nSxspEQ1JwhGTOkFFphEJ+iH4/Di4dxijFt0lfthjLPCjTwEH3eG0QoN8k+207Xmx7WLj4/vVOrZVYtugexiZEmQVhWZqX6eANaB2ZnJV40hH3enXRU0dMHLaSPx+P47SOcDMuqoB4vaJp7IuOp+ZdFWTahyx7jgnI9v9SmpSYAuKCBIW4s7eqnFkVnCYyrbOgpNjbO2uOABgR2vK1nFl5yGEvP3TuTwWbmq2PF+Y1cTSoyTiiGI/Ozl9uI2800Ne9nS+oHvcF+FAIdGmqnl1DAPA2D7VEARxMROZnK5wxBKnnWH3J4yuFxwOh8PhlANl3VVtW0sSP312AU7488e47ukFqI0F8cq1R6KrFD30mzNH4/iR3fHjp77BBY98jm5VYTx82SHK7/0+AY9fMQl+n4Bz/zkHP39uIc6d2IeuoOc+gJHTXE74NQvXYdHGnMb5Umw5db6k/6yiXopsC3K6VwARpHDz7t+IX4Srge/NdCQaifaLnQxt23M32Nnmtm0bOI2kc+hF5AKTzmTSf+12OXl0D9e2rXAqMNDgdttYpWRdedRAAGL9FS9QivY6EnfMl0kraLKGdV0dLRUh9VnQnDW7PZkHKV5MmzEHKYqmB3Ygu6ACFqlqLqr50OwLu1G9Tgo827l+hzUFt62OYTf3BmSTh6Rmfbw4srUpueV8X8PhcDgczoFIWUcc/eOSiabfR4J+/H7aWPx+2ljDafrWxfDklZNZL1qnkUwCv/xlFmvWvIKLLwYuvPAcBALibpNvat1EHGWzwCuviK/POQcIMB8RObSvEEOvY8OPwFF/+gAPXDge3xnfR39yG2LYv+asx7aWBP5+8QRbhVqVdC8PnHMlIsiXw4ehG1GXF6Muspe+jECFeVqTGVoHRm57zvJpMCkE/OOSCbj+mQXSvJ1HBBlBCl+JTK7kSTotRuKOm8gRK+zURslms3hFOsDOOaf02DWsteM2ncw0Vc1ZS3ha+taJNddcRe2Y1QlS6lc5L6JrmGZHjMNUNm+rUDyNkO+0+LZZaiM5xqZNm6Z83rC73TKVUrGvvLI+zrXiRTyZRbiSYjtRnkIEQcC5E/rg5QVbANCNIycpU/K++N/8Tbj73IOYnONkm/FECi+88AKA4uPeLdplNBJP3QhqJNGgHx0mhe5ZXhamTeiD38xcys7gPojR9YLD4XA4nHKAX5XKnNZW4MEHUwAuwKxZwLRpceVmIs8g4iiVAi64QHwdj7MXjkKFPBpn/gkA0O/nLyLv8+OPby43FI70WqCb8daS7Vi5ow1jetdYTltawJrd0/8i2/kcDvr4B6j2iaLR2c924NmbR7s62LRdsuSnwR1p9zWa9AIrRvZU218v3NRsu1aNYlt+odmffp+AUMCHdDbvLirIIPqFScSRhW2acZNKpXCBdIDF43FqR4BFqp0RbosCW9Y48jgVTi0wbT9SzSqIiEyX6UjnmHfvlJd9/vomXDy5PxOb2jE2bXxvvLpwq2dRTSUpwxbzcRK5FSEEvEzeeD+7CQob2bNaeb2nI4MuFSHnxiTk8dPS3oGLLI57FtKOl6lqgDRe272NrpSpigSVa8KBitPrBYfD4XA4nUFZp6pxRCGnutoHv38KjjpqCnxEqpMiHLlIMvL5gClTxD+HWVSmfO/IQagdMg7hfmMVNcisva9VJMZPjx9a8pmJX1FsG7JtEaYFpsmaNe/dgeptcwAAN37RBa29jirab24gfTZWncn0hIAhRIHsLc0J57aVMVoKy1RBLbLDv3yb846IRgKJ0v2HYrl9Ph+mTJmCKVOKj10jQU1GjgoyK9xrhlkEiSzseLHdAXeCLE2XLC9rHMmCJgC8s3S7M9sm3/mlL2cv32HLpllNLO0YcyIMuknLo52PnatUhKiD98aibTaXiA4yGstoLNmJxAKIGkfZgu5xr4fd6/cvTxupvLZMVbNluRTD6D6PCliTwi3Lwtv7CkbXCw6Hw+FwygH+OKPM6dIFaGmJAvio5Dv53s2iI7gp0SjwUalpZvTsWo09axbi1L9+ghXb2wAAWZPaJ0qIvcE63XjyCAzoWoFfvPCt8lme0unRppKwTFWTF+E04Utg7t8AAG/lDsXsw27C4jtPdW1f70adVYcmvRQbQRAwdUQ3fLhyl2dPtWMhP1oSGXcFrKX/2uFSERZPbQ27O7CnPY06BtEEMnYEu2g0io90DjAzQU2ch7j8Tur4WC6T6xpH5pGOXncmU2ocORGmlFfGJ0054mGdVGiaJWdL0UAsmzNox5h8Xvjz7FWYOrI7xvaxjsaUcRK9ahlxZN9kEWYRKFYCrBU10aDrcxCJHCmV8wV1j3sSp1rdNVOGQABw91srjLc9I2HH6lhjLe1Eg+I14UDF6HrB4XA4HE45wIWjfRilxpEb5aiTIGuHmN0wU0UcaOrh2L1HNow4atsOtO+yaU3E35bCd/2z8YfAEwCAbJfh+OnWn6DgYxN2r9cli7WDrt3mbhx0GfNaOwyW30DEIKMJtrUkHQlHRgKJ1wWOyXl4Ie54vfxytyongqNVtzmATY0jM244YRgefH+18zpEJgs/uF6M5LO77e10JiOjNs7751ys/MNptuZlFy8i13KaMFIv6qwBqlBhtD/sNqBQj1sb530Hq+VlDTcSJ8W+3aDtqMfhcDgcDqd84MLRPkzeImqhnKC9IaRxvrRte2nTLLS2i7ozzXsMePMmKjt69ADwB7k+dbQL2i9/D5m7PwPyBSQz7mul6AkwXjsPrCKaAH2H12mhYBqqI0H07xLDxqYO1/aNBDVXkVIW3ZrcbhuzOkSsIu2sahx5HXHkxJmlEQK87Gin2s56JobENAW+aXDTWdEqnZLmnK5FG5RqVajc6VaMMkr3Vewp50xvRE0ZeVu8tWS76ThyO77k7bNkawvOOLiX8jmr4ttayH3Mu6pxOBwOh1Ne8CTqMqepCYhEEvD5xuOgg8YjkVDrzTi5IdeSSADjx4t/CeelbEzsJzB+/HjMuvN7yGdSltPTODDads1OI47km9TK3YuLRaPKHrb/crHu2FmoxcpCf+BHnyBaUYV8JoWtT/wEI8ccXLTf3KFuGVbCS2cUmNZDFQCcO1lmxdS9iqyxI7zI43/8+PG2xoCX4oW8XTY2dTiqbWN13pHtp7N55EzSUk1tm5wBvI+YcmZfLypQi5zKlC8A6Ry98GWWkqUdY6wLeusRIKJcvUinzGnGpVVEkFPU6DU26yAft63xdsvj3s2y9+8SU15v3lNqn7Ws89rCrbqfsxZ3nHbX3F9wer3gcDgcDqcz4BFHZU42C6RSeQDfYskSIE+E8MsRR24y1fJ54Ntv1desyefz+FaaQTXFjTJNRMCY3tVF76kjjjS307GQH32Fnbj4259JH3QFrv8KiHWhskeytakDx9z7ISJBH1bU9kNIXDBkdq7Hhp3F+80JegIJi8gX0ba+w8syoklvf7Io7m226yMuxRcWgho5/vXGgJFA4jZN0EzcqYqop/0v1jXhiCFdHc3DCDIiMJnJKfWmWNt3Mm5ohGmWkXZairZNOs+k1pF2jDlxvu0+hAgFfMhK24d2P9i5TOVyxQd2RzqH2pjBxHAuYFidQ+1GYqmF8zOmxz2Jk0WfPEi9RsVTxsK7W13n1DE98dHKXQgHi58xutTrDDnQU9WsrhdlRz4HfHQ3sOg5IL4TqOoJjL8UOPZm9aAsFIAP/wh8828g2QL0Oww48wGg6xDVTkcT8NYtwMq3AcEHjD4LOPUeIFypP18Oh8Ph7BW4cFTm1NYCd90Vwdq17+K884BIJIJcvoDz/jlXedLoJuIoEgHefVd9zZpIJIJ3330Xq3a04t5FQesfSJhFHAiCgHDAp6Rh0N7EFjReQBe04s3QreoEP3jPkWhUtGzEcl917HD8c8vvcda4Xoi43Lh64hjzIsQGtXxcCTsmURiyk/XoJ+txzoS+juch2i+dQ8x1upf+wLIT8SKPf/m1YpsyVe3RT9bhV6ePol1kKvrWRZXXm/d0ALAnHFlF1oQDqpPZkbYnHNEUO5adfS+KVwPOo1C05xc9gn4fAj4B2XwBiUwONaA7J5oVU9eOMW0qrxf4iacVr327FedOND5+nWgMY/pUA1+p7w27nrkUMFhHr8ljszEB3eOeFYIgmKbishJ2RksPaYyjytiGHB3oqWpG14uy5bMHgPmPA+c8DHQbCWxdAMy8DghXA4dfI04z56/Al48A5/wTqB0AfHgX8N9zgOvmAUFpHV++GmjbAXzvVSCXAWZeC7x+A3D+43trzTgcDoejAxeOypxQCPjVr/wATlI+29KcwMJNzQCArhUhDOpa4di+3w+cdJL1dM7t+3HSSSeh99YW3LfkM8vprRxqxS7huNBmw6h+nQBk0zh/5Y2oFqRw8GvmAF0G0xnSs62z3BXRIKKDJqDv2IHw+13WOJL+FxXHZhVxZLD9Ikxr+ei0hZechKyNlJ0S+yZd+IpqWLlAa9pOJJM8/u1CijvZXB4Bv72sYjMNQxAEnDa2J95ast2TdC+fT0A06Ecik/PEvhyV0LC7A7vaUiWpq2bQRNbEXEZ7WREN+dGWzDIr7q0dY1UReoFexm5kDXn+Xd/YTvUbO0LAxZP7oz2Vwz1vrwDgfR23tqR+Jy+7kVjy2PlgZSMevuw0hALGx61bbUcRvTwskK2IqJmcZzW5SPaBPh+e4vR6sdfYNA8YeTow/BTxfd0AYMmLwJavxfeFAvDFP4FjbwJGniF+ds7DwH3DgBVvAAedD+xaCax5D7j6Q6DPRHGa0+4Dnj4fOPkPQHWv0vlyOBwOZ6/Aaxzt48y99XjUxOw7Cp2N9in48m2tutPR1AnR8sc3l1NNVyTuvHcHerQtBQA82+MmoOdYG3PUsa2z3HJL9SfnNmDumkZ39nUcGFYRR0WCGgHpNHjBlUcNBOBhDSXXncmkF5rB6DaSCbAe52eP6628djQfi5RPV+leFCqD06LDZpE1MocNViOkxIgptjjdNkbHEQv7diJIDh1YRz+xQ8gaR1Z1rJzU8gn6ffjxcUMwsKuYn2Yl4tF0m9MjIqVg3fvOSke/1zJ1RHfldXMiTfUbp2IMjYDtVueRj+Pmjgwm/n42vt7QBIB9DSWZHx7r/OENhy1tbW1obW1V/lIpnRqV/SYD6z4BGteI77cvBjZ+AQyTxK89DUB8BzD4OPU3kRqg7yRg83zx/aZ54meyaASI0ws+YAsRdsjhcDicvQ4XjsqcZBL47W+zuOSSWXjllVnIZtWn1OGAz3WNjGwWmDVL/Mt60Agmm81i1qxZmPvhbBTy6g3uyu1tutPTOKVaMpQRK7Kzfub2h4AvZgAAnsiegls3TDT7mS1IJyAoFNCxdj461s7Hjf/7ho194rXsgL65eJsrm0ZRXjEGETs03b1YRHZ42T3MyC7NdpHH/6xZxceulS8dDviU/eFFdzI1msybOhpRl3WCzBzeynAAw7pXOrJPVeOIcactLW7Gvd520Y4xR9cEm23nyYgjL1vCRxgXr9YyVBpHtYwevvTrEkMs5Echn8OsN94sOe5J3KfZibdvuqlqjKQd8oHPno4Mfvifr4u+Zx2AJD9skayzNb4PYHS92BuMHj0aNTU1yt/dd99dOtHRNwJjzwX+MQn4XVfg4WOAw38MHHyB+H18p/i/snvx7yq6iYISIP6v6Fb8vT8AROvUaTgcDodTFvBUtTKntRX4/e9TAM7Es88C8XgcLPW+VAo480zxdTwOBBiPiFQqhTOlGfT7+YsQLJwy2qf25Le0Dl6hAFzlfwvH7f4fAKCx57G4s+FyAMDOtiS6VzmvKaDnBASQw64X7wQARPq96Ng2oP+ENybVjlm1g02tl5KULCZd1UxSyST7jfE05jc04dCB9utL0aTCuW9p77w4Njn+4/E4ApoDzKyNdjToR0c6h6STtvOKHf3v1Wgy+84JzTHqVLSjdaZZFFa3tO1w2a2cadadvLRjLBSJWvzCPX5iJb0oji0j74sn5qzHscO7GU7nVMA4ZUxP/PW91YYCqoPnGIiF/IjHM7jqkvMB6B/3JE7lEVlk8UrgBEq7nMl1BT2bXyfU5ypnrK4XncmyZcvQp08f5X04rJMSvPRlYPELwHn/B3QfJUYcvf1LoKoXMP6STlxaDofD4XQGXDgqcwIBIBr1IZWahIMPBnw+tkFiPh8waZL6mjU+nw+TJk1CoQDsFKyfUtN0VdNi6eDt2QB8+md8b+nbqArKT7AE1PzgFeC2dwAArYmMO+FIsapSEQ4g1HOY9IXLp6c62+X8iX3xt/dXAxDTRfwOC0RYFYF2l5JlDOkkTH/4czT86Qy29pl1JtO3S+P4y+Nffq3Ylv6b7TFZOHIi7ljBIprMbEi7LTpsJRw7FjUpzi9uo6WscFObTG+7aMdYwMF5wG58it9fLBx5Vf9GPqclMwbCDqPi2AlG9aYAaWwKAkYfPAGxkJ/5NVuGZpw6TeGTiWii19JKQwpvktUO9K5qRteLvUFVVRWqq6vNJ5r9W+Don4u1igCgxxigeRPw6V9E4UiONJI7rsm07wJ6HiS+ruwhvifJZYHEHvE7DofD4ZQNXDgqc7p0ATo6ogDmqx+mEszsR6PA/PnW0zm3H8X8+fNRKBQw6NY3lc9/98YydKsK4yyilgvg7AnvtpYkfjtzCW49bVRpK+rZvwXmPAgAqJI+SvgqEf3ZfAQDAfSqiWBbS9J1yo5eZE1NVSV6Xf6AK7uKfek/6ZuRRYHdtD03TlWTnmgzcKDNUslkMrk8gjaLQJvZV9Nc2AovssOWyuaRzxfgM3HU5fHvaD4hP9DubPurw1F/2dxEk9H4jMw7/mntMyoMr2ubWHY7gghtfTbW28bNGNNCq/0cPbQbnp23EYA4HlLZfFFHLH3b9kWMHx47BPMbvrLcVs6jdiz2tYMHGdGgH75gGI+9/C6OHFJvOJ1b6cUsjY+VruPzCYgEfYpwl9akhbOWCiPENYE2BX1/guWx3ClkOkoPDp8PKEj7rm6gKP6s/xjodbD4WbIV2PwVMOkq8X2/yUCyRezI1nuC+Nn6j0UbfSZ1ympwOBwOhw5e44jTKeg5DT95dkHphJRdbLTf/+fzDXhj0VbCTgF46QeKaAQAa+pPwBmpu3DXqFeBalGwYl9gWqVExGIAKQSQbc9ZOKElKVkh4xoatJilkmkdTWcihrFjF3NZx8dICCD3azLrNJrJOIVPmY+H4otq20kanLVAYqfzXJFtynQvp6lqVil8gLrs+UKpo8wCJ9FYtNtF/7fso0N+fcYo3HTycOW9mYDnZvZW28ptLR8v9rXdVEengVry+VnuPOcVnZk+Rs7LyxQ8DiOGnwZ88mdg1TtiZPfy14HPZwCjpPoHgiDWPPrkPmDFm8COpcAr14jRRyOlabqNAIaeCLz2U2Dz12Jx7TdvBsaexzuqcTgcTpnBhaN9kHze/lPQcmB8v1rLaWgcOwC457yDSz7b2SZ1/ehoAh6dIubeA2LHjl9vx1uj7sHSwiDk/Gqkjpp2xCYqRa/rGQv0iobLbc8BRlFBmm0eYWLb2LHTRhexbivtNl3KyFkn0zfeWbrdmW3pv2mqmotUO8WhNpiB6tx6U4BV7jy3qYl91zOA1djUp8h5tWGfusaRR0XbjaCpS2MVoaalMhzA9ccPQ8hPLy47uVx5va2c7mszWNSGo2FQvVjYuzpiEmnK4B5hT0em5DOvuqqR1wTW1wOOB5x+LzD6O8CsXwAzJgPv3gYcciUw9TZ1mqN+Bkz+EfD6DcCjU4F0HPjuy0CQKA1w7mNA/XDgP2cDT08H+h8BnPVgyew4HA6Hs3fhwlGZ09QEVFUl4PcfhcMOOwqJREJxJFkIE4kEcNRR4l+CXQYcYT+Bo446CkcddRSevmICrj1uiOn0NO24AeCMg3vhy1+dUDyvdA5IxYF7BwHbvhU/rO0P/GIVEIwSN7ul4o7bzl56TqMvl8b2p27G9qduRj6j08rWjn2jOkQMigQbRSR4KUrp4cY51HN4vYrYIVPT1u5sN52WHP8JmweYmyLKVgFNbsYNjUASkGrg/Ouz9fZse5zuRSOQBP0+BKXl9zbay4YoZbJdrMaYt13PxFsIszHqJiqIdls5ra9kta9pH2SQREN+5DMp3HTZ2abHvdtIsNPGijVj9Oo/sRR2fnL8UMPvvKhrJXMgRhy5uV7sFcJVwGl/An6+BLhtB3DDt8AJvwECIXUaQQCO/zVw82rgNzuBy18D6jVjKtYFOP9x4FdbgFs3AdNmAOHKzl0XDofD4VjCaxyVOdksEI/nAczFvHlAPp9XbtKLW9c6I58H5s5VX7Mmn89jrjwDFFAXCxV9v2ZnHIPrKxRn3M6NerfK4i4fwfgW4O7J6geTfyQ+EZPnruPwshBeJOsln4T8AlJblhfP3Kl1AyGAhbhjWeOIRaoaxbSsRQxW0WR6IsM1U4bg4Y/XWi4zOf7zxAFmlsIn42UdHy8jdgDg8MFd8caibaiOsmlzrsXrAtaRoB+ZXJZZAWuSKIPC5CRGY0ymI5NDnYUNqwg1I2KhAFqTWbz8zWbccupIez+mwGpbscjCc7Ov9YgG/UChgJWLxNb1evuExKn0Ih8D6Vwe2VweAZ36cCxkne5VOt20OgG99dnfsTqWORwOh8PZm3DhqMyprgZuuSWMNWtewYUXii1RO9JJAOrTXjeEw8Arr6ivWRMOh/GKNINwOFxU/BIATvzLx7hu6hDcfIrodKiOgPUtL1m4c6iwGTcsvkX98oTbgWNuLJpe76m9WYFRO+gJJDVVMXQ759fi5wF3DrSR0EDzxN/StvJKv+18Nl9wXLjajl/nSDgymYObOj6AeUQAbSSEdvzbwU00nJUwxaIlvJlActigLpJ9e6IdjaAGuKlxRCeQRIN+tCWznqRIOYo4MtkuemNsQv9aLNjYLM7Hw4gjef8qacI66KXZ0kIdcWTfdNE82pJZbNjdgcHd9KMc7HQn8wkChEAQQy6+HfdfMN72cU9Lca21PCqJ8zPLulZRnQdUHjVVAwDceNJwvLpgC645drB3MylT3FwvOBwOh8PxGi4clTmRCHDPPQEA05TPElJ7bhYRR4EAMG2a5WQu7AcwjZhBXaxUQJnx4VpVOJLFHcr79EIBONk3H4+GiO5l5z4GHHyB4W9I23YLmRouh2JbNV4VDSM2/AjlvVUHLhq0v5bHgNtUO0Cnlk9IdUTmrt2NKcO7ubBtvd6uWsPrfMYqskM/mkmq7WJhWzv+VWy0hfcgZSPmon4SDV7XeYl4GI0FONs+ZoXaSeRx2axTO8YJemPshR8dgfG/m414Kku1DnYiA0l+cfII3P7aUs/2g7ytsvkC0tk8QoFi8ZqFfpGTaga+vGALpo7sXmzfwQzOGtcbsxZvQ/eDjsG0aSdYTu803Ssc8EEQxGVMpHOodNhV0wptSvye9rTy2otEtZ+eMAw/PWGYB5bLH+PrBYfD4XA4e58DLxZ4P0B+Cu5F1y6vOXFUD9Pv7TowP6n7vFg0uvBpQ9FIzwlgXuOI+Cym2T80RWoN7Rt8zkJcMHKOQsQT7IVS9IJT2zT7000tH70ZuBUvzNPgpDQ+hw4zzXZxUxjYKsjDzbahEUjUwt55pZg/lW3pv/c1jsyRt8+X65ts2adBXvYXvt5MvW3sBu0E/D50k1KMvKwVY2c/2Ina0doHgLakidDmQsE4cmg9AHZROkO7VwAQUwS9RBCsmyOwKEEUDRXfJppFl3E4HA6Hw9l/4cJRmZNOA3/5Sw7XX/8RZs/+CLlcDrukGzetMOGEXA746CPxL+fBfW4ul8NHH32Ejz4Sl13bgl0LbaoKCgXglR/j+ja188Yvev9HbQOr9xPpP+nAsI6MIBc7IBSQ3LgIyY2LUMjnmBSwLo0KYlDjyKDwriAIuOLIgaJ9p+KLjQgyZ6lqxsjHx8amDuxsS9q2bQatw6wd/17MQw8rccdN/SSa4tikw+9GMLWy71Wkiywkf752N/VvaLuqHTGkq/I6zqCrndEYk7dRQ6N5AXfAxnlXA804ciPHyIWrAeD5rzaX2mYg9hwl7Q+9dXBS+ykaCqCQz2H36gV4/4MPDY97FjqV0TmCZSpZNFgcyZTI5Dzrqnag4+Z6weFwOByO13DhqMxpbgZ+8YskZsyYipNPnoqPl23Bna8vA8BGOEomgalTxb8kW99asp/E1KlTMXXqVCSlGfzrikmWvzO9T8/ngRcuB759RvloYvJhbEF3kx9BuZvWK47tusaRzq10KpXCjmd/hR3P/gqFbIaJOKV9ah+Vaxx5kKoGQEl/cNvSngY320cvmqFfl5jyep6DyBGj2k8AvaijN/5J2zTFsb1oS+1lGhxpH7BX56hAGRLktKg9rUDy3cMHAADyHhRzOahPDeSMVdp9a7ZdrMbYc/M3uVhac5RaWTQRRw6iXwRBQI1UYL0lwSa1Twu7BgmSvaAfhWwGm5/6JU484XjMX7PddHo3QUFep4QCpZHN5PHsYVO1AxKjY5nD4XA4nHKAC0dljs8HhEICBGE0RowYjWXbWpXvrNK+aBAEYPRo8c+Lm0BBEDB69GiMHj1acdaOH9kDw7rrFyG1TIPJ54DHTwSWzQQA5IQAxiQfRxOq8cW6JmRzxtENeukeMcapaqR1QRAQ7Nofwa79AQFIMIguMOp85kZcMPON2XX2sh5czqJfjMdLZTiAQwfWubANQ9u0tbH0xj9p2wwWhduN0oPk5c/kxMLndjAT1GR8PgFhqR5NubS0t8OArhW27dNsF0AcE/Jxy6L4ttEYO3ywWKDcSVF7WmjETbfa2/eOEEU8s3O02+LYgPm+tmM/GvQDApRz/22vLnWxdBbzkrb/9hb91u1O0gNL5qGJEvaqLhrH+FjmcDgcDqcc4MWxy5z6eiCVigEQbz4f/WQtAODciX1w7sS+ru3HYsBS7+5rEYvFsFRnBlmD2h566WQKiWbgH5OA9l3i+6En4eUR96P9JdX+1uYk+neNlf4W+tEGrIQRPZEhFouh9w8eUhc/7aLGkUHAAYsnzmbb3E5Ega5tyvQdgH2qGgDUV3pT54V2uxuNfxmzzeK0cxgNZMpoMpPzRFyIhvxIZfP2CkxL/y1rHDk8bm3XUPIoFS4S9COeylLvW7PlNhpjRw2pxxNzGqiOXaeNz+ycf5y6waqAWiq8s4gHMxtLTkSvcMAHXzCinPtTgv5tlllHSFr80on1P59vwKlje7m2p4c2sjmRznvbVu0Axup6weFwOBzO3oRHHHH2Cj85fqju54ZCQ3sjcM8AVTQa/13g0hcQ1LS5t+tkswr1N6oTdNwItROZO3FH/0adSeSFSdSOW2HNjnPkxkk3ckpd1fIx2Kdu7RbZpqgT5OQJv5VgJ3dkAoCPVu5ialsm6iBiijaVzEtRDSC65tnqqib+pxFJndi3i5epjjIxilRftwKJeo4zFt7dRGewHks+n4BIUL21snxg4CKwZGyfGgCA32W3TjN610aV6DWgeDvxoBgOh8PhcA4cuHDE2SsY1WfSdTI2fw3cN0R9f8T1wLQZgCAg4C++czW7+dezTeP42EF7I/3EFYdiUL3UZcdFqppR9zDalCkadAUSRql8NP6Fo3lYCA3uCkzLto3tepm2ITv+7y3fafu3Vs66IAjK+pHpryxhl+ZYSsRhRBBNRzg39mlRxg91jSPxvx2BxI4oTrtdtMjr0RhP2eqeZwezcxyLwBezfe20aLiciggYnyNYLPuJo7qbzoOFsOP3CfjfD4/AqWN6AhBTrnm8EYfD4XA4Bx5cOCpzmpqALl0SCAZPwnHHnYR0im3BxEQCOOkk8S+hXybBpf0ETjrpJJx00klIEDPQdleTHZcSZ339p8D/Ha9OeMT1wCl3KW8DvuIhfN87K7B5T4fusugJAawEADWVTDWeSCRw8sknY9njNyOfSbmah1E6GQvn3MwJMEsTobJtEYVx5sFqeoWbtvNGeFULh1ZwNBr/ejWxtNTFQsprozFthZnf+OPjRDHWfrqXcSQWibNtTydgyLZ3tqWws5V9EVnZ8bdX44huuwDOorGMMBpjrEVxPSrCqkDy0SpzgdOpiKFETpnVIHIhkHhxjgghix3/uw07/ncb2jvMj103dYgiBtFSXmSS6UVmsaihxFExOpY5HA6HwykHeI2jMiebBfbsyQN4Dx9/DJx3K9vW1vk88N576mvW5PN5vCfNIE/MQFtwc8X2NozqVV0skMx7DHjzJnWiH34E9J5Q9LugJuJozprd+NUrS/CfqyaXLIue+GJ0480Cct37nVbAsm1trutQaB0kJjWOTJ6qq86CB4MDwJ/OOxhLt7ZifWO7u65qBv6Lm4gss9pP5HYvFAqGEQlG45/GsTuSaNu+O55G3zr92l16GEWokVRQOORu8DIqq2dNRHn96epGnHeIvXpvVg6v1zWO7HfyMh4wVudYO9vfrhDQrSqsvN6yR9/RdStimIvX7hUSWSRsS2axtTmB3rXRkmnsyiN74ikkNywEAORsFp+3g9U4ZSnryNfK177dCh/PUfMEo2OZw+FwOJxygAtHZU51NfCjH4WxZs1TuPRSIBkMWf/IBuEw8NRT6mvWhMNhPCXNIEzMQNvit6k9DUB1MuoXzgDm/Umd4PqvgfrSukhyjQeSz1br12wxizhau7PdfEUs0LMtr/s/P1qDTYEgXl2wBTeeNNyhffMaR04jUgDztCaap/00GDmkleEALj9iAO54fZnDdDLzKI+Im4gsE4+XHL+pbL4kgk7GaPzLmPlfAb8PQ7pVYO0ud6KaEU67tlHXOHIQ8WLSdb6IWCiAwwd3wRfrmhzVIbJCPq6yebHrHE3xcFs1jiT7G5vsHbd6tq3OsVbiplvOndAHLy/YYrmfnUan0NQ4ckNdhVon78OVO3HpYQOU905lqRtPHY1fL/sFAEDQ1OFza5vESBhnUXhbi9wlcc3OOIZ1r2Jun2N9veBwOBwOZ2/ChaMyJxIBHn44AOBSAMDDH69laj8QAC69lKlJjf0ALtWZQWmnFvHGVyjkcH/wYXSf94n4heADfvI10GWwrv0e1RF8eNNxuP6Zb7B0q1irxajUhl4qSQWRkrJmZxxDu1fSrpqlbXndc4Mb8NuZS5Ubb2f2UWIfACrC4nb8ZmMz4qksKsPOD2mzWj6Oi0BTONOyg9vckbZv3+J7FmkoZtsFEIURI+HIaPxTp3tJ2yaetJcqaNqdUMJJOhatbcC7NEGZXjViZIgTUdOyxlFIPVbXN7ZjeA+2jnJO2ohz1zbiuqn6jQJoMRpj8tjJ5QtI5/IIB/THKGBP9DKaj1f7Wb5WLN/WaijiuZHEwgE/jhlWj09XNzKLMKuvjqFyzFSqacstzc6Iiw7tjyfmNBR/yAOPmGJ0LHM4HA6HUw7wGkf7GFkp7D3o27d3ndbRbktlgFwGj+duw/l+STTqNQ64dYuhaCQzqL4C/btQpPHoOEcje6kO4dpdcapl1zVtku51kBQV5a7zmb79o4bWK6+3tziriWAWheG6xhGFQBKVBIw5a3Y7no+R8+WqM5lsW+c7v09AKNAJnbGk5f/Bf77Cht3uouJKbEviiGepag6ivZRtTuFNO4mYoo3ECBHixOoddOcFWkENAIZ0EwvmV4TohF7aSCwSUtxcub3Nxi/tQSteOBVIelSraYnz1jcVfceqlo+cBqodS46Lhhs0fyi2bc+mHlapyiyjzOorxWjnZCaPvBdFlDgcDofD4ZQ1+7b6cACQTgNPPJHD7bfPx9y585GSHGttNzGn5HLA/PniX84D/zGXy2H+/PmYP38+csQMtDWObnvuC2T+0BsHYxUAID7gBODqD4EQXV0XrT0zyJvpoN+Ho4aKtWRYO9Dyuq9ZthCFfI6Jfa0fUF8ZRi+p3otl22cDzBzemJKq5tA2hX8xeaDa6nmzQZ0Ua/sGXdVcFAi2WnaaaCyj8U8b4UEuw+drd5tPXPQ7a4fXcTQZpTPtdec5N9EWVmdPQRBwxGDxvJD1oNbIqJ7Vkm33DrjRGCMjc9636Mxnp7C3FiWd1WAcGaXZ0tKPeCjQmsjoTuNWIGEduRPyAaltq5DatgqFfA7bTER9N0seJc7PZFc7L3QdUgzrjAinAxGjY5nD4XA4nHKAC0dlTnMzcNVVSfzud5Nx1FGTkUyJN6A0NTdoSCaByZPFvyT75kRIJpOYPHkyJk+ejCQxA7IbTx/swtLI9xEsiKlKX+ZHYtvp/wZ89GJQBUWKltG9NIsOR3q25XW/4LTjUchmmHQ+03Myom6jgijazqdzeSXazQlmjl3Pmgj6SAVpnXb4MoJNqpqBKEUhjBiNf8W2hdsYDqrHuR0BhqI2thLp5VXXLS9rHAHOCp/bcajlbZ/OUo57G9EpsvCfoTymzCKxzMbYGVLXwmTWOyeURYF+K44ZVq87D1b6iBx9Z1xk2p6848tnsP0/N2L7f25EIZvB9hYPLq4ofmDSwDgiUUuESHWURUKeqcYWq+sFh8PhcDh7Ey4clTk+H+DzCQAGoF+/AZDv/wM+NrdsggAMGCD+eVE7VRAEDBgwAAMGDChyeiJBP24+ZQSGCZsxJ3KD8vk/hQtwcfo228ty4aH9LKcxKqQsO9CuhB0dp1Fe9379+wMC0CEVqXVjXw9WNUZ0RSniKXOS1oEmoF1bN5FBgEmqmquuauZLLwsXc9c2miyX/vinhXTWUg62vxlOI4JoRCnSvleCgpt9S+PxBqR0YBZRQSW2JeE/m3Nv22yMDa4XU+KMooFYEKM8dlnU8mmMp3QFcreXrhiDawBJNByAv7o7/NXdAUH/GGBRwJoUjt5asr3ke5aXdJ9PUOr08Ygjb3B7veBwOBwOx0u4cFTm1NcDuVwMhUIDNm5sgBAU05ICjCKOYjGgoUH8i9F3+7ZhP4aGhgY0NDQgppnBdX3WYnb4FuX9Vemb8DCmIw8f7N7y6nVX02LUojwadH8zrEYEqJ/J67505Rr4ghEUCs6dfz37Mm5TgswcmHDAp8zTSUQTrVDmWMSwiFDxUrxoS4nbY976PYbTGI1/2lQ1shaYrXRBk5pbMp4LO5L9koK6JigpUxSHv5NIFzuuelCKCqKNtKMV1AAgKAn/tGlwZuPc7BxLu42Mzo00WB27LNKmZJHwj2+uwOjfvoOcJOaxSsliHTXVtaYKfX/8L/T98b/gC0ZMz2tu9AGfT8DRUp07ch5eVSByK/BzzDE7ljkcDofD2dtw4WgfIpcvYPGWZgCqU7PPsuh54JkLlLcXpW/DB/mJjouRAsAFk/oqr/UEC6NaPixqsaiOXemCFzv/7rqT6Xl2riOOTEQMQRDU7eOwhpKRbRKnIoZlHSI3EUcW4s73jx4EAMg5qIFDG21AduJLMU43UtJznIp1Fjt1RE+18LwXdY7kMbPHQTc+mtQjWZzPMIgK0iKnGqc9sE1CGw3kBvkYe2/5Tguh2Pk1S9uF026XQSuMzj9Ou81pO2h6uf3H9BHrZbGKljIjJm0n+QEIj4rhcDgcDufAgQtH+xA3v/At5jeI0Q2sahztFWb/Fnj5auXttNTv8EV+NAB7T+21/ObM0crrVTqdkIycAJa1XvTuo4N+nyL0uZ2HWcSRU9tWnazcRKaYRUoVzcN1qpqHy24wGuW6TG72qdU4DzuMOKLZ7l7XOJo6srvymlY4sip2ThKRIgU/XW2cKlhqn16YdhoVREPAdjSTw+5elKK4nY5wWnoSXc8adncY2naDtgtnWrPd3OoXsjDVGE+5MyTh0yyQnqjDKloqFpSOYzLiyEkbPgoiFN3iOBwOh8Ph7J/sw+rDgUFzM9CzZxKRyDQ8/bufopAVn66fOKoHE/vJJDBtmvjnVXHsadOmYdq0aWKxx5d/CMx5UPwyEMHMEz/BwsJQZXraaAY9yNbWH6wo7SJk1DmITcpOqW1y3cNCztU8zCJUXNV6ITDa4rLTtnRri22btM6R231gmKoWch9NZkSMwnbJ+JegHecRsji2jYgjmhRBpfB5Nq+k/lDZppQCSMHUi3S4kVJnMsB95y491ALW9mzTnLuCDGscGY0xgF39MzMmD1K7Ihp1PQPY1DiSkcU8FnWCAPUct2BjM1pM1oGW+qgAYfZ92PnyH1DIps1T1VyqO3LkoJd1rJR52eheyrGP2bHM4XA4HM7exroVFWevkk4DO3bkAMzEzmVAv1NuwBNXTMLo3tWWv6UhlwNmzlRfsyaXy2HmzJkI+oDgf04Htn4tftFtJHDNZzgl7wPe2KxMLzs4Tm6lfT4BZxzcC7MWbbNVU4LFjbee3yqvOwAcMv77iGediztmD5Bd1ziycLrlFKl3lm7HuRP7mk6rYx2AtXNk1dLb0LrFsstP4zO5AjK5vK1IPas0FRqxixwDTtorh3U6GdnBtKsa4QSe//BcPHnlZNREg5Y27aTvRIJ+ZHJZPD9/M244cRhT271r1UiXbL5Alb5rqw6RNFZoo1DsiBhyc4OVO9qQyxfgt2h2YBaJZTbGIpTRiG5ShAVBwJBuFVi7q12/CDQDbUebqlYquLkTXw4dWKe83tTUgRqpZp7RwwYr8vk8Gr75WLSRz3ubKmhyHmKdSKbd7jxRjS1urxccDofD4XgJF47KnMpK4NJLQ1i79lHE+61Fq5/tLguFgEcfVV+zJhQK4cmHH8R5O/8MvywaVXQDfjwX8PkR9hXfiMqRD06fTveuEZ1JPRHFyIFxm+oF6Kd7hUIhPCpt3P/ujgBJ8yfPpvZNIlRonUMr20ZewHfG98Hjn6135QB6n6qm/3kkpApF21uS6NeFvuColRAQoYjmIMdAiDjAaDclGXG0emdp+qURNKlqZB2WBRub8faSbbjw0P7U86AhFvKjLZnFA++twjHD6zGxf53p9HYcdbJBQDZXAOtgCDnd6N9zG3D7WWOY2u4tpTkCYiTfwX1rHdsyGmOAKriwiKIxQz5+NzZ1YPLALlInUHZoU9UyUqoaq0CzrpVhDK6vwLrGdiYij7xPXlmwGUv9AdNzhNs0O72UU68qZ/3khKG4/pkFHlnnmB3LHA6Hw+HsbbhwVObEYsBTTwUBXI2z//EZFm22ny5kRjAIXH219XSO7bdvx+Xb7wAK0k3t5B8Cp94DSK2ujdI6nIbvyzfRL32zGRce2g+Du1Va2pZ/46qrmk5EUDAYxNXSxn35wU+B5rT7Gkc6n8VcpqNY1TcZI0W3OaoTRFlqw3FxbMj29ecQIsSFmQu34PrjraNetFh2bDPZp+QYIKFNrSIjjhZvYXvs+3xi4XN5m8dTlHWIpP80xygZ1bRiW5ulcGSHACFOZPJ5REGhHNlIhT1IijrpXhWxmFIybcNb79clBkEQf9NOsd3NIrGMxhigbv91u9qxozWJHtX66+KmthygRvbd8uIifLB8Jx6+7JCSadzoI1phLatJrWRRo1kvrc9ptzl5n+x4YxmWfbYez8/fhJ+dONz9QurgdXdEEm3kF4ctZscyh8PhcDh7G17jiOMdm78C/jpWFY1Ovgs4/T5FNDLDqSMg30Q3xtM47cFPixx0o3QMFjfeVsVIo8HSDlbrG9uxpTlhaz5mxbHd1vEx2ubaWj6JdM52i3IrnK6DlbMuCAJG9xKFr3a7op1F2lSMQW0pq3FORhwFbERx0BaZjhKOoJPucFaQkSI0x5edVLWgJuKINRMHiCKX3fMC7blrbG9RmPKi/pbMyF5qSvOyba2ezYcsmvz20u1F37GoQ0TWUQLYRxwBdEKwXSojoqC2rbW0Xg2rulxyqnVHurTTHOuuZ9rIL95UjcPhcDicAwcecVTmZLPA66/nsXnzcrRsXo8Capnaz+eB5cvF16NGUWk6dKz7GPjP2crbzYfdgd6HX+u5UhklHO1UNo9kJq84x0YPj5UaRwwcONJ2Pp/HcmnjRgJC0TxakxlMvf8jAEDDn86wtGvmZMjr12azRXUincNP/7cAs5ftMJ2OTIWLp7IYe/s7GFRfgQ9vOo56XlYOjJqq5qzNtpn540d2x7JtrbYdQiu3jhQcC4WC7jqSY2DUqFHwSQcYbbc50lHL5guG83FKVGNfj427O7CjLYlDB4rOux1xh4xQSJjs23y+YDu9ye8T4BOAfMFOdzIRmmX3urC0HcHaLIXPaIwBQGU4gEMH1mF+wx6qGllOxxZ53jW27ci0LnaKudOi7m9n5yASeZ+Mq0iiUMgD8BmOcdepalK015ItrdjTnkZdRYipoEYSC/FbRi8xO5Y5HA6Hw9nb8KtSmdPUBJx7bgI//elYfHLv91DIpJnaTySAsWPFv4S94BdjvnykSDQ69al29Dv9RiRszMBxxJEmlF4/7UCTqhZ035Zc7z49kUhg7NixGDt2LILIFs1jZ6tacJfG6TVLD5LX+Y1F2/DQR2uol/mdpduLRCPLlKxMDgs3NgMQo6VosJ+qZi/qhSaaQd4+rcmMI4fTyJmWoyzyhdL24DLkGLAz/mXIOkQAfYcv2vbtRRFHBrbPe3gupj/8OZZttR+xQto3Ekj+34uLcMy9H6ItqdbhoU1VlescZTwQEmJE17kLH/kcm/eUtponsdvS3m1dLxmrMUaViuty85kJCqxEDDLiTluwnIUmFVEijtRj2e4+lZH3yfFHTlKu2Xa6ItphZM8q5bWXUWUA76rmNW6vFxwOh8PheAkXjvYZ6hGM1QJw3763xHK9+MeEz/4KvHWL8rbjqo/xdUsd6m3OwPGTb40DQ9NphkU7e6Pi1fX19aivr1fEKXkepCBAIwaYRXmQN/P3vr2SeplTGkfGsHsY0fEsQNG5ioQ2HUNvH9w1axlOe/BT0/1CY152CF/+ZgvO+vtnyFOKDEpqo9Eyk2lYJssoj4Fi25Bsm29PbWpIVied7LZXF+Pap7/WpGXK9s0hl9tIfNnVJjrpCzc1i7ZtFLAm08lIh5zkua82YUtzAq8u2EJhUWNfEhMyWWPBMZnJYVOTKPpY7VMSUvT6cn0T/jJ7le3lM7VvJ+LIIspLb4yp8xH3waLNLfjL7FWIp9xH1GjRjlOZjbs78OD7qwG4v26R0TqvLNgKgE0anIzbWnFatPtEKxBuamIjDNRVhDC2j1SHLk13TneKVjhifS/CMT+WORwOh8PZm3DhqMzp3h0oFCpQKOzC1D/MhC8UsdVS3IqKCmDXLvGvosKFoXweePEq4L3bxfeRWuD/NSDWfzx27dqFXbt2ocLGDJzejmpvbE/+y8d4V6q5YeRksKgRpOdMV1RUKOteXSWuu+yUkPswbeL00uD0KbCRs6eFdKjIlue2anRYRb4ES9NEHvt0PZZva8WL32y2Nm9in9w+y7a1ot1hOpyWoN+nbA8jZ5McA8Xjn27bVYSLhVCtyJjJ5fHUFxvx5uLtWLG9jX7hJc47pK/y2iryTVlHG7u9KFXNIgUoly/YSoMD1Iij3e3GkZhn/O1THHPvh4rwRYs22mutRVc7u8tOCrJuMB5j0nyk8f/k3Ab87f3VuGvWspJp7KTw6WF0Dvrhf79yZlAHsxpfTIpjKxFHerWC7Nki90msIibZVffz3DWNeO6rTbJ1R8tLUhUOAgA6lPOQN7lq2oheDlusjmUOh8PhcPYmXDjah8hI0QZBm1EfnpPLAP86BVjykvrZjcuAqPMOSl0qnLWi1Tow7ekcfvjfrwEYO3ZMutJYOI1RpU6Q6JT4CSdIm+a0ZEsLLnjkc3yzcQ9h3tgRiDi8mdemlxg9PSZrHBUVJNZEqKxvbMftM5dgK1Hwm7YpkVnUV9ykdhNNKom2ExCtUEfTUSnisqCulUN61JCumDxQLQxsJu6QkSRGEXBaRvRQ01ys0vi0wiqNM33kEPXJudU2MqqxZIYcNfeOpiAzydpdYlrlzIVbbAkkgiAUR5UxrnXkJFXNSYSHNgrz6w17DKZ0Tt+6qO7nq3bYFzONIM+Z8lhiWctHPpbnNRDnXQYz0HswoYpGbJDH0rbmBB79ZG1RKrQX85HhxbE5HA6Hwzlw4MLRPoTcSSYYKKPdlooDfx4JbJ4nvh92MvDbPUCI/mnZ3y6eUPIZbTSMlnH9agy/MxIZyCK4bh0FQ/FFmseMD9eK0xGTZTRiwGWPf4l565tw7kNzlc/MohliFttqfkMT/v7+6pL5aCMqrESvVDYPHzGRtpPVRY9+jn9/vgFXPTm/ZLmtMKtxZOaw20lVk6GvE2RNNGjt/M9cuAXvaQqQ06aSBfw+PH/NEZB9ZjNxxYl4JReGB4zrNGntq+KLtddIitxWdYhy+YKtNDgASsc8mq5qTjqvkaKj0T5u6cjgi3W7baXBAZ3XRl0rputtB7vLruWscb117QUIodmtyEAKRyUiJoOonbCU0vfJql2ubZHonSPIcy/LaKm731qBP765Apf835eibfemdefD4XA4HA7nwKOMFAiOHs3NwIABSVRUXIpl/7kbhWwaIYapaskkcOml4l+ytGOwOS1bgLv7AB2N4vtDrgAueb6oNVsymcSll16KSy+9FEmDGZw9rjee+cFhynutoGGHqkjQ8DvDiCO561rBeXFRvUgGct2HdQ0D0E+30EbA7OnIlExDUxzbiB/8+yv8efYqvPJNcQ0Zn2ZDGNbyKWrZrjqdGU29nR3SU24yZUot0kzXVU0vTUTrJObyBTw/fxNWE9EMpqlqDiOOFNsU0UxGaY4bdzXjkksvxXemX1hU7NSuhKEUgTYRd/SEDSvHkRTVnpjTYDqtkyLOAeJc4LcYA04ijo4b0R0AnfiSzecJoZHOpSa3j9E+PuPvn+KiR7/AKzZrNLFIkQWsz7GkOAgUH7eFQgHtDGoeaY8xWZwN2uyUZwY5fpx2XzTjrINF8UsQVOGLNmJSC7lPQoK4f8kxylqA6awUMj/D/ckpheZ+icPhcDicvQUXjsqcdBrYuDGHjo5n0LT0PRTyeaY1jnI54JlnxL+cHf9l27fAA6PV91N+CZz1YIkHn8vl8Mwzz+CZZ55BzmQGTtOt9Lh+6tCSzz5cuRPrdok1SrS3vmTEzscOnzbrRb6Q637kYDHdKJsvlDj/ZmKAFj3fu19dzPQ3LQlRiFpgs8aLTCRAtmxXl9VOBAdtV7WG3R0ljqzWSXx7yXbc8tIiXPnkfEARpoxtl6Sq0bZup6hZE7GIGmlPpNGx7GN0LPsY7clSQZC2CLzsgJsV1E0WdRCkLEpOjP2qsHmrbXkdnUanWK1qcY0jOuvyvm3V2bZaaCPNSIq6whkIZ5v3iPtkp1RE3G6NI5pIMbOxaHWONYs4uuaprzHm9nfQsLvD1rJbzWNnm+j0ktGxbiUHMh3ObvdFGgZ0Fc+jhYIYXekGcp9EpMOqSDhi3NbeSIhy2mTCjB8eO1i1z9z6gQ3t/RKHw+FwOHsDtncvHOZUVgJnnx3CytX3Y0f3VRD8AaY1jkIh4IEH1NdUrHwbePZC9f3p9wOTrzawH8ID0gxCJjMgb3zd3rTfdMoIfLxqFxZvaVE+u/IJNX1Key/t8wmYNr43Xl241XGhWr0aROS611SoTk9HOlfU2Uu7viG/r0TcMHMc6yjrQWkjG0qW2cDJ8PkERII+JDP5os5YVsWUpZmYmVboVRNRXn+2phGnjOmpvNd241q4SaxBsnlPAl0p1l3rVNEKdTQdm6yc/+qKKOqOF4+NnKAuB22qmky7ZP+T1btwxJCuutPodhC0mEFRRI1Fu3AnkTHkOLZaVycRR/Lyz1q0DZMGrMeVRw0ytp/LExFwdPY9rXEkpzC5tGt1jjUrsP7O0h3ayR2hjUR55Zst+MkJw4oiztzy6zNG46JHP0e+4D5KSw9tdBn53q4AQ+6TT4MRABk8MacBU6UIOXJcsbiad2bRaqdp5BxraO+XOBwOh8PZG3DhqMyJxYCnngMOvWs0ajIjAQBhhjduwSDws5/Z+MGy14DnL1PfXzELGHi0if0gfkYxgx7VEctp7NC3LlokHJHoOQHy/J06h3qREuS6FwoF+H0CcvkCkplckZO8YnsbxvZRazMF/QJKdQhzp3r6IX3xwteblXnpraNW3EhpntqbOTDRoB/JTL4osqM5kUF3i/1GKwV0rQyjR3UYO1pTJRFGWidR7+m6WTqZ1tGxn6pmjFWdmnA4hOpDvwMAyLoI8DxxVHe8t3ynqVhHppLRFA0HireNVUSOvN1oioZrfwNY1zjK5+03VyfHwoPvrzYVjtxGHNHqWrQig62II5PaT1bn2DMO7oU7X1c7qWXzZuOfzUMJWegkH3K4jX6ZPKgLXrv+aJz5988cF6M3Q+6SmMkVkMjkUAs4bk5G7pMvHv0cQPF5jEwfdFJ7S0tn1h7SRnBy2EF7v8ThcDgczt6Ap6rtAzR3ZJCUnPzLDh+A3jVsRRZqti4sFo1u+NZUNLJDl4oQBnY1T7mywy9OHmFrequUIyusfGlBEJSUuEQ6VyQAfLhiZ9G0esXP1QgV/Tn8+oxRyuu4Qc0SbWSDdl3N071EjbmNEI5e+maz8Q800BSvHde3FkBpLR2tcESmNdJ0ySqtv8IuVU12ojY1dVjaI53dgs2QozG9RWFRu8/yREqakygMrcOpTXHLO4gCIiG3tZWzH09lbRenJ53YZp3aYNplsRvp5aVDblUfixXdqyIIEeeUjMuIThrkWmUBxh1A3Z6nae07qedlhJzalTRIVWOxLkYRR16kkhUdEzxXjcPhcDicAwYuHJU52SzwxVygfXkS/ngj7jx7NNO6Bfk80NAg/pk+iE63A09PV9/f8C1QN5DCfh4NDQ1oaGhA3nQGwIT+dTSLTEW3yrCt6dWn/+6cKnLXaNc9QrTfJqMftHEWejWsrASSSiId5eVv9Iv0atPw7DgsEanjUFtSFaW0EUt60IgvMkYRGLvb00XvyZpUNEKDtutcG4NiwDJyio5RYel8Po9syw5kW3YU1TiyHVljMD6TxD4gRRra7V6axle8ZHrpa7TRTEBxGqaVcPQ80aKc9hRnpzZa3qYoBTgTjmjPzqpIYT0ezWo/0ZxjadeD1aXFK2FHFttaEhlsb2FfPDimOQfRCNN6kPskJIlnpBhFZvax2FZkaq/X8M5q3mHnfonD4XA4nM6GC0dlTlMTcMFZBTS+dj7WzbiiqDMTCxIJYNAg8c/QdDYF/P0QoF2KjPnhR1SikWg/gUGDBmHQoEGWy+7EsTPC7Em3nhOgphw5ExWUgsGkQ6BZdzKtiUwX0T7dNuuaZ7RWAb9Psd+kEVqU5cmYR/KYCQFRpQixun20v9dbbjvJR2SXKVIQWqgp6k0+XZfTPEzTyTTiwpfrmqiXSbRtbP3wwWK9ocqIftZvIpHAloe/jy0Pfx/NbXEd23QYdeAiRYe/vrdaeU273asiAVQRy64Vd8j38vFp5zAlUzCtHOQuFSHbjrodJ3bhpmbHxbe9QD0fuHMQac6x5HZq9yDNq2SZpHUifV8WkVVdiJpmH2giNVnAqtMduU/8eVEwJo8lUvBlsV0Gdo0xE/2s6Mx6Sgcadu6XOBwOh8PpbLhwtE9QABCDEPAmRS0WE/90yeeAp84D2raJ7y9+Dug9wab9GGKGM1BxUhzXCFPhyKSlvdPaGUZLTq57tChVTf2Fdp4h3VQ1623z3cP7AzB2RLSOu1awMk33kpa9NaFGzWjtua2Dq+yDTK7IsQLEjlsyZF0eeRnMlj2s2Z4hyvQZPTFQy2GDROHIaNwUCoAQDEMIhou2l10BIxrSj05pT6k2u1AWSSfx+QR8fdtJynvtPiXflwiNFIt+7LB6XDzZeFySKZtO0uLsCDuNcX1B1QxH3R4pHXg7qWpWW8bqHKvdTm5TEK2QU9VG9apSPmvY3e7abiToxxGSWEsTqeXEPqCTxuvAlrxPIjpiFPmaRb0mQRD0a795ICYVF/bmuWqsob1f4nA4HA6ns+HFscuc7t2Bjbt9OObe5xEL+VFRUcHUfkUF0G52P//0+UDDp+Lr0+8HRpxq034F2k1noMLSmQmaqBjmEUcOb+J1ahBp1112/mct3oaKUKn4IUMWlN3ekkTPmghdLR+L+hza7at1WMyjdsRTxfpGdX208/ERCzfo1lkA7Akk5PJr6zQl/n97dx7eRL23DfxOt7Rpk+4LUMoipVAou0DhYccCBxEBRRFREFwQZHF7UBFQOQc9h3MEAcEHFagvCoLAQYEKWimLLG2hlM1KK2VvC5TuezLvH2mmSTppkzRtCr0/18VFOjOZ+c03k1m++S3larE5nn4iyJxko/G2S2zYx4t+skuKk9wVIa//UPmH9YlfU8en/mdo2IeS9n9zHhxdnBygdHVCfklFjbXSdPNq6qjZmEwmwxM9W+C7k1clj0v9pmwaofa+vIx5ujmbtVy1cpm5XH02y3F1rluyWsecc6xxB/ElFWqx3zJ9dUkD/N+Unnjpm0QAVceKn16T4brWrNJp5avAsb/uoqRcLdaCs1WCRPd9Xvzf81C5OeNGjnW1PvQ/k6t3tf2fFZn4rtqqWZ+bs6NN+2YyuR3WOKo3ltwvERERNTQmjsi0cz8AabHa1w8NBR6eUa+bs2WNIweHmmocVad7QLxXWI6UjHyLt3f9nvbhoKYHGFXlQ+53J68aTDd+cOzc3BN/ZmqbNaVmFWgTR2JoampOpv06m3oQ+etOIXp8dED825Jf7HX9BMX9eVucZlxTIixIidNXcwBUb87Uq1Xt/VfpNxOpljgqq0oclUmOQmT+k6O5TUPMGTzMrZaHf/0aQQt2JOODH88DqEqYWJrAMH74LtT7DIsrm/hZ0weam7Mj8ksqEH0sHf7Kqof9rLxSvfVb9+Dv5qz93ApKKwwSj4C2rxoda5qqtvBys6pM5tL17WUJc5Nebnr99Xxz/EqNy5pzfjFnWzrFZdKJo7qI6hSEL6b0xMvfJIrfB/1R9Yz7WLOWfgfWuu+Xu9w2+9La1x2nr+bgL6PjtC6jfuonl3Xfz6J6SBwZJwcBoI2fbX9oAoAAvfODyo23kERERE0Fr/ok7fIhYPsL2tdeIcDk7fVT711PfTef0JGscVR5c5+SmY8RKw5Zv+4a5s0bHgofhbM4LHny9Rxcyy6u9uAgl2iKJa6/xhpHDpLv0Weq/6Pa1v1075a4kl2Esgo10m5rH6qMkyUtvRU4fTUHrwx6CC/8T2txuoNMZlDzwGT5a+j4Vj/ZUyTRubUlh6bZiSMzDkddQqdCI6BcranWsXlBaVViJKeoHDkwHPmrXYCHWWURmzUZxdw4+VdaoYGrs6NFHVgDgJfCGVn5pSY7+Qaqmh9ZUpsJqPpcswvLMGT5QZPLaZNpltUgcTKKd22JM3OaH+pzNFrQnMScr5lNBlWu2kRycbka7+86Z9Z7pJqxmsO45lRRmRq+EsvVdeAF41qPZXpNEaU6WreGQi8RU1h5LrBV4ujv4zrjsW7NodZLTof4KtDSx/qmQ/pJO933U/8cbc4gA+bQb4749MMt8XTvEHQIUtbwDuuEBiqxalJ3aAQB4c1UNl8/ERERNU5MHDVyeXnAoN4yXL2+DaqwLJS+NwRyuWUjhtWktBSYPVv7evVqQC4HcDcN2DSmcgkZMOsk4GBd9fTS0lLMrtzA6tWrayy7LWscWapbSy90D/Eya1h1UxxkMkzoGSz+bbzvPUK80UNv5LiUDG2SyjgBo9+fkdg8SKi9eZBUP036ybjvXuwLPw/Dh9q1B9Ow47R2FLaaHhoHhwVgcFgAAOBgShamboiv1ixC1+F3cy9XBCgt/4VeV/69ZzOw92yGwTz9bRlvV+7kYFZiSsfimjM1xMXVpepBvrhcXS1xlJ1fhLv7PoNMJsPBHdFw0Tv+ZTIgLNC8BztXvcRmu3f3itONa+kUlaklax7UZsljnfDf0zclO9U+cy0XKZn5VteMCPFRoN9Dvjh7PVdyvm6UO2ubbP0wMxIT1h7Tvj51A0411DYst/Ac07O1j8Hfugd/fW393fHX7UKM6BSIv0U0Q1S4eSNc+SvleGdUh2qdv5sikwHjugdXm27OObZaUzUTn6U1Naz06ZIXabcL8Mh/4nBLLwFsq2ZUUjUTlTZKHClcnDCk8jxXF/qfycrPVonTfzxzEx5yJ/x1u6qjfJs1VdNLHPVv54duLb1ssl4pY7o2r7d1N2WW3C8RERE1NCaOGrmSEuBymgbAJuQmAxUVFTa9maioAL78Uvt6xQpA7lQBbH6yaoGX4wBn65uDVFRU4MvKDaxYsaLGsjdU2kiq2YG73Ak7X+1v0+3Utu+m+q3RTwaItTzM2J7YVM1EM4iuLT2rNU9p6295UwbdOowfPnVDuTtZ2Ut279Y+8FY4416RtlaOTFZVu0V/P3S1bFp4ueHjCRFo6+9Ra1837z8ajo9+uiBZblPM6cvHxdEBjg4yqDUCisvUYi0SnfzCUhQk7wcAhAW6W91H2UP+HvBxd0F2YVmNCdaqRKP2b3MrkPR7yA/9HvKTnHfxVh5GrTyM4jLDhJu5tZkcHWT49sW+JuffKyxD948OoEytMWuUPGNdg73gINP2kfTmtjM1Lqv7bphb9kHt/XH8nWHou+xXANpjxzgJo6uV9Hxka/RrJx1DU14e9JBFy0sx5xwb7G14Dtc1m9IZEOqHvm19zU5kmhLio4CTgwzlagGXsgxHEbRVUzX9BHmBWOOocfW7Y/yZuFXWMnpre3K1ZW3ROTZgmBz0sFEijRqWJfdLREREDY13F42cQgH07g+cOv8O/LtlwtnZus5gTXF2BpYurXztJABfPQJkp2knPLsDaNa1jut3xtLKDdRW9vdHd8SEq/ds8jAFACM6BeLn85kG076Z3tvkA7Kt1bbvutoqRWVqhC+KEafrdxgsPlSY0cm0VCJK9yu/TAa4OlV/uHKzop8T3Xau3yvGuM+PitPTKh8UaxrRriahgUokLnzEIEk2csUhXMoqMHi40g0nHtUpEANC/c1a9/T/aQMvN2e8se2MTZuq6UYzKiitkHwALFEDXgOmoI2fe52+u55uzvh9wVCDPoF0HB1kGP6fOOQUleuVwXZp2Kp+nGw/ihVgWFPCmtoXTo4OePdvHQ3639InCMCR1DsArBuJK8jTFS6ODihTa1BcroaX0XxdIs+42VxDMecc+0ZUe/Ro5Y05350GoD2v6I9cuPbZnjZJNgSoXHHwrcG4ll3VqfSk9ccB2K6pmqtEJ/q2aqpmK8afydsjw7DPqBblyfRsALarcaTfVM3DtXHFg8xjyf0SERFRQ2tSdxfRx9LxRdxfuF1Qio7NVPjgsU71Wp3bFjw8gLXflmH85/3R0scNLi6WD7ldExcX4L33Kv/44SXg5int6zGfAe2G2WD9LnhP3EDNQgOVSFoUVWPH1pZY92xPfBKTgnVx2kTY2G7NzU402EJt++6jcEErXwWu3C0y2YxD16zKko6aU7MK8MLGeO37K9ercHaUjKt+TR1zmzg183KFs6MMZWqN2Bm2PuPaDZYwLqObxJDlun1ytzDppds/S/sUqa3Wjmtl4mjhrnNQGj2wXblbBM9+T6F/1+Z1/u66Ojua/IzcnB2Rg/JqSTFbdEum+wwKy9RovWCPTdcNaJsa6mqXffbrpcp1W7byGQPaYsaAtibnd1oUg8IyNTQW1sTScXXWJo42HE2Ht8Lwc9T1G2ZtwrSuzDnHKl2d8VjX5vgiLg3nb+ahSK+Zl0xW1fm9LQR7KxDsXb1PoHLJTu0tp0uQ3Csqw63KUc9s1VTNVow/k2n922Ba/zYGy+h/l2whSK8mbVAdOvMm+7HkfqnRyLsJHFgMpB4AyosBn7bA2DVAix7a+YIA/PYP4NQmoCQXaNkHePRTwFfvB8KibGDf20BKDCBzAMLHACM/AeTm9QFIREQNo3HdbdWjH8/cxNKfLmLpuM7o3tILXx+9jOe+OoHYNwdb1D+KPehGpjHuP8Wmds0Czn6vfT34XaDn8/W3rRrYKmkEaB8+9fv0sfUoQnXl5OiA/fMHGoxcpfNJzB/4KflWtVoeNT3wNvfSPiwUlFYg9o8sg3ktTCRzRnQKxB+32qBCI2BsN/P6rfDzkGPf3IEG/XSI85RydLdhMlaXKPnqyGXsv6D9xf7EZe0v9QoLm6fo+m+5eCsPz319stblL2VqR9errVlTcy9X3CkoFWu1mFqmPumShhuOpqO5l6s42pS5TbJq4uvugtAAD4OmR86OMpsl3WUyGZqpXHEztwSnKhORPmZ2MG0uNxcnsaaaNbwULsgrqcD/HfrL5DL3Q/MgXdLl7PVc5JdU1tZxcbLpebe+6Y71+PR7iE+/B6Dx1Tiyh7dHdkBEsCdaetetM28isxXfA74aAbQZAEz+AXD31faR6eZVtczRFcCJL4BxawGvVsBvfwe+GaftO9O58rq440UgPxN4bhegLgf++yrw41zgia/ssFNERGRKk7nb+vLIZTzduyUm9moJAPj74xGI/SML3ydcw6uD29m5dKZpNMCffwgovl4OjSLP6uG2TREEICf5GLyT/p92gk9bYOCbNly/gDt3tA/Ufn5+Ni27OfSTRe4uDdsPhjn7LndylLzJ1w0zXr1zbNPxa+vvga0v9cUViQ6+I9tKjaGkrYmw8NHwWvakunYBHmaPCFYXuqGfpZIylnbArUue5ZdW4JCJZk1S9Ienl7J6Ug8cunRbsnGYIAgoL8jB0A6eNv/u6vN2dwHuFOKHU9cNpitscMw7OTogZt5A5BRVjcjn6uxo04f1TS/0xvHKhKCDTNu3kC25udQt6f7R452xJ/mmyeaLDwV4ILQBvg9SLDnH6pqm/ufAn+K0+k54NfN0xa3cEvGcVle9WvvgIX93ZFYm3D3kThjWse4dWtuSPa57Pu4umNynVb1vh+qPve+XLHZkBeDZAnj886pp3q2rXgsCcHyt9p6yw2jttHHrgH+FAn/8BEQ8AdxOAVJ/AV78raqW0qh/AZufAKKWAqpmDbU3RERUiyaROCqr0ODcjVy8OriqaqyDgwz92/nh1JUc+xXMDHfuAGteuogOTk9AOAKUTIyBm6vtai+UZV2H996XAQCCwg+yV45aPYKalKKiIgQEaG/qCwoKrO4c2Fo+7lVNsXw8bFuLoTZ12XddTZuT6few6tdLBsNa16RPW1/0MZEkuh+987eO6NTcExVG++/l7oJHu1h2Q9khSIWtL/XFjZzi2heupHJ1xqCwmpMYIb4KPOsr/cBWWFgIDw9tE5X6PP4XjwnHjlM3DDo8bh+ktFnNA0cHGXzrsWZmaKASoXXsmLkmHYNUYr87rs4OaG5hEmNQe3+bJ7NsxZLzzOQ+IcjKK0F55fdJJpNhUu+Qei3fty/2xZrfUjFzsG36rvNXyvHrG4Ntsq76Ys5n8r8jO+CTmD8we0jj/eGKGpa975cslrJP26XB988B6Ue1SZ6HZwA9p2rn30sHCjKBtoOr3uPqCQT3Aq7HaxNH105qp+mSRoB2eZkDcCMBUI0BERE1Dk0icXSvqAxqjVCtSZq/hxxptwurLV9aWorS0qrmQ/n5+fVexppsHj8b7XwqbyC2TLDpuvUjUjT1ONxdHqwq7kM6BGDBqA4oLlNj0sP1+4BkS94KbcLrzLUcnNEbrtu1jjUn7jctvNxs9sAJ4IFKqunrEuyFLsFe9i5Go7Vmcg+kZORDELQ1z2zdFO5+MaJTEEZ0CmrQbbbxc8fyJ+s2yMKD6JVBbfFol2Z16hOOqL7k5+cjLy9P/Fsul1cf5e1eOhD/FRA5CxjwBnDjFLDvfwFHF6DbM0BBZZN5D6Mage7+2oQSoP3f3Sgp7+gEuHlXLUNERI1Ck0gcWWrZsmX44IMP7F0MAEBAAJDTKgC3SgR4Kpxt2ompSOYARC2Fe4Dtf1F3d3c3qAXR0OROjnjFRqO0Waou+z6uezAy8koNRtHq3EJlcfMssi97H/+k5ezogM4tPO1djHrBY6zxMeczkclk7IuIDDSm73J4uGET9sWLF2PJkiWGCwkaoHl3YPhi7d/NugJZF4GEr7WJIyIieqA0icSRt8IFjg4y3Ckw7IT4dkEp/CWaX7zzzjt4/fXXxb9v3LhR7SLakNq/ccBu2yb78FQ4Y8GoDvYuBhERETUxFy5cQIsWLcS/q9U2AgBlEOAfZjjNvz1wcbf2ta6mUUGWdlmdwttAUETlMoHav/WpK7Qdb3sE1nEviIjIlppEuxcXJ+2vzb/rdbCr0Qj4PfUuerTyqra8XC6HSqUS/ymV9df3BhERERFRY6FUKg3ugyUTRy37AHdTDafdTQM8tYPQwLu1NvlzOa5qfkkecD0BCH64ch29gZJc4ObpqmUux2lrM7XoZdN9IiKiumkSiSMAmPE/bfBd/DVsT7yO1Kx8vLfrHIrKKvBkz5b2LhoRERER0f0j8lVtJ9eHlmsTRsnbgMSNQO8XtfNlMqDvTODQv4A/9gKZ54Gdr2hrH3V4VLuMfxjQbjiwew5wPRG4ehzY+xbQeQJHVCMiamSaRFM1ABjTtTmyC8vw6YE/cTu/FB2bq7Dphd61DrVNRERERER6WvQEntoM/PoBEPdPwLsVMHIZ0GVi1TL95wFlRcCPc7U1i0L6As/uAJz1+mscv16bLIp+TNvnZsfHgFGfNPjuEBFRzWRCY+mJrxG7fv06WrZsiWvXriE4ONjexSEiIiIisine7xIRkSlNpqkaERERERERERFZhokjIiIiIiIiIiKSxMQRERERERERERFJYuKIiIiIiIiIiIgkMXFERERERERERESSmDgiIiIiIiIiIiJJTBwREREREREREZEkJo6IiIiIiIiIiEgSE0dERERERERERCSJiSMiIiIiIiIiIpLExBEREREREREREUli4oiIiIiIiIiIiCQxcURERERERERERJKYOCIiIiIiIiIiIklMHBERERERERERkSQmjoiIiIiIiIiISJKTvQtwP9BoNACAW7du2bkkRERERES2p7vP1d33EhER6TBxZIbMzEwAQO/eve1cEiIiIiKi+pOZmYmQkBB7F4OIiBoRmSAIgr0L0dhVVFTg9OnTCAwMhIOD+a378vPzER4ejgsXLkCpVNZjCR8cjJl1GDfrMG7WYdysw7hZjjGzDuNmnaYeN41Gg8zMTHTv3h1OTvxtmYiIqjBxVI/y8vLg6emJ3NxcqFQqexfnvsCYWYdxsw7jZh3GzTqMm+UYM+swbtZh3IiIiKSxc2wiIiIiIiIiIpLExBEREREREREREUli4qgeyeVyLF68GHK53N5FuW8wZtZh3KzDuFmHcbMO42Y5xsw6jJt1GDciIiJp7OOIiIiIiIiIiIgkscYRERERERERERFJYuKIiIiIiIiIiIgkMXFERERERERERESSmDiywLJly/Dwww9DqVQiICAAjz/+OFJSUgyWycjIwJQpUxAUFAR3d3f06NEDP/zwg8Ey2dnZmDx5MlQqFby8vDB9+nQUFBQ05K40qLVr16JLly5QqVRQqVSIjIzEvn37xPklJSWYNWsWfH194eHhgQkTJiAzM9NgHVevXsXo0aOhUCgQEBCAt956CxUVFQ29Kw2qprhlZ2fjtddeQ1hYGNzc3BASEoI5c+YgNzfXYB2MW/XjTUcQBIwaNQoymQy7du0ymMe4Scft2LFjGDp0KNzd3aFSqTBw4EAUFxeL85vauQ2oPW68JtTu448/hkwmw7x588RpvC7UzjhuvC6YR+p40+F1gYiISBoTRxaIi4vDrFmzcPz4cRw4cADl5eWIiopCYWGhuMxzzz2HlJQU7N69G2fPnsX48eMxceJEnD59Wlxm8uTJOH/+PA4cOICffvoJhw4dwksvvWSPXWoQwcHB+Pjjj5GYmIiEhAQMHToUY8eOxfnz5wEA8+fPx48//oht27YhLi4ON2/exPjx48X3q9VqjB49GmVlZfj999+xadMmbNy4EYsWLbLXLjWImuJ28+ZN3Lx5E8uXL8e5c+ewceNGxMTEYPr06eL7GTfp401nxYoVkMlk1d7PuEnH7dixYxg5ciSioqJw8uRJxMfHY/bs2XBwqLqMNLVzG1B73HhNqFl8fDy++OILdOnSxWA6rws1k4obrwu1M3W86fC6QEREZIJAVsvKyhIACHFxceI0d3d3ITo62mA5Hx8fYf369YIgCMKFCxcEAEJ8fLw4f9++fYJMJhNu3LjRMAVvBLy9vYUvv/xSyMnJEZydnYVt27aJ8y5evCgAEI4dOyYIgiDs3btXcHBwEDIyMsRl1q5dK6hUKqG0tLTBy25PurhJ+f777wUXFxehvLxcEATGTZ9x3E6fPi20aNFCuHXrlgBA2LlzpziPcauiH7c+ffoICxcuNLksz21V9OPGa4Jp+fn5QmhoqHDgwAFh0KBBwty5cwVBEHhdqIWpuEnhdaFKbXHjdYGIiMg01jiqA131bx8fH3Fav379sHXrVmRnZ0Oj0WDLli0oKSnB4MGDAWh/tffy8kKvXr3E9wwfPhwODg44ceJEg5bfHtRqNbZs2YLCwkJERkYiMTER5eXlGD58uLhMhw4dEBISgmPHjgHQxiwiIgKBgYHiMiNGjEBeXl61WiQPKuO4ScnNzYVKpYKTkxMAxg2QjltRURGeeeYZrFmzBkFBQdXew7hVj1tWVhZOnDiBgIAA9OvXD4GBgRg0aBCOHDkivqepn9sA6eON1wTTZs2ahdGjRxuc/wHwulALU3GTwutClZrixusCERFRzZzsXYD7lUajwbx589C/f3907txZnP7999/jqaeegq+vL5ycnKBQKLBz5060a9cOgLa/i4CAAIN1OTk5wcfHBxkZGQ26Dw3p7NmziIyMRElJCTw8PLBz506Eh4cjKSkJLi4u8PLyMlg+MDBQjEdGRobBzZpuvm7eg8xU3IzduXMHH330kUHzFsZNOm7z589Hv379MHbsWMn3Mm7V43b8+HEAwJIlS7B8+XJ069YN0dHRGDZsGM6dO4fQ0NAme24Daj7eeE2QtmXLFpw6dQrx8fHV5mVkZPC6YEJNcTPG60KV2uLG6wIREVHNmDiy0qxZs3Du3DmDX9wB4P3330dOTg5++eUX+Pn5YdeuXZg4cSIOHz6MiIgIO5XW/sLCwpCUlITc3Fxs374dzz//POLi4uxdrEbPVNz0k0d5eXkYPXo0wsPDsWTJEvsVthExFbfU1FTExsYa9C9DVUzFTaPRAABefvllTJs2DQDQvXt3/Prrr/j666+xbNkyexbb7mr6nvKaUN21a9cwd+5cHDhwAK6urvYuzn3DkrjxulCltrjt3r2b1wUiIqJaMHFkhdmzZ4sdmAYHB4vT09LSsHr1apw7dw6dOnUCAHTt2hWHDx/GmjVrsG7dOgQFBSErK8tgfRUVFcjOzpasHv2gcHFxEX9h79mzJ+Lj47Fy5Uo89dRTKCsrQ05OjsGvy5mZmWI8goKCcPLkSYP16UbXeZBjBpiO2xdffAEAyM/Px8iRI6FUKrFz5044OzuL72XcqsfNzc0NaWlp1WoyTJgwAQMGDMDBgwcZN4m4LViwAACq1Xbr2LEjrl69CgBN9twGmI7b22+/zWuChMTERGRlZaFHjx7iNLVajUOHDmH16tX4+eefeV2QUFvcSktL4ejoyOuCkdriNnPmTF4XiIiIasE+jiwgCAJmz56NnTt3IjY2Fm3atDGYX1RUBAAGowwBgKOjo/iLfWRkJHJycpCYmCjOj42NhUajQZ8+fep5DxoPjUaD0tJS9OzZE87Ozvj111/FeSkpKbh69arYR0hkZCTOnj1r8HB14MABqFQqyWZbDzJd3ADtL8pRUVFwcXHB7t27q/2SyrhV0cVtwYIFSE5ORlJSkvgPAD799FNs2LABAOOmTxe31q1bo3nz5khJSTGY/+eff6JVq1YAeG7Tp4sbrwnShg0bhrNnzxp8D3v16oXJkyeLr3ldqK62uDk6OvK6IKG2uL333nu8LhAREdXG3r1z309mzpwpeHp6CgcPHhRu3bol/isqKhIEQRDKysqEdu3aCQMGDBBOnDghpKamCsuXLxdkMpmwZ88ecT0jR44UunfvLpw4cUI4cuSIEBoaKkyaNMleu1XvFixYIMTFxQmXL18WkpOThQULFggymUzYv3+/IAiC8MorrwghISFCbGyskJCQIERGRgqRkZHi+ysqKoTOnTsLUVFRQlJSkhATEyP4+/sL77zzjr12qUHUFLfc3FyhT58+QkREhJCammpwPFZUVAiCwLiZOt6MwWj0HMZNOm6ffvqpoFKphG3btgmXLl0SFi5cKLi6ugqpqaniOprauU0Qao4brwnmMx7litcF8+jHjdcF89U2Gh2vC0RERIaYOLIAAMl/GzZsEJf5888/hfHjxwsBAQGCQqEQunTpUm0o5rt37wqTJk0SPDw8BJVKJUybNk3Iz89v4L1pOC+88ILQqlUrwcXFRfD39xeGDRtm8BBfXFwsvPrqq4K3t7egUCiEcePGCbdu3TJYR3p6ujBq1CjBzc1N8PPzE9544w1xeOEHVU1x++2330wej5cvXxbXwbhVP96MGT8gCALjZipuy5YtE4KDgwWFQiFERkYKhw8fNpjf1M5tglB73HhNMI/xgzyvC+bRjxuvC+azNHEkCIwbERE1bTJBEISGrOFERERERERERET3B/ZxREREREREREREkpg4IiIiIiIiIiIiSUwcERERERERERGRJCaOiIiIiIiIiIhIEhNHREREREREREQkiYkjIiIiIiIiIiKSxMQRERERERERERFJYuKIiIiIiIiIiIgkMXFERNRETJ06FY8//rjdtj9lyhT84x//sNv2bWHjxo3w8vIya9mYmBh069YNGo2mfgtFRERERFSPmDgiInoAyGSyGv8tWbIEK1euxMaNG+1SvjNnzmDv3r2YM2eOXbZvDyNHjoSzszM2b95s76IQEREREVnNyd4FICKiurt165b4euvWrVi0aBFSUlLEaR4eHvDw8LBH0QAAq1atwpNPPmnXMtjD1KlT8dlnn2HKlCn2LgoRERERkVVY44iI6AEQFBQk/vP09IRMJjOY5uHhUa2p2uDBg/Haa69h3rx58Pb2RmBgINavX4/CwkJMmzYNSqUS7dq1w759+wy2de7cOYwaNQoeHh4IDAzElClTcOfOHZNlU6vV2L59O8aMGWMw/fPPP0doaChcXV0RGBiIJ554Qpyn0WiwbNkytGnTBm5ubujatSu2b99u8P7z58/j0UcfhUqlglKpxIABA5CWlia+/8MPP0RwcDDkcjm6deuGmJgY8b3p6emQyWTYsWMHhgwZAoVCga5du+LYsWMG29i4cSNCQkKgUCgwbtw43L1712D+mTNnMGTIECiVSqhUKvTs2RMJCQni/DFjxiAhIUEsFxERERHR/YaJIyKiJmzTpk3w8/PDyZMn8dprr2HmzJl48skn0a9fP5w6dQpRUVGYMmUKioqKAAA5OTkYOnQounfvjoSEBMTExCAzMxMTJ040uY3k5GTk5uaiV69e4rSEhATMmTMHH374IVJSUhATE4OBAweK85ctW4bo6GisW7cO58+fx/z58/Hss88iLi4OAHDjxg0MHDgQcrkcsbGxSExMxAsvvICKigoAwMqVK/Hvf/8by5cvR3JyMkaMGIHHHnsMly5dMijbe++9hzfffBNJSUlo3749Jk2aJK7jxIkTmD59OmbPno2kpCQMGTIES5cuNXj/5MmTERwcjPj4eCQmJmLBggVwdnYW54eEhCAwMBCHDx+25uMhIiIiIrI7mSAIgr0LQUREtrNx40bMmzcPOTk5BtOnTp2KnJwc7Nq1C4C2xpFarRaTGmq1Gp6enhg/fjyio6MBABkZGWjWrBmOHTuGvn37YunSpTh8+DB+/vlncb3Xr19Hy5YtkZKSgvbt21crz65du/DEE0+gvLwcMpkMALBjxw5MmzYN169fh1KpNFi+tLQUPj4++OWXXxAZGSlOnzFjBoqKivDtt9/i3XffxZYtW5CSkmKQqNFp0aIFZs2ahXfffYsXpX8AAATrSURBVFec1rt3bzz88MNYs2YN0tPT0aZNG3z55ZeYPn06AODChQvo1KkTLl68iA4dOuCZZ55Bbm4u9uzZI67j6aefRkxMjBhblUqFVatW4fnnnzf5efTo0QNjx47F4sWLTS5DRERERNRYscYREVET1qVLF/G1o6MjfH19ERERIU4LDAwEAGRlZQHQNs367bffxD6TPDw80KFDBwAw2RyruLgYcrlcTBoBwCOPPIJWrVqhbdu2mDJlCjZv3izWakpNTUVRUREeeeQRg+1ER0eL20hKSsKAAQMkk0Z5eXm4efMm+vfvbzC9f//+uHjxosn9b9asmcG+Xrx4EX369DFYXj+RBQCvv/46ZsyYgeHDh+Pjjz+WjIGbm5u4b0RERERE9xt2jk1E1IQZJ15kMpnBNF2yRzekfEFBAcaMGYNPPvmk2rp0iRdjfn5+KCoqQllZGVxcXAAASqUSp06dwsGDB7F//34sWrQIS5YsQXx8PAoKCgAAe/bsQYsWLQzWJZfLAWiTMbZQ076aY8mSJXjmmWewZ88e7Nu3D4sXL8aWLVswbtw4cZns7Gz4+/vbpLxERERERA2NNY6IiMhsPXr0wPnz59G6dWu0a9fO4J+7u7vke7p16wZA2xRMn5OTE4YPH45//vOfSE5ORnp6OmJjYxEeHg65XI6rV69W20bLli0BaGsKHT58GOXl5dW2p1Kp0Lx5cxw9etRg+tGjRxEeHm72vnbs2BEnTpwwmHb8+PFqy7Vv3x7z58/H/v37MX78eGzYsEGcV1JSgrS0NHTv3t3s7RIRERERNSZMHBERkdlmzZqF7OxsTJo0CfHx8UhLS8PPP/+MadOmQa1WS77H398fPXr0wJEjR8RpP/30Ez777DMkJSXhypUriI6OhkajQVhYGJRKJd58803Mnz8fmzZtQlpaGk6dOoVVq1Zh06ZNAIDZs2cjLy8PTz/9NBISEnDp0iV88803SElJAQC89dZb+OSTT7B161akpKRgwYIFSEpKwty5c83e1zlz5iAmJgbLly/HpUuXsHr1aoOR2YqLizF79mwcPHgQV65cwdGjRxEfH4+OHTuKyxw/fhxyubxaEzciIiIiovsFE0dERGQ2XU0etVqNqKgoREREYN68efDy8oKDg+lLyowZM7B582bxby8vL+zYsQNDhw5Fx44dsW7dOnz33Xfo1KkTAOCjjz7C+++/j2XLlqFjx44YOXIk9uzZgzZt2gAAfH19ERsbi4KCAgwaNAg9e/bE+vXrxaZnc+bMweuvv4433ngDERERiImJwe7duxEaGmr2vvbt2xfr16/HypUr0bVrV+zfvx8LFy4U5zs6OuLu3bt47rnn0L59e0ycOBGjRo3CBx98IC7z3XffYfLkyVAoFGZvl4iIiIioMeGoakREVO+Ki4sRFhaGrVu3NpnaN3fu3EFYWBgSEhLEhBcRERER0f2GNY6IiKjeubm5ITo6Gnfu3LF3URpMeno6Pv/8cyaNiIiIiOi+xhpHREREREREREQkiTWOiIiIiIiIiIhIEhNHREREREREREQkiYkjIiIiIiIiIiKSxMQRERERERERERFJYuKIiIiIiIiIiIgkMXFERERERERERESSmDgiIiIiIiIiIiJJTBwREREREREREZEkJo6IiIiIiIiIiEgSE0dERERERERERCTp/wNc5GCwj2pX7QAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ai.simulate(target_time)\n", "dd.viewHistory(ai.game_state)\n", "dd.cashGen(ai.game_state, parameters = parameters)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[(3,2,0) Farm ACTIVE, (3,2,0) Farm ACTIVE]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["ai.game_state.farms"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Comparing Against Human Play"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["buy_queue = [\n", "    [b2.sellFarm(index=1), b2.upgradeFarm(index = 0,upgrades = (4,2,0))]\n", "]\n", "\n", "eco_queue = [\n", "    b2.ecoSend(send_name = 'Grouped Blacks', time = 0),\n", "    b2.ecoSend(send_name = 'Zero', time = rounds.getTimeFromRound(19))\n", "]\n", "\n", "initial_state_game = {\n", "    'Cash': 0,\n", "    'Eco': 600,\n", "    'Eco Queue': eco_queue,\n", "    'Buy Queue': buy_queue,\n", "    'Rounds': rounds, #Determines the lengths of the rounds in the game state\n", "    'Farms': farms,\n", "    'Game Round': 13.99\n", "}"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'Time': 222.29999999999998, 'Send Name': 'Grouped Blacks', 'Max Send Amount': None, 'Fortified': False, '<PERSON>oflauge': False, '<PERSON><PERSON>': <PERSON><PERSON><PERSON>, '<PERSON> Eco Amount': None, 'Max Send Time': None, 'Queue Threshold': 6}, {'Time': 411.8, 'Send Name': 'Zero', 'Max Send Amount': None, 'Fortified': Fals<PERSON>, 'Camoflauge': <PERSON>als<PERSON>, '<PERSON><PERSON>': <PERSON><PERSON><PERSON>, 'Max Eco Amount': None, 'Max Send Time': None, 'Queue Threshold': 6}]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Time</th>\n", "      <th>Type</th>\n", "      <th>Message</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>284.8</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Grouped Blacks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>285.0</td>\n", "      <td>Round</td>\n", "      <td>Round 14 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>297.0</td>\n", "      <td>Round</td>\n", "      <td>Round 15 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>313.4</td>\n", "      <td>Round</td>\n", "      <td>Round 16 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>346.4</td>\n", "      <td>Round</td>\n", "      <td>Round 17 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>385.7</td>\n", "      <td>Round</td>\n", "      <td>Round 18 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>411.8</td>\n", "      <td>Round</td>\n", "      <td>Round 19 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>411.8</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Zero</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>438.0</td>\n", "      <td>Buy</td>\n", "      <td>Sell farm 1, Upgrade farm 0 to (4,2,0)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>448.2</td>\n", "      <td>Round</td>\n", "      <td>Round 20 start</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Time   Type                                 Message\n", "0  284.8    Eco            Change eco to Grouped Blacks\n", "1  285.0  Round                          Round 14 start\n", "2  297.0  Round                          Round 15 start\n", "3  313.4  Round                          Round 16 start\n", "4  346.4  Round                          Round 17 start\n", "5  385.7  Round                          Round 18 start\n", "6  411.8  Round                          Round 19 start\n", "7  411.8    Eco                      Change eco to Zero\n", "8  438.0    Buy  Sell farm 1, Upgrade farm 0 to (4,2,0)\n", "9  448.2  Round                          Round 20 start"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue</th>\n", "      <th>Expenses</th>\n", "      <th>Profit</th>\n", "      <th>Eco Impact</th>\n", "      <th>Start Time</th>\n", "      <th>End Time</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Farm Index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5450.0</td>\n", "      <td>21750</td>\n", "      <td>-16300.0</td>\n", "      <td>200.0</td>\n", "      <td>285.0</td>\n", "      <td>448.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>8725.0</td>\n", "      <td>0</td>\n", "      <td>8725.0</td>\n", "      <td>342.0</td>\n", "      <td>285.0</td>\n", "      <td>438.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            Revenue  Expenses   Profit  Eco Impact  Start Time  End Time\n", "Farm Index                                                              \n", "0            5450.0     21750 -16300.0       200.0       285.0     448.0\n", "1            8725.0         0   8725.0       342.0       285.0     438.0"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["27045.0"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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***************************************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*********************************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", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["game_state = b2.GameState(initial_state_game)\n", "game_state.fastForward(target_round=20)\n", "dd.viewH<PERSON><PERSON>(game_state)\n", "dd.cashGen(game_state, parameters = parameters)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["a = [1,2,3]\n", "b = [4,5,6]\n", "a = a + b"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4, 5, 6]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["a"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.9990788738501881"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["def foo(x):\n", "    return ((1/x)-1)*log(1-x)+1\n", "\n", "foo(0.9999)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 2}