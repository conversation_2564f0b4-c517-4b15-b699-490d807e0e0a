<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BTDB2 农场策略模拟器</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="app">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-seedling me-2"></i>
                    BTDB2 农场策略模拟器
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" @click="activeTab = 'simulator'" :class="{active: activeTab === 'simulator'}">
                                <i class="fas fa-calculator me-1"></i>模拟器
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" @click="activeTab = 'analysis'" :class="{active: activeTab === 'analysis'}">
                                <i class="fas fa-chart-line me-1"></i>分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" @click="activeTab = 'help'" :class="{active: activeTab === 'help'}">
                                <i class="fas fa-question-circle me-1"></i>帮助
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <div class="container-fluid mt-4">
            <!-- 模拟器标签页 -->
            <div v-if="activeTab === 'simulator'" class="row">
                <!-- 左侧控制面板 -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-cogs me-2"></i>模拟参数设置</h5>
                        </div>
                        <div class="card-body">
                            <!-- 初始状态设置 -->
                            <div class="mb-4">
                                <h6 class="text-primary">初始状态</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <label class="form-label">初始现金</label>
                                        <input type="number" class="form-control" v-model="simulation.initialCash" min="0">
                                    </div>
                                    <div class="col-6">
                                        <label class="form-label">初始经济</label>
                                        <input type="number" class="form-control" v-model="simulation.initialEco" min="0">
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <label class="form-label">开始回合</label>
                                        <input type="number" class="form-control" v-model="simulation.startRound" min="1" max="50">
                                    </div>
                                    <div class="col-6">
                                        <label class="form-label">结束回合</label>
                                        <input type="number" class="form-control" v-model="simulation.endRound" min="1" max="50">
                                    </div>
                                </div>
                            </div>

                            <!-- 农场设置 -->
                            <div class="mb-4">
                                <h6 class="text-primary">农场配置</h6>
                                <div class="farm-list">
                                    <div v-for="(farm, index) in simulation.farms" :key="index" class="farm-item mb-2 p-2 border rounded">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>农场 {{index + 1}}</span>
                                            <button class="btn btn-sm btn-danger" @click="removeFarm(index)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-4">
                                                <label class="form-label small">上路</label>
                                                <select class="form-select form-select-sm" v-model="farm.upgrades[0]">
                                                    <option v-for="i in 6" :key="i-1" :value="i-1">{{i-1}}</option>
                                                </select>
                                            </div>
                                            <div class="col-4">
                                                <label class="form-label small">中路</label>
                                                <select class="form-select form-select-sm" v-model="farm.upgrades[1]">
                                                    <option v-for="i in 6" :key="i-1" :value="i-1">{{i-1}}</option>
                                                </select>
                                            </div>
                                            <div class="col-4">
                                                <label class="form-label small">下路</label>
                                                <select class="form-select form-select-sm" v-model="farm.upgrades[2]">
                                                    <option v-for="i in 6" :key="i-1" :value="i-1">{{i-1}}</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                优质土壤折扣: {{farm.upgrades[2] > 0 ? '15%' : '无'}}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-success w-100" @click="addFarm()">
                                    <i class="fas fa-plus me-1"></i>添加农场
                                </button>
                            </div>

                            <!-- 经济设置 -->
                            <div class="mb-4">
                                <h6 class="text-primary">经济策略</h6>
                                <div class="mb-2">
                                    <label class="form-label">经济发送类型</label>
                                    <select class="form-select" v-model="simulation.ecoSend">
                                        <option value="Zero">零发送</option>
                                        <option value="Red">红气球</option>
                                        <option value="Blue">蓝气球</option>
                                        <option value="Green">绿气球</option>
                                        <option value="Yellow">黄气球</option>
                                        <option value="Pink">粉气球</option>
                                        <option value="White">白气球</option>
                                        <option value="Black">黑气球</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 控制按钮 -->
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" @click="runSimulation" :disabled="isSimulating">
                                    <i class="fas fa-play me-1" v-if="!isSimulating"></i>
                                    <i class="fas fa-spinner fa-spin me-1" v-else></i>
                                    {{isSimulating ? '模拟中...' : '开始模拟'}}
                                </button>
                                <button class="btn btn-secondary" @click="resetSimulation">
                                    <i class="fas fa-redo me-1"></i>重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧结果显示 -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-area me-2"></i>模拟结果</h5>
                        </div>
                        <div class="card-body">
                            <div v-if="simulationResults">
                                <!-- 结果摘要 -->
                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="stat-card bg-success text-white">
                                            <div class="stat-value">{{formatNumber(simulationResults.finalCash)}}</div>
                                            <div class="stat-label">最终现金</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-card bg-info text-white">
                                            <div class="stat-value">{{formatNumber(simulationResults.finalEco)}}</div>
                                            <div class="stat-label">最终经济</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-card bg-warning text-white">
                                            <div class="stat-value">{{formatNumber(simulationResults.totalRevenue)}}</div>
                                            <div class="stat-label">总收入</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-card bg-danger text-white">
                                            <div class="stat-value">{{formatNumber(simulationResults.totalExpenses)}}</div>
                                            <div class="stat-label">总支出</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 图表区域 -->
                                <div class="chart-container">
                                    <canvas id="simulationChart"></canvas>
                                </div>
                            </div>
                            <div v-else class="text-center text-muted py-5">
                                <i class="fas fa-chart-line fa-3x mb-3"></i>
                                <p>运行模拟以查看结果</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分析标签页 -->
            <div v-if="activeTab === 'analysis'" class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-analytics me-2"></i>策略分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>农场效率分析</h6>
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>农场类型</th>
                                                    <th>投资回报率</th>
                                                    <th>回本时间</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="analysis in farmAnalysis" :key="analysis.type">
                                                    <td>{{analysis.type}}</td>
                                                    <td>{{analysis.roi}}%</td>
                                                    <td>{{analysis.paybackTime}}s</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>优质土壤影响分析</h6>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        优质土壤现在提供15%的升级费用折扣（之前为20%）
                                    </div>
                                    <canvas id="qualitySoilChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 帮助标签页 -->
            <div v-if="activeTab === 'help'" class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-question-circle me-2"></i>使用帮助</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>程序功能介绍</h6>
                                    <p>BTDB2农场策略模拟器是专门为《气球塔防对战2》设计的经济策略分析工具。</p>
                                    <ul>
                                        <li><strong>农场经济模拟</strong>：精确模拟香蕉农场的收益和升级策略</li>
                                        <li><strong>经济系统模拟</strong>：模拟游戏中的经济发送机制</li>
                                        <li><strong>升级成本计算</strong>：包含优质土壤等折扣机制</li>
                                        <li><strong>策略优化</strong>：帮助玩家找到最优的农场策略</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>使用说明</h6>
                                    <ol>
                                        <li>设置初始游戏状态（现金、经济、回合）</li>
                                        <li>配置农场升级路径</li>
                                        <li>选择经济发送策略</li>
                                        <li>运行模拟查看结果</li>
                                        <li>在分析页面查看详细数据</li>
                                    </ol>
                                    
                                    <h6 class="mt-4">农场升级路径说明</h6>
                                    <ul>
                                        <li><strong>上路</strong>：香蕉农场 → 更多香蕉 → 香蕉种植园 → 香蕉研究设施 → 香蕉中心</li>
                                        <li><strong>中路</strong>：有价值的香蕉 → 猴子银行 → IMF贷款 → 猴子经济学</li>
                                        <li><strong>下路</strong>：EZ收集 → 优质土壤 → 长寿香蕉 → 有价值的香蕉 → 猴子华尔街</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
