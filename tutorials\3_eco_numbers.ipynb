{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Eco Break Conditions\n", "\n", "We can specify break conditions on our eco sends which, when met, force the simulator to stop eco'ing the current send and attempt to move on to the next send in the eco queue. In this tutorial document we utilize one such condition called `max_eco_amount` to determine optimal eco'ing during round 1 defense. We'll cover the following base cases from which the end user can develop further to optimize their strategies:\n", "\n", "1. Standard tack start with Jericho\n", "2. Perfect anti-stall tack start with <PERSON>\n", "3. Standard dartling start with Jericho\n", "4. <PERSON><PERSON><PERSON> start with Jericho\n", "\n", "Let's begin by importing the b2 module."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import b2sim.engine as b2\n", "import b2sim.analysis as dd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## More About Eco Sends\n", "\n", "The eco queue, defined as a list of `b2.ecoSend(time = x, send_name = y)` calls, can be used to tell the simulator that at time $x$, you wish to eco with the send $y$. However, in some game scenarios, players may desire to stop eco or switch eco sends not at a predetermined time, but rather when a break condition of some sort is met, such as reaching a certain amount of eco. \n", "\n", "By the way, if you need a quick reference for *all* the things you can do in the simulator, take a look at `actions.py`. You'll notice that there are more arguments available for the `ecoSend` call that we haven't yet gone into detail about yet!"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Eco Numbers\n", "\n", "When adding sends to the eco queue in the simulator, you can specify either a maximum eco amount `max_eco_amount` or a maximum send amount `max_send_amount`. \n", "1. If a value for `max_eco_amount` is specified, the simulator will attempt to switch to the next send in the eco queue (or the zero send if the queue is empty) once the max eco amount has been reached or exceeded.\n", "2. If a value for `max_send_amount` is specified, the simulator will attempt to switch to the next send in the eco queue (or the zero send if the queue is empty) once the specified bloon send has been sent the specified number of times."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The Power of Python\n", "\n", "Since we're going to be doing the same thing multiple times over in this document --- in this case, determining eco numbers for multiple different compositions, let's build a function to automate the process and cut down on the overall amount of lines of code needed."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def eco_numbers(rounds, buy_queue, red_eco_limit = 273, blue_eco_limit = 279, filename = 'eco_numbers', view_stats = True):\n", "\n", "    eco_queue = [\n", "            b2.ecoSend(time = rounds.getTimeFromRound(1) + 0.5, send_name = 'Grouped Reds', max_eco_amount = red_eco_limit),\n", "            b2.ecoSend(send_name = 'Spaced Blues', max_eco_amount = blue_eco_limit)\n", "    ]\n", "\n", "    initial_state_game = {\n", "        'Cash': 650,\n", "        'Eco': 250,\n", "        'Eco Send': b2.ecoSend(send_name='Zero'),\n", "        'Rounds': rounds,\n", "        'Game Round': 0,\n", "        'Buy Queue': buy_queue,\n", "        'Eco Queue': eco_queue\n", "    }\n", "\n", "    game_state = b2.GameState(initial_state_game)\n", "    game_state.fastForward(target_round = 2)\n", "\n", "    if view_stats:\n", "        dd.viewH<PERSON><PERSON>(game_state)\n", "        print(\"The current cash and eco are (%s,%s)\"%(game_state.cash, game_state.eco))\n", "        print(game_state.eco_queue)\n", "    \n", "    #Always write the log file! It's useful to look at\n", "    b2.writeLog(game_state.logs, filename = filename)\n", "\n", "    if len(game_state.buy_queue) == 0:\n", "        print(\"Success! Afforded all our defense in time!\")\n", "        return True\n", "    else:\n", "        return False\n", "        print(\"Failed! We missed our R1 defense!\")\n", "    "]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Standard Tack Start With Jericho (Round 1 vs Round 2)\n", "\n", "In the standard start, we get access to *5* eco ticks before round 2 starts, with those eco ticks occuring at times $6,12,18,24, 30$ respectively. For this example, I've decided to display the graphs so you can see exactly how our money evolves over time. However, for future examples, I'm just gonna run the code and return the result over whether we afford all our defense or not.\n", "\n", "**Warning**: The in-game eco counter is known to round incorrectly. While this says to do blues to 274, the eco counter will actually show *273* if you exactly as the simulator prescribes below."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Time</th>\n", "      <th>Type</th>\n", "      <th>Message</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.0</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Zero</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>6.0</td>\n", "      <td>Round</td>\n", "      <td>Round 1 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6.0</td>\n", "      <td>Buy</td>\n", "      <td>Buy Tack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6.5</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Grouped Reds</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>16.9</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Spaced Blues</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>26.2</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Zero</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>30.0</td>\n", "      <td>Buy</td>\n", "      <td>Buy Jericho</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>31.1</td>\n", "      <td>Round</td>\n", "      <td>Round 2 start</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Time   Type                     Message\n", "0   0.0    Eco          Change eco to Zero\n", "1   6.0  Round               Round 1 start\n", "2   6.0    Buy                    Buy Tack\n", "3   6.5    Eco  Change eco to Grouped Reds\n", "4  16.9    Eco  Change eco to Spaced Blues\n", "5  26.2    Eco          Change eco to Zero\n", "6  30.0    Buy                 Buy Jericho\n", "7  31.1  Round               Round 2 start"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["The current cash and eco are (12.600000000000136,276.2000000000001)\n", "[]\n", "Success! Afforded all our defense in time!\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["rounds = b2.Rounds(0.45)\n", "\n", "buy_queue = [\n", "    [b2.buyDefense(280 + 100 + 225, min_buy_time = rounds.getTimeFromRound(1), message = \"Buy Tack\")], #Buy 020 tack\n", "    [b2.buyDefense(850, min_buy_time = rounds.getTimeFromRound(1.5), message = \"Buy Jericho\")] #Buy Jericho\n", "]\n", "\n", "eco_numbers(rounds, buy_queue = buy_queue, red_eco_limit = 269, blue_eco_limit = 276, filename='eco_numbers_1')"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Perfect Anti-<PERSON><PERSON>ck Start with Jericho\n", "\n", "If the game is perfectly anti-stalled, we will have access to one less eco tick than usual, meaning we have to adjust our strategy to get Jericho on R1. To compensate, we will not crosspath the tack and we will eco blues sooner before deciding to save for Jericho:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Success! Afforded all our defense in time!\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["rounds = b2.Rounds(0.0)\n", "\n", "buy_queue = [\n", "    [b2.buyDefense(280 + 100 + 225, min_buy_time = rounds.getTimeFromRound(1), message = \"Buy Tack\")], #Buy 020 tack\n", "    [b2.buyDefense(750, min_buy_time = rounds.getTimeFromRound(1.5), message = \"Buy Jericho\")] #Buy Jericho\n", "]\n", "\n", "eco_numbers(rounds, buy_queue = buy_queue, red_eco_limit = 263, blue_eco_limit = 267, filename='eco_numbers_2', view_stats = False)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Standard Dartling Start with Jericho\n", "\n", "It is reasonable to expect in most dartling alch farm games that the player will have access to 5 eco ticks, especially if the opponent is *also* playing Dartling."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Warning! The eco send Spaced Blues is not available yet! Switching to the zero send for now, we will attempt to use this send later.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Time</th>\n", "      <th>Type</th>\n", "      <th>Message</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.0</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Zero</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.0</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Zero</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6.0</td>\n", "      <td>Round</td>\n", "      <td>Round 1 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6.0</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Spaced Blues</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>12.0</td>\n", "      <td>Buy</td>\n", "      <td>Buy Dartling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>27.0</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Zero</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>31.1</td>\n", "      <td>Round</td>\n", "      <td>Round 2 start</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Time   Type                     Message\n", "0   0.0    Eco          Change eco to Zero\n", "1   0.0    Eco          Change eco to Zero\n", "2   6.0  Round               Round 1 start\n", "3   6.0    Eco  Change eco to Spaced Blues\n", "4  12.0    Buy                Buy Dartling\n", "5  27.0    Eco          Change eco to Zero\n", "6  31.1  Round               Round 2 start"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["The current cash and eco are (849.6000000000007,266.0000000000002)\n", "[]\n"]}, {"data": {"text/plain": ["False"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["rounds = b2.Rounds(0.45)\n", "\n", "buy_queue = [\n", "    [b2.buyDefense(800, min_buy_time = rounds.getTimeFromRound(1.1), message = \"Buy Dartling\")], #Buy 000 dartling\n", "    [b2.buyDefense(850, min_buy_time = rounds.getTimeFromRound(1.5), message = \"Buy Jericho\")] #Buy Jericho\n", "]\n", "\n", "eco_numbers(rounds, buy_queue = buy_queue, red_eco_limit = 249, blue_eco_limit = 266, filename='eco_numbers_3', view_stats = True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON> Start With Jericho"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Success! Afforded all our defense in time!\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["rounds = b2.Rounds(0.45)\n", "\n", "buy_queue = [\n", "    [b2.buyDefense(700, min_buy_time = rounds.getTimeFromRound(1.2), message = \"Buy Dartling\")], #Buy 000 mortar\n", "    [b2.buyDefense(850, min_buy_time = rounds.getTimeFromRound(1.5), message = \"Buy Jericho\")] #Buy Jericho\n", "]\n", "\n", "eco_numbers(rounds, buy_queue = buy_queue, red_eco_limit = 265, blue_eco_limit = 271, filename='eco_numbers_3', view_stats = False)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}