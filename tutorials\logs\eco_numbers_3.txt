MESSAGE FROM GameState.__init__(): 
Initialized Game State!
The current game round is 0
The current game time is 0 seconds
The game round start times are given by [0, 6.0, 31.075, 55.825, 82.025, 113.67500000000001, 143.775, 170.325, 197.325, 221.2, 246.1, 261.325, 279.55, 301.675, 323.27500000000003, 339.225, 361.82500000000005, 396.80000000000007, 438.67500000000007, 467.1000000000001, 506.42500000000007, 523.2750000000001, 543.5000000000001, 576.3750000000001, 592.6000000000001, 656.3750000000001, 701.5500000000001, 738.1750000000001, 769.1, 799.475, 850.6, 871.95, 930.019, 949.324, 981.8489999999999, 1000.06566, 1050.98266, 1082.4556599999999, 1174.61566, 1243.31149, 1343.0864900000001, 1369.93649, 1415.39149, 1450.16649, 1480.9414900000002, 1505.2664900000002, 1526.9414900000002, 1571.7164900000002, 1596.4914900000003, 1638.3864900000003, 1678.1614900000004, 1707.6114900000005] 

MESSAGE FROM GameState.fastForward: 
Advancing game to time 0.1
Modified the eco send to Zero
Advancing game to time 0.2
Advancing game to time 0.3
Advancing game to time 0.4
Advancing game to time 0.5
Advancing game to time 0.6
Advancing game to time 0.7
Advancing game to time 0.8
Advancing game to time 0.9
Advancing game to time 1.0
Advancing game to time 1.1
Advancing game to time 1.2
Advancing game to time 1.3
Advancing game to time 1.4
Advancing game to time 1.5
Advancing game to time 1.6
Advancing game to time 1.7
Advancing game to time 1.8
Advancing game to time 1.9
Advancing game to time 2.0
Advancing game to time 2.1
Advancing game to time 2.2
Advancing game to time 2.3
Advancing game to time 2.4
Advancing game to time 2.5
Advancing game to time 2.6
Advancing game to time 2.7
Advancing game to time 2.8
Advancing game to time 2.9
Advancing game to time 3.0
Advancing game to time 3.1
Advancing game to time 3.2
Advancing game to time 3.3
Advancing game to time 3.4
Advancing game to time 3.5
Advancing game to time 3.6
Advancing game to time 3.7
Advancing game to time 3.8
Advancing game to time 3.9
Advancing game to time 4.0
Advancing game to time 4.1
Advancing game to time 4.2
Advancing game to time 4.3
Advancing game to time 4.35
Advancing game to time 4.4
Advancing game to time 4.5
Advancing game to time 4.6
Advancing game to time 4.7
Advancing game to time 4.8
Advancing game to time 4.9
Advancing game to time 5.0
Advancing game to time 5.1
Advancing game to time 5.2
Advancing game to time 5.3
Advancing game to time 5.4
Advancing game to time 5.5
Advancing game to time 5.6
Advancing game to time 5.7
Advancing game to time 5.8
Advancing game to time 5.9
Advancing game to time 6.0
Awarded eco payment 250 at time 6
Recorded cash and eco values (900,250) at time 6
Advancing game to time 6.1
Advancing game to time 6.2
Advancing game to time 6.3
Advancing game to time 6.4
Advancing game to time 6.5
Modified the eco send to Grouped Reds
Advancing game to time 6.6
Sent a set of Grouped Reds at time 6.5
Currently, the send queue looks like this: 
[7.3]
Recorded cash and eco values (880.0,251.0) at time 6.6
Advancing game to time 6.7
Sent a set of Grouped Reds at time 6.65
Currently, the send queue looks like this: 
[7.3, 8.1]
Recorded cash and eco values (860.0,252.0) at time 6.7
Advancing game to time 6.8
Sent a set of Grouped Reds at time 6.800000000000001
Currently, the send queue looks like this: 
[7.3, 8.1, 8.9]
Recorded cash and eco values (840.0,253.0) at time 6.8
Advancing game to time 6.9
Advancing game to time 7.0
Sent a set of Grouped Reds at time 6.950000000000001
Currently, the send queue looks like this: 
[7.3, 8.1, 8.9, 9.700000000000001]
Recorded cash and eco values (820.0,254.0) at time 7.0
Advancing game to time 7.1
Advancing game to time 7.2
Sent a set of Grouped Reds at time 7.100000000000001
Currently, the send queue looks like this: 
[7.3, 8.1, 8.9, 9.700000000000001, 10.500000000000002]
Recorded cash and eco values (800.0,255.0) at time 7.2
Advancing game to time 7.3
Sent a set of Grouped Reds at time 7.250000000000002
Currently, the send queue looks like this: 
[7.3, 8.1, 8.9, 9.700000000000001, 10.500000000000002, 11.300000000000002]
Recorded cash and eco values (780.0,256.0) at time 7.3
Advancing game to time 7.4
Advancing game to time 7.5
Sent a set of Grouped Reds at time 7.400000000000002
Currently, the send queue looks like this: 
[8.1, 8.9, 9.700000000000001, 10.500000000000002, 11.300000000000002, 12.100000000000003]
Recorded cash and eco values (760.0,257.0) at time 7.5
Advancing game to time 7.6
Advancing game to time 7.7
Advancing game to time 7.8
Advancing game to time 7.9
Advancing game to time 8.0
Advancing game to time 8.1
Sent a set of Grouped Reds at time 8.1
Currently, the send queue looks like this: 
[8.9, 9.700000000000001, 10.500000000000002, 11.300000000000002, 12.100000000000003, 12.900000000000004]
Recorded cash and eco values (740.0,258.0) at time 8.1
Advancing game to time 8.15
Advancing game to time 8.2
Advancing game to time 8.3
Advancing game to time 8.4
Advancing game to time 8.5
Advancing game to time 8.6
Advancing game to time 8.65
Advancing game to time 8.7
Advancing game to time 8.8
Advancing game to time 8.9
Sent a set of Grouped Reds at time 8.9
Currently, the send queue looks like this: 
[9.700000000000001, 10.500000000000002, 11.300000000000002, 12.100000000000003, 12.900000000000004, 13.700000000000005]
Recorded cash and eco values (720.0,259.0) at time 8.9
Advancing game to time 9.0
Advancing game to time 9.1
Advancing game to time 9.15
Advancing game to time 9.2
Advancing game to time 9.3
Advancing game to time 9.4
Advancing game to time 9.5
Advancing game to time 9.6
Advancing game to time 9.7
Sent a set of Grouped Reds at time 9.700000000000001
Currently, the send queue looks like this: 
[10.500000000000002, 11.300000000000002, 12.100000000000003, 12.900000000000004, 13.700000000000005, 14.500000000000005]
Recorded cash and eco values (700.0,260.0) at time 9.7
Advancing game to time 9.8
Advancing game to time 9.9
Advancing game to time 10.0
Advancing game to time 10.1
Advancing game to time 10.2
Advancing game to time 10.3
Advancing game to time 10.4
Advancing game to time 10.5
Advancing game to time 10.6
Sent a set of Grouped Reds at time 10.500000000000002
Currently, the send queue looks like this: 
[11.300000000000002, 12.100000000000003, 12.900000000000004, 13.700000000000005, 14.500000000000005, 15.300000000000006]
Recorded cash and eco values (680.0,261.0) at time 10.6
Advancing game to time 10.7
Advancing game to time 10.8
Advancing game to time 10.9
Advancing game to time 11.0
Advancing game to time 11.1
Advancing game to time 11.2
Advancing game to time 11.3
Advancing game to time 11.4
Sent a set of Grouped Reds at time 11.300000000000002
Currently, the send queue looks like this: 
[12.100000000000003, 12.900000000000004, 13.700000000000005, 14.500000000000005, 15.300000000000006, 16.100000000000005]
Recorded cash and eco values (660.0,262.0) at time 11.4
Advancing game to time 11.5
Advancing game to time 11.6
Advancing game to time 11.7
Advancing game to time 11.8
Advancing game to time 11.9
Advancing game to time 12.0
Awarded eco payment 262.0 at time 12
Recorded cash and eco values (922.0,262.0) at time 12
We have 922.0 cash! We can do the next buy, which costs 700.0 and has a buffer of 0 and a minimum buy time of 11.01!
Completed the buy operation! The buy queue now has 1 items remaining in it
Recorded cash and eco values (222.0,262.0) at time 12.0
Advancing game to time 12.1
Advancing game to time 12.2
Sent a set of Grouped Reds at time 12.100000000000003
Currently, the send queue looks like this: 
[12.900000000000004, 13.700000000000005, 14.500000000000005, 15.300000000000006, 16.100000000000005, 16.900000000000006]
Recorded cash and eco values (202.0,263.0) at time 12.2
Advancing game to time 12.3
Advancing game to time 12.4
Advancing game to time 12.5
Advancing game to time 12.6
Advancing game to time 12.7
Advancing game to time 12.8
Advancing game to time 12.9
Advancing game to time 13.0
Sent a set of Grouped Reds at time 12.900000000000004
Currently, the send queue looks like this: 
[13.700000000000005, 14.500000000000005, 15.300000000000006, 16.100000000000005, 16.900000000000006, 17.700000000000006]
Recorded cash and eco values (182.0,264.0) at time 13.0
Advancing game to time 13.1
Advancing game to time 13.2
Advancing game to time 13.3
Advancing game to time 13.4
Advancing game to time 13.5
Advancing game to time 13.6
Advancing game to time 13.7
Advancing game to time 13.8
Sent a set of Grouped Reds at time 13.700000000000005
Currently, the send queue looks like this: 
[14.500000000000005, 15.300000000000006, 16.100000000000005, 16.900000000000006, 17.700000000000006, 18.500000000000007]
Reached the limit on eco'ing for this send! Moving to the next send in the queue.
The break time does not occur exactly when a payout is scheduled.
Modified the eco send to Spaced Blues
Advancing game to time 13.8
Recorded cash and eco values (162.0,265.0) at time 13.8
Advancing game to time 13.9
Advancing game to time 14.0
Advancing game to time 14.1
Advancing game to time 14.2
Advancing game to time 14.3
Advancing game to time 14.4
Advancing game to time 14.5
Advancing game to time 14.6
Sent a set of Spaced Blues at time 14.500000000000005
Currently, the send queue looks like this: 
[15.300000000000006, 16.100000000000005, 16.900000000000006, 17.700000000000006, 18.500000000000007, 20.000000000000007]
Recorded cash and eco values (147.0,265.8) at time 14.6
Advancing game to time 14.7
Advancing game to time 14.8
Advancing game to time 14.9
Advancing game to time 15.0
Advancing game to time 15.1
Advancing game to time 15.2
Advancing game to time 15.3
Advancing game to time 15.4
Sent a set of Spaced Blues at time 15.300000000000006
Currently, the send queue looks like this: 
[16.100000000000005, 16.900000000000006, 17.700000000000006, 18.500000000000007, 20.000000000000007, 21.500000000000007]
Recorded cash and eco values (132.0,266.6) at time 15.4
Advancing game to time 15.5
Advancing game to time 15.6
Advancing game to time 15.7
Advancing game to time 15.8
Advancing game to time 15.9
Advancing game to time 16.0
Advancing game to time 16.1
Advancing game to time 16.2
Sent a set of Spaced Blues at time 16.100000000000005
Currently, the send queue looks like this: 
[16.900000000000006, 17.700000000000006, 18.500000000000007, 20.000000000000007, 21.500000000000007, 23.000000000000007]
Recorded cash and eco values (117.0,267.4) at time 16.2
Advancing game to time 16.25
Advancing game to time 16.3
Advancing game to time 16.4
Advancing game to time 16.5
Advancing game to time 16.6
Advancing game to time 16.7
Advancing game to time 16.75
Advancing game to time 16.8
Advancing game to time 16.9
Advancing game to time 17.0
Sent a set of Spaced Blues at time 16.900000000000006
Currently, the send queue looks like this: 
[17.700000000000006, 18.500000000000007, 20.000000000000007, 21.500000000000007, 23.000000000000007, 24.500000000000007]
Recorded cash and eco values (102.0,268.2) at time 17.0
Advancing game to time 17.1
Advancing game to time 17.2
Advancing game to time 17.25
Advancing game to time 17.3
Advancing game to time 17.4
Advancing game to time 17.5
Advancing game to time 17.6
Advancing game to time 17.7
Advancing game to time 17.75
Sent a set of Spaced Blues at time 17.700000000000006
Currently, the send queue looks like this: 
[18.500000000000007, 20.000000000000007, 21.500000000000007, 23.000000000000007, 24.500000000000007, 26.000000000000007]
Recorded cash and eco values (87.0,269.0) at time 17.75
Advancing game to time 17.8
Advancing game to time 17.9
Advancing game to time 18.0
Awarded eco payment 269.0 at time 18
Recorded cash and eco values (356.0,269.0) at time 18
Advancing game to time 18.1
Advancing game to time 18.2
Advancing game to time 18.25
Advancing game to time 18.3
Advancing game to time 18.4
Advancing game to time 18.5
Advancing game to time 18.6
Sent a set of Spaced Blues at time 18.500000000000007
Currently, the send queue looks like this: 
[20.000000000000007, 21.500000000000007, 23.000000000000007, 24.500000000000007, 26.000000000000007, 27.500000000000007]
Recorded cash and eco values (341.0,269.8) at time 18.6
Advancing game to time 18.7
Advancing game to time 18.75
Advancing game to time 18.8
Advancing game to time 18.9
Advancing game to time 19.0
Advancing game to time 19.1
Advancing game to time 19.2
Advancing game to time 19.3
Advancing game to time 19.4
Advancing game to time 19.5
Advancing game to time 19.6
Advancing game to time 19.7
Advancing game to time 19.8
Advancing game to time 19.9
Advancing game to time 20.0
Advancing game to time 20.1
Sent a set of Spaced Blues at time 20.000000000000007
Currently, the send queue looks like this: 
[21.500000000000007, 23.000000000000007, 24.500000000000007, 26.000000000000007, 27.500000000000007, 29.000000000000007]
Recorded cash and eco values (326.0,270.6) at time 20.1
Advancing game to time 20.2
Advancing game to time 20.3
Advancing game to time 20.4
Advancing game to time 20.5
Advancing game to time 20.6
Advancing game to time 20.7
Advancing game to time 20.8
Advancing game to time 20.9
Advancing game to time 21.0
Advancing game to time 21.1
Advancing game to time 21.2
Advancing game to time 21.3
Advancing game to time 21.4
Advancing game to time 21.5
Advancing game to time 21.6
Sent a set of Spaced Blues at time 21.500000000000007
Currently, the send queue looks like this: 
[23.000000000000007, 24.500000000000007, 26.000000000000007, 27.500000000000007, 29.000000000000007, 30.500000000000007]
Reached the limit on eco'ing for this send! Moving to the next send in the queue.
No more sends in the eco queue! Switching to the zero send.
The break time does not occur exactly when a payout is scheduled.
Advancing game to time 21.6
Modified the eco send to Zero
Recorded cash and eco values (311.0,271.4) at time 21.6
Advancing game to time 21.7
Advancing game to time 21.8
Advancing game to time 21.9
Advancing game to time 22.0
Advancing game to time 22.1
Advancing game to time 22.2
Advancing game to time 22.3
Advancing game to time 22.4
Advancing game to time 22.5
Advancing game to time 22.6
Advancing game to time 22.7
Advancing game to time 22.8
Advancing game to time 22.9
Advancing game to time 23.0
Advancing game to time 23.1
Advancing game to time 23.2
Advancing game to time 23.3
Advancing game to time 23.4
Advancing game to time 23.5
Advancing game to time 23.6
Advancing game to time 23.7
Advancing game to time 23.8
Advancing game to time 23.9
Advancing game to time 24.0
Awarded eco payment 271.4 at time 24
Recorded cash and eco values (582.4,271.4) at time 24
Advancing game to time 24.1
Advancing game to time 24.2
Advancing game to time 24.3
Advancing game to time 24.4
Advancing game to time 24.5
Advancing game to time 24.6
Advancing game to time 24.7
Advancing game to time 24.8
Advancing game to time 24.9
Advancing game to time 25.0
Advancing game to time 25.1
Advancing game to time 25.2
Advancing game to time 25.3
Advancing game to time 25.4
Advancing game to time 25.5
Advancing game to time 25.6
Advancing game to time 25.7
Advancing game to time 25.8
Advancing game to time 25.9
Advancing game to time 26.0
Advancing game to time 26.1
Advancing game to time 26.2
Advancing game to time 26.3
Advancing game to time 26.4
Advancing game to time 26.5
Advancing game to time 26.6
Advancing game to time 26.7
Advancing game to time 26.8
Advancing game to time 26.9
Advancing game to time 27.0
Advancing game to time 27.1
Advancing game to time 27.2
Advancing game to time 27.3
Advancing game to time 27.4
Advancing game to time 27.5
Advancing game to time 27.6
Advancing game to time 27.7
Advancing game to time 27.8
Advancing game to time 27.9
Advancing game to time 28.0
Advancing game to time 28.1
Advancing game to time 28.2
Advancing game to time 28.3
Advancing game to time 28.4
Advancing game to time 28.5
Advancing game to time 28.6
Advancing game to time 28.7
Advancing game to time 28.8
Advancing game to time 28.9
Advancing game to time 29.0
Advancing game to time 29.1
Advancing game to time 29.2
Advancing game to time 29.3
Advancing game to time 29.4
Advancing game to time 29.5
Advancing game to time 29.6
Advancing game to time 29.7
Advancing game to time 29.8
Advancing game to time 29.9
Advancing game to time 30.0
Awarded eco payment 271.4 at time 30
Recorded cash and eco values (853.8,271.4) at time 30
We have 853.8 cash! We can do the next buy, which costs 850.0 and has a buffer of 0 and a minimum buy time of 18.54!
Completed the buy operation! The buy queue now has 0 items remaining in it
Recorded cash and eco values (3.8,271.4) at time 30.0
Advancing game to time 30.1
Advancing game to time 30.2
Advancing game to time 30.3
Advancing game to time 30.4
Advancing game to time 30.5
Advancing game to time 30.6
Advancing game to time 30.7
Advancing game to time 30.8
Advancing game to time 30.9
Advancing game to time 31.0
Advancing game to time 31.075
Advanced game state to round 2
The current time is 31.075
The next round starts at time 55.825
Our new cash and eco is given by (3.8,271.4) 

