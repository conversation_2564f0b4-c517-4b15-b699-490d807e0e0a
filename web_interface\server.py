#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BTDB2 农场策略模拟器 Web 服务器
使用 Flask 提供 REST API 接口
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / 'src'))

try:
    import b2sim.engine as b2
    B2SIM_AVAILABLE = True
    print("✓ b2sim 库加载成功")
except ImportError as e:
    B2SIM_AVAILABLE = False
    print(f"⚠ b2sim 库加载失败: {e}")
    print("将使用简化的模拟逻辑")

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 静态文件服务
@app.route('/')
def index():
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    return send_from_directory('.', filename)

@app.route('/api/simulate', methods=['POST'])
def simulate():
    """运行农场策略模拟"""
    try:
        data = request.json
        
        # 验证输入数据
        required_fields = ['initialCash', 'initialEco', 'startRound', 'endRound', 'farms', 'ecoSend']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必需字段: {field}'}), 400
        
        if B2SIM_AVAILABLE:
            # 使用真实的 b2sim 库进行模拟
            results = run_b2sim_simulation(data)
        else:
            # 使用简化的模拟逻辑
            results = run_simplified_simulation(data)
        
        return jsonify(results)
        
    except Exception as e:
        print(f"模拟错误: {e}")
        return jsonify({'error': str(e)}), 500

def run_b2sim_simulation(data):
    """使用真实的 b2sim 库运行模拟"""
    try:
        # 创建回合对象
        rounds = b2.Rounds(0.1)  # 使用默认的停滞因子
        
        # 准备农场列表
        farms = []
        for i, farm_data in enumerate(data['farms']):
            farm = b2.initFarm(
                purchase_time=rounds.getTimeFromRound(data['startRound']),
                upgrades=farm_data['upgrades']
            )
            farms.append(farm)
        
        # 创建经济发送
        eco_send = b2.ecoSend(send_name=data['ecoSend'])
        
        # 初始化游戏状态
        initial_state = {
            'Cash': data['initialCash'],
            'Eco': data['initialEco'],
            'Eco Send': eco_send,
            'Rounds': rounds,
            'Farms': farms,
            'Buy Queue': [],
            'Game Round': data['startRound']
        }
        
        # 创建游戏状态对象
        game_state = b2.GameState(initial_state)
        
        # 运行模拟
        game_state.fastForward(target_round=data['endRound'])
        
        # 收集结果数据
        time_data = []
        cash_data = []
        eco_data = []
        
        # 从游戏状态历史中提取数据
        for i in range(len(game_state.cash_history)):
            time_data.append(data['startRound'] + i)
            cash_data.append(int(game_state.cash_history[i]))
            eco_data.append(int(game_state.eco_history[i]))
        
        # 计算总收入和支出
        total_revenue = sum(farm.revenue for farm in game_state.farms)
        total_expenses = sum(farm.expenses for farm in game_state.farms)
        
        return {
            'finalCash': int(game_state.cash),
            'finalEco': int(game_state.eco),
            'totalRevenue': int(total_revenue),
            'totalExpenses': int(total_expenses),
            'timeData': time_data,
            'cashData': cash_data,
            'ecoData': eco_data,
            'farmDetails': [
                {
                    'upgrades': farm.upgrades,
                    'revenue': int(farm.revenue),
                    'expenses': int(farm.expenses),
                    'roi': round((farm.revenue / max(farm.expenses, 1) - 1) * 100, 2)
                }
                for farm in game_state.farms
            ]
        }
        
    except Exception as e:
        print(f"b2sim 模拟错误: {e}")
        # 如果 b2sim 模拟失败，回退到简化模拟
        return run_simplified_simulation(data)

def run_simplified_simulation(data):
    """简化的模拟逻辑（当 b2sim 不可用时使用）"""
    rounds = data['endRound'] - data['startRound'] + 1
    cash = data['initialCash']
    eco = data['initialEco']
    total_revenue = 0
    total_expenses = 0
    
    time_data = []
    cash_data = []
    eco_data = []
    
    # 计算农场成本
    farm_details = []
    for farm in data['farms']:
        farm_cost = calculate_farm_cost(farm['upgrades'])
        total_expenses += farm_cost
        cash -= farm_cost
        
        farm_details.append({
            'upgrades': farm['upgrades'],
            'expenses': farm_cost,
            'revenue': 0,
            'roi': 0
        })
    
    # 模拟每个回合
    for round_num in range(rounds):
        time = data['startRound'] + round_num
        
        # 经济收入
        eco_income = eco * 0.1
        cash += eco_income
        total_revenue += eco_income
        
        # 农场收入
        for i, farm in enumerate(data['farms']):
            farm_income = calculate_farm_income(farm['upgrades'])
            cash += farm_income
            total_revenue += farm_income
            farm_details[i]['revenue'] += farm_income
        
        # 经济增长
        if data['ecoSend'] != 'Zero':
            eco += get_eco_growth(data['ecoSend'])
        
        time_data.append(time)
        cash_data.append(int(cash))
        eco_data.append(int(eco))
    
    # 计算农场ROI
    for farm_detail in farm_details:
        if farm_detail['expenses'] > 0:
            farm_detail['roi'] = round((farm_detail['revenue'] / farm_detail['expenses'] - 1) * 100, 2)
    
    return {
        'finalCash': int(cash),
        'finalEco': int(eco),
        'totalRevenue': int(total_revenue),
        'totalExpenses': int(total_expenses),
        'timeData': time_data,
        'cashData': cash_data,
        'ecoData': eco_data,
        'farmDetails': farm_details
    }

def calculate_farm_cost(upgrades):
    """计算农场总成本"""
    cost = 1000  # 基础农场成本
    
    # 升级成本表
    upgrade_costs = [
        [550, 550, 2750, 16000, 66000],  # 上路
        [200, 700, 4400, 8000, 45000],   # 中路
        [400, 240, 2800, 13000, 46000]   # 下路
    ]
    
    for path in range(3):
        for tier in range(upgrades[path]):
            upgrade_cost = upgrade_costs[path][tier]
            
            # 应用优质土壤折扣（15%折扣）
            if path < 2 and upgrades[2] > 0:
                upgrade_cost *= 0.85
            
            cost += upgrade_cost
    
    return cost

def calculate_farm_income(upgrades):
    """计算农场每回合收入"""
    base_income = 40
    income = base_income
    
    # 根据升级调整收入
    if upgrades[0] >= 1: income *= 1.2
    if upgrades[0] >= 2: income *= 1.5
    if upgrades[0] >= 3: income *= 2.0
    if upgrades[0] >= 4: income *= 15
    
    if upgrades[1] >= 2: income *= 1.25
    if upgrades[1] >= 3: income *= 1.1  # 银行特殊机制
    
    return income

def get_eco_growth(send_type):
    """获取经济发送的增长值"""
    eco_values = {
        'Zero': 0,
        'Red': 1,
        'Blue': 2,
        'Green': 3,
        'Yellow': 4,
        'Pink': 5,
        'White': 6,
        'Black': 7
    }
    return eco_values.get(send_type, 0)

@app.route('/api/status', methods=['GET'])
def status():
    """获取服务器状态"""
    return jsonify({
        'status': 'running',
        'b2sim_available': B2SIM_AVAILABLE,
        'version': '1.0.0'
    })

if __name__ == '__main__':
    print("🚀 启动 BTDB2 农场策略模拟器服务器...")
    print(f"📁 项目根目录: {project_root}")
    print(f"🔧 b2sim 可用: {'是' if B2SIM_AVAILABLE else '否'}")
    print("🌐 访问 http://localhost:5000 查看界面")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
