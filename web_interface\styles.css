/* 全局样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
}

.nav-link {
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    margin-bottom: 20px;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    border: none;
}

.card-header h5 {
    margin: 0;
    font-weight: 600;
}

/* 农场项目样式 */
.farm-item {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.farm-item:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

/* 统计卡片样式 */
.stat-card {
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 图表容器样式 */
.chart-container {
    position: relative;
    height: 400px;
    margin-top: 20px;
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    border: none;
}

.btn-secondary {
    background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    border: none;
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 表格样式 */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(102, 126, 234, 0.05);
}

/* 警告框样式 */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-info {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 10px;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .chart-container {
        height: 300px;
    }
    
    .navbar-brand {
        font-size: 1.1rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeIn 0.5s ease-out;
}

/* 加载动画 */
.spinner-border {
    width: 1rem;
    height: 1rem;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 徽章样式 */
.badge {
    border-radius: 6px;
}

/* 进度条样式 */
.progress {
    border-radius: 8px;
    height: 8px;
}

.progress-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 模态框样式 */
.modal-content {
    border-radius: 10px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0;
}

/* 特殊效果 */
.glow {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 文本样式 */
.text-primary {
    color: #667eea !important;
}

.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 图标样式 */
.fas, .far {
    margin-right: 0.25rem;
}

/* 列表样式 */
.list-group-item {
    border-radius: 8px;
    margin-bottom: 5px;
    border: 1px solid #dee2e6;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}
