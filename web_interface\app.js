const { createApp } = Vue;

createApp({
    data() {
        return {
            activeTab: 'simulator',
            isSimulating: false,
            simulation: {
                initialCash: 650,
                initialEco: 0,
                startRound: 1,
                endRound: 20,
                farms: [],
                ecoSend: 'Zero'
            },
            simulationResults: null,
            farmAnalysis: [
                { type: '基础农场 (0-0-0)', roi: 12.5, paybackTime: 25.0 },
                { type: '更多香蕉 (1-0-0)', roi: 15.2, paybackTime: 22.3 },
                { type: '香蕉种植园 (2-0-0)', roi: 18.7, paybackTime: 19.8 },
                { type: '猴子银行 (0-3-0)', roi: 25.4, paybackTime: 15.2 },
                { type: '优质土壤 (0-0-2)', roi: 14.8, paybackTime: 21.5 }
            ],
            chart: null,
            qualitySoilChart: null
        }
    },
    mounted() {
        this.initializeCharts();
    },
    methods: {
        addFarm() {
            this.simulation.farms.push({
                upgrades: [0, 0, 0],
                purchaseTime: 0
            });
        },
        removeFarm(index) {
            this.simulation.farms.splice(index, 1);
        },
        async runSimulation() {
            this.isSimulating = true;

            try {
                // 调用后端API进行真实模拟
                const response = await axios.post('/api/simulate', {
                    initialCash: this.simulation.initialCash,
                    initialEco: this.simulation.initialEco,
                    startRound: this.simulation.startRound,
                    endRound: this.simulation.endRound,
                    farms: this.simulation.farms,
                    ecoSend: this.simulation.ecoSend
                });

                this.simulationResults = response.data;

                // 更新图表
                this.updateChart(response.data);

            } catch (error) {
                console.error('模拟出错:', error);
                // 如果后端不可用，使用本地计算作为后备
                const results = this.calculateSimulation();
                this.simulationResults = results;
                this.updateChart(results);
            } finally {
                this.isSimulating = false;
            }
        },
        calculateSimulation() {
            // 这里是简化的模拟计算逻辑
            // 在实际应用中，这里会调用Python后端的b2sim库
            
            const rounds = this.simulation.endRound - this.simulation.startRound + 1;
            let cash = this.simulation.initialCash;
            let eco = this.simulation.initialEco;
            let totalRevenue = 0;
            let totalExpenses = 0;
            
            const timeData = [];
            const cashData = [];
            const ecoData = [];
            
            // 计算农场成本
            this.simulation.farms.forEach(farm => {
                const farmCost = this.calculateFarmCost(farm.upgrades);
                totalExpenses += farmCost;
                cash -= farmCost;
            });
            
            // 模拟每个回合
            for (let round = 0; round < rounds; round++) {
                const time = this.simulation.startRound + round;
                
                // 经济收入
                const ecoIncome = eco * 0.1; // 简化的经济收入计算
                cash += ecoIncome;
                totalRevenue += ecoIncome;
                
                // 农场收入
                this.simulation.farms.forEach(farm => {
                    const farmIncome = this.calculateFarmIncome(farm.upgrades);
                    cash += farmIncome;
                    totalRevenue += farmIncome;
                });
                
                // 经济增长
                if (this.simulation.ecoSend !== 'Zero') {
                    eco += this.getEcoGrowth(this.simulation.ecoSend);
                }
                
                timeData.push(time);
                cashData.push(Math.round(cash));
                ecoData.push(Math.round(eco));
            }
            
            return {
                finalCash: Math.round(cash),
                finalEco: Math.round(eco),
                totalRevenue: Math.round(totalRevenue),
                totalExpenses: Math.round(totalExpenses),
                timeData,
                cashData,
                ecoData
            };
        },
        calculateFarmCost(upgrades) {
            // 农场基础成本
            let cost = 1000;
            
            // 升级成本
            const upgradeCosts = [
                [550, 550, 2750, 16000, 66000],  // 上路
                [200, 700, 4400, 8000, 45000],   // 中路
                [400, 240, 2800, 13000, 46000]   // 下路
            ];
            
            for (let path = 0; path < 3; path++) {
                for (let tier = 0; tier < upgrades[path]; tier++) {
                    let upgradeCost = upgradeCosts[path][tier];
                    
                    // 应用优质土壤折扣（15%折扣，即乘以0.85）
                    if (path < 2 && upgrades[2] > 0) {
                        upgradeCost *= 0.85;
                    }
                    
                    cost += upgradeCost;
                }
            }
            
            return cost;
        },
        calculateFarmIncome(upgrades) {
            // 简化的农场收入计算
            const baseIncome = 40;
            let income = baseIncome;
            
            // 根据升级调整收入
            if (upgrades[0] >= 1) income *= 1.2;
            if (upgrades[0] >= 2) income *= 1.5;
            if (upgrades[0] >= 3) income *= 2.0;
            if (upgrades[0] >= 4) income *= 15;
            
            if (upgrades[1] >= 2) income *= 1.25;
            if (upgrades[1] >= 3) income *= 1.1; // 银行特殊机制
            
            return income;
        },
        getEcoGrowth(sendType) {
            const ecoValues = {
                'Zero': 0,
                'Red': 1,
                'Blue': 2,
                'Green': 3,
                'Yellow': 4,
                'Pink': 5,
                'White': 6,
                'Black': 7
            };
            return ecoValues[sendType] || 0;
        },
        resetSimulation() {
            this.simulation = {
                initialCash: 650,
                initialEco: 0,
                startRound: 1,
                endRound: 20,
                farms: [],
                ecoSend: 'Zero'
            };
            this.simulationResults = null;
            if (this.chart) {
                this.chart.destroy();
                this.chart = null;
            }
        },
        formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        },
        initializeCharts() {
            // 初始化优质土壤对比图表
            const ctx = document.getElementById('qualitySoilChart');
            if (ctx) {
                this.qualitySoilChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['无优质土壤', '优质土壤(20%折扣)', '优质土壤(15%折扣)'],
                        datasets: [{
                            label: '升级成本',
                            data: [1000, 800, 850],
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.8)',
                                'rgba(54, 162, 235, 0.8)',
                                'rgba(75, 192, 192, 0.8)'
                            ],
                            borderColor: [
                                'rgba(255, 99, 132, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(75, 192, 192, 1)'
                            ],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '优质土壤折扣对比'
                            },
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '成本'
                                }
                            }
                        }
                    }
                });
            }
        },
        updateChart(results) {
            const ctx = document.getElementById('simulationChart');
            if (!ctx) return;
            
            if (this.chart) {
                this.chart.destroy();
            }
            
            this.chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: results.timeData,
                    datasets: [
                        {
                            label: '现金',
                            data: results.cashData,
                            borderColor: 'rgb(75, 192, 192)',
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            tension: 0.1,
                            yAxisID: 'y'
                        },
                        {
                            label: '经济',
                            data: results.ecoData,
                            borderColor: 'rgb(255, 99, 132)',
                            backgroundColor: 'rgba(255, 99, 132, 0.2)',
                            tension: 0.1,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '现金和经济随时间变化'
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '回合'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '现金'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '经济'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }
    }
}).mount('#app');
