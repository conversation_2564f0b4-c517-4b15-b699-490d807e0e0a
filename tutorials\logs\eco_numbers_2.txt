MESSAGE FROM GameState.__init__(): 
Initialized Game State!
The current game round is 0
The current game time is 0 seconds
The game round start times are given by [0, 6.0, 29.5, 52.0, 75.5, 104.0, 130.5, 153.0, 175.5, 196.0, 215.5, 226.0, 239.5, 256.0, 274.0, 285.0, 299.5, 332.0, 370.5, 396.0, 431.5, 438.0, 453.5, 483.0, 494.5, 554.0, 589.5, 616.0, 639.5, 662.0, 707.5, 714.0, 767.794, 782.824, 811.074, 825.0156599999999, 871.65766, 898.85566, 986.7406599999999, 1051.16149, 1146.66149, 1154.16149, 1195.34149, 1225.84149, 1252.34149, 1272.39149, 1289.79149, 1330.29149, 1350.79149, 1388.41149, 1423.91149, 1429.5114899999999] 

MESSAGE FROM GameState.fastForward: 
Advancing game to time 0.1
Modified the eco send to Zero
Advancing game to time 0.2
Advancing game to time 0.3
Advancing game to time 0.4
Advancing game to time 0.5
Advancing game to time 0.6
Advancing game to time 0.7
Advancing game to time 0.8
Advancing game to time 0.9
Advancing game to time 1.0
Advancing game to time 1.1
Advancing game to time 1.2
Advancing game to time 1.3
Advancing game to time 1.4
Advancing game to time 1.5
Advancing game to time 1.6
Advancing game to time 1.7
Advancing game to time 1.8
Advancing game to time 1.9
Advancing game to time 2.0
Advancing game to time 2.1
Advancing game to time 2.2
Advancing game to time 2.3
Advancing game to time 2.4
Advancing game to time 2.5
Advancing game to time 2.6
Advancing game to time 2.7
Advancing game to time 2.8
Advancing game to time 2.9
Advancing game to time 3.0
Advancing game to time 3.1
Advancing game to time 3.2
Advancing game to time 3.3
Advancing game to time 3.4
Advancing game to time 3.5
Advancing game to time 3.6
Advancing game to time 3.7
Advancing game to time 3.8
Advancing game to time 3.9
Advancing game to time 4.0
Advancing game to time 4.1
Advancing game to time 4.2
Advancing game to time 4.3
Advancing game to time 4.35
Advancing game to time 4.4
Advancing game to time 4.5
Advancing game to time 4.6
Advancing game to time 4.7
Advancing game to time 4.8
Advancing game to time 4.9
Advancing game to time 5.0
Advancing game to time 5.1
Advancing game to time 5.2
Advancing game to time 5.3
Advancing game to time 5.4
Advancing game to time 5.5
Advancing game to time 5.6
Advancing game to time 5.7
Advancing game to time 5.8
Advancing game to time 5.9
Advancing game to time 6.0
Awarded eco payment 250 at time 6
Recorded cash and eco values (900,250) at time 6
We have 900 cash! We can do the next buy, which costs 605 and has a buffer of 0 and a minimum buy time of 6.0!
Completed the buy operation! The buy queue now has 1 items remaining in it
Recorded cash and eco values (295,250) at time 6.0
Advancing game to time 6.1
Advancing game to time 6.2
Advancing game to time 6.3
Advancing game to time 6.4
Advancing game to time 6.5
Modified the eco send to Grouped Reds
Advancing game to time 6.6
Sent a set of Grouped Reds at time 6.5
Currently, the send queue looks like this: 
[7.3]
Recorded cash and eco values (275.0,251.0) at time 6.6
Advancing game to time 6.7
Sent a set of Grouped Reds at time 6.65
Currently, the send queue looks like this: 
[7.3, 8.1]
Recorded cash and eco values (255.0,252.0) at time 6.7
Advancing game to time 6.8
Sent a set of Grouped Reds at time 6.800000000000001
Currently, the send queue looks like this: 
[7.3, 8.1, 8.9]
Recorded cash and eco values (235.0,253.0) at time 6.8
Advancing game to time 6.9
Advancing game to time 7.0
Sent a set of Grouped Reds at time 6.950000000000001
Currently, the send queue looks like this: 
[7.3, 8.1, 8.9, 9.700000000000001]
Recorded cash and eco values (215.0,254.0) at time 7.0
Advancing game to time 7.1
Advancing game to time 7.2
Sent a set of Grouped Reds at time 7.100000000000001
Currently, the send queue looks like this: 
[7.3, 8.1, 8.9, 9.700000000000001, 10.500000000000002]
Recorded cash and eco values (195.0,255.0) at time 7.2
Advancing game to time 7.3
Sent a set of Grouped Reds at time 7.250000000000002
Currently, the send queue looks like this: 
[7.3, 8.1, 8.9, 9.700000000000001, 10.500000000000002, 11.300000000000002]
Recorded cash and eco values (175.0,256.0) at time 7.3
Advancing game to time 7.4
Advancing game to time 7.5
Sent a set of Grouped Reds at time 7.400000000000002
Currently, the send queue looks like this: 
[8.1, 8.9, 9.700000000000001, 10.500000000000002, 11.300000000000002, 12.100000000000003]
Recorded cash and eco values (155.0,257.0) at time 7.5
Advancing game to time 7.6
Advancing game to time 7.7
Advancing game to time 7.8
Advancing game to time 7.9
Advancing game to time 8.0
Advancing game to time 8.1
Sent a set of Grouped Reds at time 8.1
Currently, the send queue looks like this: 
[8.9, 9.700000000000001, 10.500000000000002, 11.300000000000002, 12.100000000000003, 12.900000000000004]
Recorded cash and eco values (135.0,258.0) at time 8.1
Advancing game to time 8.15
Advancing game to time 8.2
Advancing game to time 8.3
Advancing game to time 8.4
Advancing game to time 8.5
Advancing game to time 8.6
Advancing game to time 8.65
Advancing game to time 8.7
Advancing game to time 8.8
Advancing game to time 8.9
Sent a set of Grouped Reds at time 8.9
Currently, the send queue looks like this: 
[9.700000000000001, 10.500000000000002, 11.300000000000002, 12.100000000000003, 12.900000000000004, 13.700000000000005]
Recorded cash and eco values (115.0,259.0) at time 8.9
Advancing game to time 9.0
Advancing game to time 9.1
Advancing game to time 9.15
Advancing game to time 9.2
Advancing game to time 9.3
Advancing game to time 9.4
Advancing game to time 9.5
Advancing game to time 9.6
Advancing game to time 9.7
Sent a set of Grouped Reds at time 9.700000000000001
Currently, the send queue looks like this: 
[10.500000000000002, 11.300000000000002, 12.100000000000003, 12.900000000000004, 13.700000000000005, 14.500000000000005]
Recorded cash and eco values (95.0,260.0) at time 9.7
Advancing game to time 9.8
Advancing game to time 9.9
Advancing game to time 10.0
Advancing game to time 10.1
Advancing game to time 10.2
Advancing game to time 10.3
Advancing game to time 10.4
Advancing game to time 10.5
Advancing game to time 10.6
Sent a set of Grouped Reds at time 10.500000000000002
Currently, the send queue looks like this: 
[11.300000000000002, 12.100000000000003, 12.900000000000004, 13.700000000000005, 14.500000000000005, 15.300000000000006]
Recorded cash and eco values (75.0,261.0) at time 10.6
Advancing game to time 10.7
Advancing game to time 10.8
Advancing game to time 10.9
Advancing game to time 11.0
Advancing game to time 11.1
Advancing game to time 11.2
Advancing game to time 11.3
Advancing game to time 11.4
Sent a set of Grouped Reds at time 11.300000000000002
Currently, the send queue looks like this: 
[12.100000000000003, 12.900000000000004, 13.700000000000005, 14.500000000000005, 15.300000000000006, 16.100000000000005]
Recorded cash and eco values (55.0,262.0) at time 11.4
Advancing game to time 11.5
Advancing game to time 11.6
Advancing game to time 11.7
Advancing game to time 11.8
Advancing game to time 11.9
Advancing game to time 12.0
Awarded eco payment 262.0 at time 12
Recorded cash and eco values (317.0,262.0) at time 12
Advancing game to time 12.1
Advancing game to time 12.2
Sent a set of Grouped Reds at time 12.100000000000003
Currently, the send queue looks like this: 
[12.900000000000004, 13.700000000000005, 14.500000000000005, 15.300000000000006, 16.100000000000005, 16.900000000000006]
Reached the limit on eco'ing for this send! Moving to the next send in the queue.
The break time does not occur exactly when a payout is scheduled.
Modified the eco send to Spaced Blues
Advancing game to time 12.2
Recorded cash and eco values (297.0,263.0) at time 12.2
Advancing game to time 12.3
Advancing game to time 12.4
Advancing game to time 12.5
Advancing game to time 12.6
Advancing game to time 12.7
Advancing game to time 12.8
Advancing game to time 12.9
Advancing game to time 13.0
Sent a set of Spaced Blues at time 12.900000000000004
Currently, the send queue looks like this: 
[13.700000000000005, 14.500000000000005, 15.300000000000006, 16.100000000000005, 16.900000000000006, 18.400000000000006]
Recorded cash and eco values (282.0,263.8) at time 13.0
Advancing game to time 13.1
Advancing game to time 13.2
Advancing game to time 13.3
Advancing game to time 13.4
Advancing game to time 13.5
Advancing game to time 13.6
Advancing game to time 13.7
Advancing game to time 13.8
Sent a set of Spaced Blues at time 13.700000000000005
Currently, the send queue looks like this: 
[14.500000000000005, 15.300000000000006, 16.100000000000005, 16.900000000000006, 18.400000000000006, 19.900000000000006]
Recorded cash and eco values (267.0,264.6) at time 13.8
Advancing game to time 13.9
Advancing game to time 14.0
Advancing game to time 14.1
Advancing game to time 14.2
Advancing game to time 14.3
Advancing game to time 14.4
Advancing game to time 14.5
Advancing game to time 14.6
Sent a set of Spaced Blues at time 14.500000000000005
Currently, the send queue looks like this: 
[15.300000000000006, 16.100000000000005, 16.900000000000006, 18.400000000000006, 19.900000000000006, 21.400000000000006]
Recorded cash and eco values (252.0,265.4) at time 14.6
Advancing game to time 14.7
Advancing game to time 14.8
Advancing game to time 14.9
Advancing game to time 15.0
Advancing game to time 15.1
Advancing game to time 15.2
Advancing game to time 15.3
Advancing game to time 15.4
Sent a set of Spaced Blues at time 15.300000000000006
Currently, the send queue looks like this: 
[16.100000000000005, 16.900000000000006, 18.400000000000006, 19.900000000000006, 21.400000000000006, 22.900000000000006]
Recorded cash and eco values (237.0,266.2) at time 15.4
Advancing game to time 15.5
Advancing game to time 15.6
Advancing game to time 15.7
Advancing game to time 15.8
Advancing game to time 15.9
Advancing game to time 16.0
Advancing game to time 16.1
Advancing game to time 16.2
Sent a set of Spaced Blues at time 16.100000000000005
Currently, the send queue looks like this: 
[16.900000000000006, 18.400000000000006, 19.900000000000006, 21.400000000000006, 22.900000000000006, 24.400000000000006]
Reached the limit on eco'ing for this send! Moving to the next send in the queue.
No more sends in the eco queue! Switching to the zero send.
The break time does not occur exactly when a payout is scheduled.
Advancing game to time 16.2
Modified the eco send to Zero
Recorded cash and eco values (222.0,267.0) at time 16.2
Advancing game to time 16.25
Advancing game to time 16.3
Advancing game to time 16.4
Advancing game to time 16.5
Advancing game to time 16.6
Advancing game to time 16.7
Advancing game to time 16.75
Advancing game to time 16.8
Advancing game to time 16.9
Advancing game to time 17.0
Advancing game to time 17.1
Advancing game to time 17.2
Advancing game to time 17.25
Advancing game to time 17.3
Advancing game to time 17.4
Advancing game to time 17.5
Advancing game to time 17.6
Advancing game to time 17.7
Advancing game to time 17.75
Advancing game to time 17.8
Advancing game to time 17.9
Advancing game to time 18.0
Awarded eco payment 267.0 at time 18
Recorded cash and eco values (489.0,267.0) at time 18
Advancing game to time 18.1
Advancing game to time 18.2
Advancing game to time 18.25
Advancing game to time 18.3
Advancing game to time 18.4
Advancing game to time 18.5
Advancing game to time 18.6
Advancing game to time 18.7
Advancing game to time 18.75
Advancing game to time 18.8
Advancing game to time 18.9
Advancing game to time 19.0
Advancing game to time 19.1
Advancing game to time 19.2
Advancing game to time 19.3
Advancing game to time 19.4
Advancing game to time 19.5
Advancing game to time 19.6
Advancing game to time 19.7
Advancing game to time 19.8
Advancing game to time 19.9
Advancing game to time 20.0
Advancing game to time 20.1
Advancing game to time 20.2
Advancing game to time 20.3
Advancing game to time 20.4
Advancing game to time 20.5
Advancing game to time 20.6
Advancing game to time 20.7
Advancing game to time 20.8
Advancing game to time 20.9
Advancing game to time 21.0
Advancing game to time 21.1
Advancing game to time 21.2
Advancing game to time 21.3
Advancing game to time 21.4
Advancing game to time 21.5
Advancing game to time 21.6
Advancing game to time 21.7
Advancing game to time 21.8
Advancing game to time 21.9
Advancing game to time 22.0
Advancing game to time 22.1
Advancing game to time 22.2
Advancing game to time 22.3
Advancing game to time 22.4
Advancing game to time 22.5
Advancing game to time 22.6
Advancing game to time 22.7
Advancing game to time 22.8
Advancing game to time 22.9
Advancing game to time 23.0
Advancing game to time 23.1
Advancing game to time 23.2
Advancing game to time 23.3
Advancing game to time 23.4
Advancing game to time 23.5
Advancing game to time 23.6
Advancing game to time 23.7
Advancing game to time 23.8
Advancing game to time 23.9
Advancing game to time 24.0
Awarded eco payment 267.0 at time 24
Recorded cash and eco values (756.0,267.0) at time 24
We have 756.0 cash! We can do the next buy, which costs 750.0 and has a buffer of 0 and a minimum buy time of 17.75!
Completed the buy operation! The buy queue now has 0 items remaining in it
Recorded cash and eco values (6.0,267.0) at time 24.0
Advancing game to time 24.1
Advancing game to time 24.2
Advancing game to time 24.3
Advancing game to time 24.4
Advancing game to time 24.5
Advancing game to time 24.6
Advancing game to time 24.7
Advancing game to time 24.8
Advancing game to time 24.9
Advancing game to time 25.0
Advancing game to time 25.1
Advancing game to time 25.2
Advancing game to time 25.3
Advancing game to time 25.4
Advancing game to time 25.5
Advancing game to time 25.6
Advancing game to time 25.7
Advancing game to time 25.8
Advancing game to time 25.9
Advancing game to time 26.0
Advancing game to time 26.1
Advancing game to time 26.2
Advancing game to time 26.3
Advancing game to time 26.4
Advancing game to time 26.5
Advancing game to time 26.6
Advancing game to time 26.7
Advancing game to time 26.8
Advancing game to time 26.9
Advancing game to time 27.0
Advancing game to time 27.1
Advancing game to time 27.2
Advancing game to time 27.3
Advancing game to time 27.4
Advancing game to time 27.5
Advancing game to time 27.6
Advancing game to time 27.7
Advancing game to time 27.8
Advancing game to time 27.9
Advancing game to time 28.0
Advancing game to time 28.1
Advancing game to time 28.2
Advancing game to time 28.3
Advancing game to time 28.4
Advancing game to time 28.5
Advancing game to time 28.6
Advancing game to time 28.7
Advancing game to time 28.8
Advancing game to time 28.9
Advancing game to time 29.0
Advancing game to time 29.1
Advancing game to time 29.2
Advancing game to time 29.3
Advancing game to time 29.4
Advancing game to time 29.5
Advanced game state to round 2
The current time is 29.5
The next round starts at time 52.0
Our new cash and eco is given by (6.0,267.0) 

