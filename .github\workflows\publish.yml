name: Test and Publish to PyPI

on:
  push:
    tags:
      - 'v*.*.*'
    branches:
      - main

permissions:
  contents: write

jobs:
  build-and-publish:
    runs-on: ubuntu-latest

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v3

      - name: 🐍 Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
        
      - name: 🔧 Install build
        run: |
          python -m pip install --upgrade pip
          pip install build
      
      - name: 📦 Build package
        run: |
          python -m build

      - name: 🚀 Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.PYPI_API_TOKEN }}
      
      - name: Create GitHub Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ github.ref_name }}
          name: Release ${{ github.ref_name }}
          files: |
            dist/*.whl
            dist/*.tar.gz

