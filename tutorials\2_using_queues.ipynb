{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Using Queues\n", "\n", "During actual games, players will have a predetermined set of purchases they intend to make and also a predetermined way of eco'ing that they've flowcharted out. The simulator can model these sorts of actions with the *buy queue* and the *eco queue*. In this tutorial, I'll show how to simulate flowcharts using these two features. Like last time, let's import the `b2sim` module:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import b2sim.engine as b2\n", "import b2sim.analysis as dd"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Round Lengths\n", "\n", "In BTD Battles 2, the length of a round depends on how fast either player pops or leaks all of the natural bloons on their side of the screen. There are *four* timers that govern round length:\n", "\n", "1. The *single* anti-stall timer (4 seconds) - This timer triggers when *one* player has popped all of the natural bloons on their side.\n", "2. The *double* anti-stall timer (2 seconds) - This timer triggers when *both* players have popped all of the natural bloons on their side.\n", "3. The minimum round length timer (5.5 seconds) - This timer triggers when the last natural bloon of the round spawns.\n", "4. The maximum round length timer (8.5 + `round number` seconds) - This timer also triggers when the last natural bloon of the round spawns.\n", "\n", "Rounds end once the minimum round length timer is completely exhausted *and* any of the other three timers completely run through. In ideal perfect anti-stall, rounds end 5.5 seconds after the last natural spawns. In ideal stall, rounds only end once the maxmimum round length timer is completely exhausted. In practice, however, achieving theoretical full stall beyond the first few rounds of the game is virtually impossible."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The Rounds class\n", "\n", "The rounds class is the simulator's tool for automatically and quickly generating round lengths true to actual games. Accurate round lengths is the key to accurate simulation, and the rounds class has four different modes for generating round lengths:\n", "1. **Stall Factor** - Generates round lengths based on a perceived level of stall ranging on $0$ (full anti-stall) to $1$ (full stall). The user can specify different stall factors for different rounds if desired. The round lengths are computed based on data collected by spoonoil and ninjayas in October 2022 --- or rather, a modified version of such which accounts for NK's recent changes to round length rules.\n", "2. **Stall Times** - The user specifies the amount of time each round takes after the last natural bloon spawns to end.\n", "3. **Manual** - The user manually inputs the round lengths they want to work with.\n", "4. **Theoretical Stall Factor** - Like stall factor, except round lengths are computed using the minimum and maximum length timers. That is, a stall factor of $1$ assumes that the maximum round length timer is exhausted and the next round might start without every natural having been popped beforehand.\n", "\n", "I recommend the stall factor mode if you need to quickly generate reasonably accurate simulations. The stall times mode may be a bit more finicky but can generate more precise results. The greatest precision (but perhaps most amount of tedium) is afforded by using the manual mode. To initialize the rounds class, we can use a structure as follows:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["rounds = b2.Rounds(info = [(1,9.5), (2,7), (10,5.75)], mode = 'Stall Times')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the stall times mode, each tuple $(r,t)$ informs the simulator on round $r$ that the round is stalled by $t$ seconds. When a stall time is not explicitly specified for a given round, the simulator searches backwards until it finds a round whose stall time *has* been specified and uses that value. The above initiliazes the `Rounds` class assigning stall times to each round as follows:\n", "\n", "| Rounds | Stall Times |\n", "|-------|----------|\n", "| 1   | 9.5 |\n", "| 2 - 9    | 7 |\n", "| 10 - 50    | 5.75 |\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## The Eco Queue\n", "\n", "The eco queue is a list which tells us what eco sends to use and at what points of the game to use them. For example, a typical midgame eco flowchart *might* look something like:\n", "\n", "| Round | Eco Send |\n", "|-------|----------|\n", "| 10    | Grouped Reds |\n", "| 12    | Spaced Rainbows  |\n", "| 13    | None |\n", "| 14    | Grouped Blacks |\n", "\n", "Implementing something like this is relatively easy in the eco sim. All we need to do is write something like..."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["eco_queue = [\n", "    b2.ecoSend(time=rounds.getTimeFromRound(10), send_name='Grouped Reds'),\n", "    b2.ecoSend(time=rounds.getTimeFromRound(12), send_name='Spaced Rainbows'),\n", "    b2.ecoSend(time=rounds.getTimeFromRound(13), send_name='Zero'),\n", "    b2.ecoSend(time=rounds.getTimeFromRound(14), send_name='Grouped Blacks')\n", "]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## The Buy Queue\n", "\n", "The buy queue is a list of lists. Each item in the buy queue prescribes a list of actions the player wishes to take. Every time the player receives a payment in the simulator, the simulator will check whether there is enough cash to execute the next item in the buy queue. In most cases (such as this example), items in the buy queue will just be lists with one action, but for cases like selling farms into better farm upgrades or more defense, it is useful to have an item in the buy queue which prescribes multiple actions to be taken simultaneously. For a full list of actions and their arguments, the player should refer to `actions.py`.\n", "\n", "A typical list of buy queue actions for rounds 10 - 14 for dartling alch eco could look something like:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["buy_queue = [\n", "    [b2.buyDefense(5600 - 0.7*2500, message=\"sell into hrp\")], #Sell into HRP\n", "    [b2.buyDefense(2550,min_buy_time=rounds.getTimeFromRound(14), message=\"buy l2g\")] #Buy Lead to Gold\n", "]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Simulation Example\n", "\n", "We consider dartling alch eco play from rounds 10 to 15 with the given rounds object and eco and buy queues given above:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Time</th>\n", "      <th>Type</th>\n", "      <th>Message</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>231.5</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Grouped Reds</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>240.0</td>\n", "      <td>Buy</td>\n", "      <td>sell into hrp</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>242.2</td>\n", "      <td>Round</td>\n", "      <td>Round 11 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>256.0</td>\n", "      <td>Round</td>\n", "      <td>Round 12 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>256.0</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Spaced Rainbows</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>272.8</td>\n", "      <td>Round</td>\n", "      <td>Round 13 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>272.8</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Zero</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>291.0</td>\n", "      <td>Round</td>\n", "      <td>Round 14 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>291.0</td>\n", "      <td>Buy</td>\n", "      <td>buy l2g</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>291.0</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Grouped Blacks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>302.2</td>\n", "      <td>Round</td>\n", "      <td>Round 15 start</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Time   Type                        Message\n", "0   231.5    Eco     Change eco to Grouped Reds\n", "1   240.0    Buy                  sell into hrp\n", "2   242.2  Round                 Round 11 start\n", "3   256.0  Round                 Round 12 start\n", "4   256.0    Eco  Change eco to Spaced Rainbows\n", "5   272.8  Round                 Round 13 start\n", "6   272.8    Eco             Change eco to Zero\n", "7   291.0  Round                 Round 14 start\n", "8   291.0    Buy                        buy l2g\n", "9   291.0    Eco   Change eco to Grouped Blacks\n", "10  302.2  Round                 Round 15 start"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["The current cash and eco are (24.0,955.0)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["initial_state_game = {\n", "    'Cash': 2800,\n", "    'Eco': 700,\n", "    'Rounds': rounds,\n", "    'Game Round': 10,\n", "    'Buy Queue': buy_queue,\n", "    'Eco Queue': eco_queue\n", "}\n", "\n", "game_state = b2.GameState(initial_state_game)\n", "game_state.fastForward(target_round = 15)\n", "dd.viewH<PERSON><PERSON>(game_state)\n", "b2.writeLog(game_state.logs, filename = 'using_queues')\n", "print(\"The current cash and eco are (%s,%s)\"%(game_state.cash, game_state.eco))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## More about Minimum Buy Time + Buffer\n", "\n", "Most buy queue actions support an argument `min_buy_time`, which tells the simulator to wait until the specified time to attempt the action. This is most useful for timing complex farm flowcharts (like selling into Monkey Wall Street just before the end of R24) but is a useful QOL feature even for mapping out general flowcharts.\n", "\n", "Most buy queue actions *also* support an argument called `buffer`. The buffer argument for a given action waits until you have the necessary cash for that action *plus* the specified buffer amount before actually carrying out that action. This is useful for scenarios where you are making purchases while eco'ing and wish to make your purchases only when they do not interfere with your eco."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}