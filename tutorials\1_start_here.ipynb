{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Welcome!\n", "\n", "The purpose of this file (and the ones that follow after it) is to give users a quick overview of how to run the eco sim.  After reading through the tutorial files, you should be able to quickly harness most facets of the simulator with ease. \n", "\n", "To begin, you'll need to install the simulator on your computer by running `pip install b2sim`. Once you do this, it's time to import the libraries..."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import b2sim.engine as b2\n", "import b2sim.analysis as dd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The libary is split into two subpackages, the \"engine\" subpackage, which concerns the construction and execution of simulations, and the \"analysis\" subpackage, which concerns displaying the results of the simulation for analysis."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Your First Simulation\n", "\n", "To create a simulation using the b2sim module, you'll need to follow this procedure:\n", "1. Determine the lengths of the rounds in the simulator using the `Rounds` class.\n", "2. Define the farms that you have at simulation start by using the `initFarm` call.\n", "3. Specify the purchases you intend to make (buy queue) and the eco flowchart you intend to follow (eco queue).\n", "4. Define the initial state of the game. (What round should the simulation start on, and how much cash and eco will you have at that time?)\n", "5. Simulate to the time that you want to simulate to.\n", "\n", "Depending on what you intend to simulate, some the above steps may either change slightly or may not be necessary at all. For example, if you're playing eco, step 2 obviously is not necessary. If you're playing with boat farms, in step 2 you'll declare those farms. If you don't intend to make any purchases or change your eco send throughout the simulation, step 3 is not necessary. Steps 1, 4, and 5 are always necessary, however.\n", "\n", "For our first simulation, we will keep things simple by working through an example which does not require use of the buy queue and eco features (we'll explain these more later)."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Eco'ing With Farms (The Basics)\n", "\n", "The name \"eco simulator\" is a bit of misnomer, because in fact the sim can handle computations concerning simultaneous investment into farms and eco. To begin, I'm going to run a simulation from *just* before the start of round 14 to the start of round 17 where the player has access to $2 \\times 320$ farms, $800$ eco, and decides to send grouped black eco the whole way through the simulation. We begin by following the 5 step process outlined above:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 1: Determine the Round Lengths\n", "\n", "The rounds class determines how long the rounds are in our simulation. The easiest way to initialize it is to simply use a number from $0$ to $1$ representing the level of stall of *stall factor* of each of the rounds. A stall factor of $0$ indicates a fully anti-stalled game while a stall factor of $1$ indicates a fully stalled game. You can be as detailed as setting the round lengths manually if you like, but for now, we'll stick to this simple syntax and explain less coarse methods for determining round lengths later."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["rounds = b2.Rounds(0.1)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Step 2: Define the farms at initial time\n", "\n", "Farms should be declared as a list of `initFarm` calls. Within the `initFarm` call, the first argument denotes when the farm was purchased/last upgraded (measured as seconds after game start), while the second argument determines what upgrades the farms have. Setting a purchase time is important to the extent that farms have different rules for paying out when bought mid-round versus before a round. For more details, see the tutorial files on farms. \n", "\n", "In almost all cases, it is more useful rather than trying to set a time directly to set the time based on rounds by using `rounds.getTimeFromRound`. Decimal values can be used to specify times between rounds, so `round.getTimeFromRound(13.9)` is interpreted as \"9/10ths the way through Round 13\"."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["farms = [\n", "    b2.initFarm(rounds.getTimeFromRound(7), upgrades = [3,2,0]),\n", "    b2.initFarm(rounds.getTimeFromRound(13.9), upgrades = [3,2,0])\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 3: Specify the flowchart\n", "\n", "In our case, there is no proper flowchart for us to specify since we do not intend to change eco sends or make purchases during the simulation. In the next section, however, we will go over this step in more detail."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 4: Define the initial state of the game\n", "\n", "The initial state contains all the important information necessary that the simulator needs to run. Generally speaking, every initial state *should* contain the following:\n", "1. Cash - Specifies starting cash.\n", "2. Eco - Specifies starting eco.\n", "3. <PERSON><PERSON> - Specifies current debt. You can leave this blank if you don't have any debt.\n", "4. Eco Send - Specifies the eco send to begin the simulation with. You can leave this blank if you instead the place the starting send in the eco queue.\n", "5. <PERSON><PERSON>ue - Specifies what eco sends the player intends to use throughout the simulation and what times to use them.\n", "6. Buy Queue - Specifies what purchases the player intends to make throughout the simulation.\n", "7. Rounds - Put the `Rounds` object that you initialized earlier here. This determines the round lengths in the simulation.\n", "8. Farms - Specifies what farms you have at simulation start. You can leave this blank if your simulation doesn't have farms\n", "9. Game Round / Game Time - Specifies when the simulation starts."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["initial_state_game = {\n", "    'Cash': 0,\n", "    'Eco': 800,\n", "    'Eco Send': b2.ecoSend(send_name = 'Grouped Blacks'),\n", "    'Rounds': rounds, #Determines the lengths of the rounds in the game state\n", "    'Farms': farms,\n", "    'Game Round': 13.99\n", "}"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Step 5: Simulate! \n", "\n", "The hardest part of running the code is setting up the simulation. Running the simulation itself is actually easy! The vehicle for simulation is the `GameState` class. By initializing a `GameState` object with our initial state defined above, we can simulate to whatever time we desire by using the `GameState` class's `fastForward()` method along with a time we want to simulate to. To view the results of the simulation afterwards, use the `viewHistory()` method from the `analysis` subclass."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'Time': 222.29999999999998, 'Send Name': 'Grouped Blacks', 'Max Send Amount': None, 'Fortified': <PERSON><PERSON><PERSON>, '<PERSON><PERSON>lau<PERSON>': <PERSON>als<PERSON>, '<PERSON><PERSON>': <PERSON><PERSON><PERSON>, '<PERSON> Eco Amount': None, 'Max Send Time': None, 'Queue Threshold': 6}]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Time</th>\n", "      <th>Type</th>\n", "      <th>Message</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>284.8</td>\n", "      <td>Eco</td>\n", "      <td>Change eco to Grouped Blacks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>285.0</td>\n", "      <td>Round</td>\n", "      <td>Round 14 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>297.0</td>\n", "      <td>Round</td>\n", "      <td>Round 15 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>313.4</td>\n", "      <td>Round</td>\n", "      <td>Round 16 start</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>346.4</td>\n", "      <td>Round</td>\n", "      <td>Round 17 start</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Time   Type                       Message\n", "0  284.8    Eco  Change eco to Grouped Blacks\n", "1  285.0  Round                Round 14 start\n", "2  297.0  Round                Round 15 start\n", "3  313.4  Round                Round 16 start\n", "4  346.4  Round                Round 17 start"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue</th>\n", "      <th>Expenses</th>\n", "      <th>Profit</th>\n", "      <th>Eco Impact</th>\n", "      <th>Start Time</th>\n", "      <th>End Time</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Farm Index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2450.0</td>\n", "      <td>0</td>\n", "      <td>2450.0</td>\n", "      <td>238.0</td>\n", "      <td>285.0</td>\n", "      <td>346.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2450.0</td>\n", "      <td>0</td>\n", "      <td>2450.0</td>\n", "      <td>238.0</td>\n", "      <td>285.0</td>\n", "      <td>346.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            Revenue  Expenses  Profit  Eco Impact  Start Time  End Time\n", "Farm Index                                                             \n", "0            2450.0         0  2450.0       238.0       285.0     346.0\n", "1            2450.0         0  2450.0       238.0       285.0     346.0"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["The current cash and eco are (1083.0,1391.0)\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABI0AAAJOCAYAAAA6ZPcUAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8fJSN1AAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdd3hTZfvA8W+atknbdA/2KIUCpTIFZZQCAmXJRhAHW2SIyk9QXl+hVRQZoggI4gBEEBTZIKWIKFPlBZQlssqW2UlH2vT8/ghJmw4obdqkeH+uqxc9J885eU7O6SG5cz/3o1IURUEIIYQQQgghhBBCiBwcbN0BIYQQQgghhBBCCGF/JGgkhBBCCCGEEEIIIfKQoJEQQgghhBBCCCGEyEOCRkIIIYQQQgghhBAiDwkaCSGEEEIIIYQQQog8JGgkhBBCCCGEEEIIIfKQoJEQQgghhBBCCCGEyEOCRkIIIYQQQgghhBAiDwkaCSGEEEIIIYQQQog8JGgkhBBlVGxsLCqVilmzZtm6KyKXwYMHU716dVt3QwghhBBCiGKRoJEQQljJmTNnGDlyJDVq1ECr1eLh4UHLli2ZM2cOqamptu5egfbt20d4eDgeHh4EBATQuXNn9uzZU+jtTcGrgn7ef//9Eux96bnXMeb82blzp627KoQQQgghhFU42roDQgjxMNi8eTP9+vVDo9Hw/PPPExoail6vZ/fu3UyYMIFjx46xaNEiW3czjwsXLhAREYGvry9RUVFkZWURExPDjz/+SMuWLR9oX08//TRdunTJs75Ro0bW6q5NLVu2zGL5q6++IiYmJs/6unXr8tlnn5GVlVWa3RNCCCGEEMLqJGgkhBDFdO7cOQYMGEC1atXYsWMHFSpUMD82ZswYTp8+zebNm23Yw4Jt3ryZpKQkfvzxR5o2bQrA//3f/5Genv7A+2rcuDHPPvustbtoN3If2/79+4mJiXmoj1kIIYQQQvy7yfA0IYQophkzZpCcnMwXX3xhETAyqVmzJi+//LJ5efHixbRr146AgAA0Gg0hISEsWLAgz3YHDhwgIiICPz8/XFxcCAwMZOjQofn2YdGiRQQFBaHRaGjatCm///57ofru4GD8b0BRFIv1Go2mUNsXxQ8//EB4eDju7u54eHjQtGlTVqxYYdHmu+++o0mTJri4uODn58ezzz7L5cuX77nfAwcOoFKpWLp0aZ7HoqOjUalUbNq0CYCkpCReeeUVqlevjkajISAggA4dOnDw4EGrHGPumkY560/Nnz+fGjVq4OrqSseOHbl48SKKovDOO+9QuXJlXFxc6NGjB7dv386z3x9++IGwsDDc3Nxwd3ena9euHDt2zCp9FkIIIYQQIjeVkvuTghBCiAdSuXJlNBoNZ86cKVT7Zs2aUa9ePRo0aICjoyMbN25k27ZtzJs3jzFjxgBw/fp16tSpg7+/PyNGjMDLy4vY2FjWrFnD8ePHAWMgIjAwkEaNGpGUlMSIESNQqVTMmDEDrVbL2bNncXJyumdfbty4Qa1atWjQoAExMTE4Ozs/8PGb+hEVFcXo0aPzPO7l5YWjozGxdcmSJQwdOpR69erx9NNP4+XlxaFDh0hPT+err74ytxkyZAhNmzZl4MCBXLt2jTlz5lCuXDkOHTqEl5dXgX0JCgqiTp06eTK7hg4dyrp167h27RpOTk4888wzrF69mrFjxxISEsKtW7fYvXs3/fv355lnninUcY8dO5b58+fnCbiBMWi0c+dOYmNjLV6jhg0botfrGT58OLdv32bGjBk0btyYdu3asXPnTgYMGMDp06eZO3cugwcP5ssvvzTvc9myZQwaNIiIiAi6du1KSkoKCxYsID4+nkOHDknhbSGEEEIIYX2KEEKIIktISFAApUePHoXeJiUlJc+6iIgIpUaNGubltWvXKoDy+++/F7ifc+fOKYDi6+ur3L5927x+/fr1CqBs3Ljxvn3Zu3ev4u3trTg7Oyv9+vVTMjMzC30cuftR0M++ffsURVGU+Ph4xd3dXXnssceU1NRUi31kZWUpiqIoer1eCQgIUEJDQy3abNq0SQGUyZMn37MvkyZNUpycnCxej/T0dMXLy0sZOnSoeZ2np6cyZsyYBz7WnMaMGaMU9N/ooEGDlGrVqpmXTa+Rv7+/Eh8fb9FfQGnQoIGSkZFhXv/0008rzs7OSlpamqIoipKUlKR4eXkpI0aMsHief/75R/H09MyzXgghhBBCCGuQ4WlCCFEMiYmJALi7uxd6GxcXF/PvCQkJ3Lx5k/DwcM6ePUtCQgKAOZtm06ZNZGRk3HN//fv3x9vb27wcFhYGwNmzZ++53fnz5+nSpQvDhg1j3bp1rF27lhEjRlhkzowcOZIqVaoU6rheeOEFYmJi8vyEhIQAEBMTQ1JSEm+88QZardZiW5VKBRiHmF2/fp3Ro0dbtOnatWu+GUS59e/fn4yMDNasWWNet23bNuLj4+nfv795nZeXF7/++itXrlwp1LFZS79+/fD09DQvP/bYY4CxXpIpG8u0Xq/Xm4fkxcTEEB8fz9NPP83NmzfNP2q1mscee4yffvqpVI9DCCGEEEL8O0ghbCGEKAYPDw/AWCOnsPbs2cOUKVPYt28fKSkpFo8lJCTg6elJeHg4ffr0ISoqig8//JA2bdrQs2dPBg4cmKfeUNWqVS2WTQGkuLi4e/Zj2rRpODg4MHXqVDQaDV9++SWDBg3C3d2dOXPmAHD06FFzYON+atWqRfv27Qt83DR8LzQ0tMA258+fB6B27dp5HqtTpw67d+++Zx8aNGhAnTp1WLVqFcOGDQNg1apV+Pn50a5dO3O7GTNmMGjQIKpUqUKTJk3o0qULzz//PDVq1Ljn/osr97kyBZByB+ZM603n8NSpUwAWx5CT6ToUQgghhBDCmiRoJIQQxeDh4UHFihU5evRoodqfOXOGJ554gjp16jB79myqVKmCs7MzW7Zs4cMPPzRP065SqVi9ejX79+9n48aNREdHM3ToUD744AP279+PTqcz71OtVuf7XMp9Stbt3buXhg0bmoNQzz33HNeuXWPChAm4u7szYMAA9u3bx/fff1+oY7MX/fv359133+XmzZu4u7uzYcMGnn76aYtMnqeeeoqwsDDWrl3Ltm3bmDlzJtOnT2fNmjV07ty5xPpW0Lm63zk0XRfLli2jfPnyedrlPDYhhBBCCCGsRd5lCiFEMXXr1o1Fixaxb98+mjdvfs+2GzduJD09nQ0bNlhknRQ0vOjxxx/n8ccf591332XFihU888wzrFy5kuHDhxe73yqViosXL1qse+2117h27Rrvvvsuy5cvp1GjRvTo0aPYzwXGItVgzF6qWbNmvm2qVasGwMmTJ/Nk1Zw8edL8+L3079+fqKgovv/+e8qVK0diYiIDBgzI065ChQqMHj2a0aNHc/36dRo3bsy7775bokGjojK9dgEBAffM5hJCCCGEEMKapKaREEIU08SJE3Fzc2P48OFcu3Ytz+NnzpwxD/cyZZTkzAJKSEhg8eLFFtvExcXlyRRq2LAhAOnp6Vbpd/v27Tl16hTLli2zWP/+++8TEhJCbGws3bt3x8HBOv9VdOzYEXd3d6ZNm0ZaWprFY6ZjffTRRwkICGDhwoUWx/nDDz9w4sQJunbtet/nqVu3Lo888girVq1i1apVVKhQgdatW5sfNxgM5tpRJgEBAVSsWNFqr621RURE4OHhwXvvvZdvjasbN27YoFdCCCGEEOJhJ5lGQghRTEFBQaxYsYL+/ftTt25dnn/+eUJDQ9Hr9ezdu5fvvvuOwYMHA8bAibOzM08++SQjR44kOTmZzz77jICAAK5evWre59KlS/nkk0/o1asXQUFBJCUl8dlnn+Hh4UGXLl2s0u9Jkyaxbt06Bg0aRExMDC1atCA5OZlvvvmGc+fO0bRpU6ZOnUrz5s3p2LHjffd38OBBvv7663xfn+bNm+Ph4cGHH37I8OHDadq0KQMHDsTb25s//viDlJQUli5dipOTE9OnT2fIkCGEh4fz9NNPc+3aNebMmUP16tV59dVXC3Vs/fv3Z/LkyWi1WoYNG2YR+EpKSqJy5cr07duXBg0aoNPp2L59O7///jsffPBB4V/AUuTh4cGCBQt47rnnaNy4MQMGDMDf358LFy6wefNmWrZsybx582zdTSGEEEII8ZCRoJEQQlhB9+7d+fPPP5k5cybr169nwYIFaDQa6tevzwcffMCIESMAY4Hn1atX89///pfXXnuN8uXLM2rUKPz9/Rk6dKh5f+Hh4fz222+sXLmSa9eu4enpSbNmzVi+fDmBgYFW6bOfnx+//fYbkZGRrF+/nm+++QY/Pz86duzIihUrqFixIs2aNaNfv37s2bPnngWsAb755hu++eabPOsHDRpkHrY3bNgwAgICeP/993nnnXdwcnKiTp06FsGgwYMH4+rqyvvvv8/rr7+Om5sbvXr1Yvr06eZZ5e6nf//+/Pe//yUlJcVi1jQAV1dXRo8ezbZt21izZg1ZWVnUrFmTTz75hFGjRhVq/7YwcOBAKlasyPvvv8/MmTNJT0+nUqVKhIWFMWTIEFt3TwghhBBCPIRUyv0qpQohhBBCCCGEEEKIfx2paSSEEEIIIYQQQggh8pCgkRBCCCGEEEIIIYTIQ4JGQgghhBBCCCGEECIPCRoJIYQQQgghhBBCiDwkaCSEEEIIIYQQQggh8pCgkRBCCCGEEEIIIYTIw9HWHSgLMjMzOXToEOXKlcPBQeJsQgghhBDC/mVlZXHt2jUaNWqEo6O87RdCCPHg5H+PQjh06BDNmjWzdTeEEEIIIYR4YL/99htNmza1dTeEEEKUQRI0KoRy5coBxv9wK1SoYOPeCCGEEEIIcX9Xr16lWbNm5veyQgghxIOSoFEhmIakVahQgcqVK9u4N0IIIYQQQhSelFcQQghRVPI/iBBCCCGEEEIIIYTIQ4JGQgghhBBCCCGEECIPCRoJIYQQQgghhBBCiDwkaCSEEEIIIYQQQggh8pCgkRBCCCGEEEIIIYTIQ2ZPE0IIIYQQQohcFEXBYDCQmZlp664IIYRVOTk5oVarC9VWgkZCCCGEEEIIcZeiKMTHx3Pjxg0MBoOtuyOEECXCy8uL8uXLo1Kp7tlOgkZCCCGEEEIIcdc///xDfHw8Hh4eeHh44OjoeN8PVUIIUVYoikJKSgrXr18HoEKFCvdsL0EjIYQQQgghhAAMBgMJCQn4+/vj5+dn6+4IIUSJcHFxAeD69esEBATcc6iaFMIWQgghhBBCQOweWNEfZtWGSE84sangthtfMbbZ94nl+pTb8P1weK8yTKsK68dAerJlm3+Owped4J0AmB0Cuz+y9pEUWUZGBoqi4ObmZuuuCCFEiXJ1dQWM9717kaCREEIIIYQQAjJSoFwodJ1173YnNsKlA+Cez5CGNSPg+l/w/DoYuArO74WNL2c/npYIy3qBZxUY+TN0eBt2vg8HFlv1UIpLhqMJIR52hb3PSdBICCGEEEIIAbU6wBNvQd0nC26TeAW2TIQ+n4GDk+VjN07C6e3Q/WOo/ChUaw6dZ8LR7yHxqrHNkW/BoIce8yGgLjzSFx4bCfvml9xxCSGEKDIJGgkhhBBCCPEQS0pKIjEx0fyTnp5etB1lZcGaF6DlOGPAJ7eLv4HWEyo1zl5Xow2oHODygbttfodqLcHRObtNzSfg1ilIjStav4Tdi42NRaVSMWvWfbLYhBB2R4JGQgghhBBCPMRCQkLw9PQ0/0ybNq1oO9rzITg4wmMv5v948jVw87dcp3YEF2/jY6Y2ulxt3ALuPna9aP0SRXLmzBlGjhxJjRo10Gq1eHh40LJlS+bMmUNqaqqtuyeEsBMye5oQQgghhBAPsePHj1OpUiXzskajefCdXDkE+xfCyF9A6v2UeZs3b6Zfv35oNBqef/55QkND0ev17N69mwkTJnDs2DEWLVpk624KIeyABI2EEEIIIYR4iLm7u+Ph4VG8nZzfB3duwIf1stcpBtj2JuxfAK8eAV05Y5ucDJnGYWe6csZlXTlIztXmzt0MI11A8fooCuXcuXMMGDCAatWqsWPHDipUyC5oPmbMGE6fPs3mzZtt2EMhhD2R4WlCCCGEEEKIe2swAEbthRd3Z/+4V4AW4+C5NcY2VZpBWoIxK8nk3M+gZEGlR++2aQrn94AhxxTPZ34C31rGYWyixM2YMYPk5GS++OILi4CRSc2aNXn5ZeOMd4sXL6Zdu3YEBASg0WgICQlhwYIFebY5cOAAERER+Pn54eLiQmBgIEOHDs33+RctWkRQUBAajYamTZvy+++/W/cAhRBWJZlGQgghhBBCCEhPhttns5fjz8PVP43BHK8q4Opj2d7ByZg55FfLuOxfG2q2hw3joNtHkJUBWyZAaB/wuBuceKQf7JwO68dCq1fg+nH4dSFEvFcaRyiAjRs3UqNGDVq0aHHftgsWLKBevXp0794dR0dHNm7cyOjRo8nKymLMmDEAXL9+nY4dO+Lv788bb7yBl5cXsbGxrFmzJs/+VqxYQVJSEiNHjkSlUjFjxgx69+7N2bNncXJyytNeCGF7EjQSQgghhBBCGDOElnbLXo7+j/HfBgOhV97sknz1/swYKPqqu3HWtLrdofP07Me1nvDcWtjyGnwaDq6+ED4RHh1iveMoAYqikJphsHU3zFyc1KiKUFsqMTGRy5cv06NHj0K1//nnn3FxcTEvjx07lk6dOjF79mxz0Gjv3r3ExcWxbds2Hn30UXPbqVOn5tnfhQsXOHXqFN7exqyy2rVr06NHD6Kjo+nWrVue9kII25OgkRBCCCGEKBMOxN7m1PVknm5W1dZduTd9CmwYC3HnjcuuPvDMd7btU2EEhkFkQuHbv3ok7zpXH+j7xb23Kx8KQ7c+WN9sLDXDQMjkaFt3w+z42xG4Oj/4R7nExETAWOeqMHIGjBISEsjIyCA8PJzo6GgSEhLw9PTEy8sLgE2bNtGgQYN7Zgz179/fHDACCAsLA+Ds2bMFbSKEsDEJGtm51FRo3x4MhlRUqvY4OMD27dstbuDi4ZKamkr79u0BOdfCPsg1KYSwF30X7gOgkpcLrYP979Pahg4vh6Pfmxdvpjvilpoq909hc6aC6ElJSYVqv2fPHqZMmcK+fftISUmxeMwUNAoPD6dPnz5ERUXx4Ycf0qZNG3r27MnAgQPzzNRXtaplwNcUQIqLiyvqIQkhSpgEjeyciwvs2QPgAuyxcW9EaXBxcWHPHjnXwn7INSmEsDfHriTaX9BIUYyBosQr8PvnxnXNx0L1VvipnY1v6kSZ5eKk5vjbEbbuhpmLk7pI23l4eFCxYkWOHj1637ZnzpzhiSeeoE6dOsyePZsqVarg7OzMli1b+PDDD8nKygJApVKxevVq9u/fz8aNG4mOjmbo0KF88MEH7N+/H51OZ96nWp1/vxVFKdLxCCFKngSNhBBCCCFEmZKcnnH/RqXtr03w/bDsZY0ntJkEGl3B24gyQ6VSFWk4mD3q1q0bixYtYt++fTRv3rzAdhs3biQ9PZ0NGzZYZAj99NNP+bZ//PHHefzxx3n33XdZsWIFzzzzDCtXrmT48OFWPwYhROmx6Z1v/k+niT72D2euJ6N1UtO4mjdvdK5DkH/2f65pGQbe3XyCjX9eQZ+ZReta/rzTMxR/9+xUx8vxqfx37RH2nb2Fm7MjfZpUZmJEbRzVDuY2+87cYurm45y6lkwFLy1j29ak36NVSvV4hRBCCCFE8SWlZdq6C9lS443TzO+dZ1yu8hj4BEG9XhIwEnZp4sSJLF++nOHDh7Njxw7KlStn8fiZM2fYtGmTOSsoZxZQQkICixcvtmgfFxeHl5eXRWHuhg0bApCenl5CRyGEKC02DRr9eu42zz1ejQZVvMg0KMyM/ovnv/iNmPGtzZH8dzYd56e/rvPJwMa4a52YvOEoL379P74fZZwi0pClMHTx7/i7a/h+VAuuJ6Xzf9/+gaODiomd6gBw8XYKQ5f8zjOPVWXOgIbsOX2LN9YcIcBDS7i9pTbnkpoKrVtDVlYq0BoHB/jll19kTPxDLDU1ldatWwNyroV9kGtSCGFv7CZodOUQfN7BOLU8gIMj9Ftqnl5e7p/CHgUFBbFixQr69+9P3bp1ef755wkNDUWv17N3716+++47Bg8ezPjx43F2dubJJ59k5MiRJCcn89lnnxEQEMDVq1fN+1u6dCmffPIJvXr1IigoiKSkJD777DM8PDzo0qWLDY9UCGENNg0afTW0mcXyrH4NaDJ1O0cuJfBYDV8S0zL49sBF5gxoRIuafgDM7NuA9rN/5uCFOBpX9eaXUzc4dT2Jr4c/hr+7hnrA+A7BTP/hL15pH4yzowNf/3qeKj4u/LdbCAA1A9z5PfY2X+w+Z/dBo6wsOHAAIAs4cHddli27JEpYVlYWBw7IuRb2Q65JIYS9sZug0Z45xoCRgxM4auCxkeaAEcj9U9iv7t278+effzJz5kzWr1/PggUL0Gg01K9fnw8++IARI0ag0WhYvXo1//3vf3nttdcoX748o0aNwt/fn6FDh5r3FR4ezm+//cbKlSu5du0anp6eNGvWjOXLlxMYGGjDoxRCWINdDcw1vQHwcnUG4OilBDIMCi3vBowAagboqOTlwsHzxqDRofNx1C7vYTFcLTzYn/+uO8rf15IIreTJofPxFvsAaB3szzsbj5fCURWPRgObNoHBoAE2oVaTZxYC8XDRaDRs2rTJ/LsQtibXpBDC3iSl2bCmUdI/sG4UpNyGf+5OOf/CTuM08rnI/VPYs1q1arFo0aJ7tnnyySd58skn86wfMmSI+fdGjRqxYsWKe+6nevXqBRa7liLYQtg3uwkaZWUpvL3pOI9W86Z2eXcAbiSn46x2wNPFyaKtn86ZG8np5jZ+Oudcj2vMj2W3sfyP2l+nISk9k7QMA9pcsw+kp6dbjL8t7JSUJcHREbp2BeOp6mqzfojS4+joSNeucq6F/ZBrUghhb5LTbZhptPtDOLMjeznoiXwDRiD3TyGEEGWf3QSN3lp/lJP/JLF6VMEV/EvLtGnTiIqKsnU3hBBCCCFEPko1aJSZDge/gvREUBQ49LVxfcQ08K8NVZrde3shhBCiDLOLoNHk9UfZ8dd1vh3ZnAqe2QUC/XUa9IYsElIzLLKNbibr8b+bOeSv03D4YoLF/m7ezTDK2ca0zuRGcjruGsc8WUYAkyZNYvz48ebly5cvExISUsyjLBqDAXbsAIPBAOxArYZ27dqZZzMQDx+DwcCOHcZvMOVcC3sg16QQwt6Uak2jPR/DT1Mt1/nVhsdHQY7ZovIj908hhBBlnU2DRoqiMGXDMaKP/cPKF5pTxcfV4vHQyp44qVXsPX2Tzo8YiwqeuZHM5fhUGlfzBqBRNW/m/XSamzmGoO06dRN3jSO1yunutvFi5183LPa9+9RNGt3dR24ajcZi3HliYqJ1DrgI0tKgY0eANKAjAMnJybi5udmsT6JkpaWl0bGjnGthP+SaFELYm1KpaZT0D6Qnwe+fGZeDO4Gbn3GGtCaD7xswArl/CiGEKPtsGjR6a/1R1h++wmfPP4qbRs31pDQAPLROaJ3UeGideOrRKkzdfAJPVyfcNU5M2XCUxlW9aFzVGPBpXcufWgHuvLrqMJM61+VGcjofbDvJc82roXE0fpvz7GPV+GrveaZtOUG/R6uw78xNNh+5ypeDm9rs2AvLwQEaNICsLAegAQ4O4ODgYOtuiRLk4OBAgwYNzL8LYWtyTQoh7E2GoYQL5x5ZDd8Py152rwBPLQNH54K3yYfcP4UQQpR1Ng0afb3/AgADFu23WD+zb336PVoFgLe6heCgOsGorw+iz8yidbAf7/TMLjaodlDxxeBH+e+6o/ResAdXZ0f6NK7E+A7B5jZVfFz5cnBT3tl0nMV7YinvqeX93o8QHuxfCkdZPC4ucPgwgAtw2KZ9EaXDxcWFw8aTLoRdkGtSCPGvoijwyyzj706u4OQCT0x+4IARyP1TCCFE2WfToFHs+/efTULrpOadnqEWgaLcKnu7smTIvYsQNg/yZcvLYQ/cRyGEEEIIYX8URUFViCFihXblEGyZAGmJcPMkOLnB+OPg4mW95xBCCCHKGLsohC2EEEIIIcT9OKsd0BuyAEjRG3DTWPGtbMwUuPR79vKjQyRgJIQQ4l9PgkZ2LjUVOncGgyEVlaozDg7www8/4OLicv+NRZmUmppK586dATnXwj7INSmEsBdap+ygUWJaRvGDRim34dAySI2Hcz+DygGe+gpcfaFy8Wtfyv1TCCFEWSdBIzuXlQU//wyQBfx8d12WLbskSlhWVhY//yznWtgPuSaFEPYiZ/nrSWuOUKe8BxMiaqN2KOIwta2T4M+V2ct1ukHdJ4vVx5zk/imEEKKsk6CRndNo4NtvwWDQAN+iVoNGo7F1t0QJ0mg0fPvtt+bfhbA1uSaFEPZo58kb7Dx5g7a1/Xmshm/hN1QUiDsHd27B0e+N6xoMNA5Faz7Gqn2U+6cQQoiyToJGds7REfr1A+Op6mfj3ojS4OjoSL9+cq6F/ZBrUghhN+6mGg1pWZ0fT1znwu0U4lL0D7aPn6fDzmnZy1Ueh14LrNfHHOT+KYQQoqxzsHUHhBBCCCGEKAzT8LRBzatTw98NgMS0zMLvID0Z9n9i/F3rBR6Vod1/rdpHIR4mKpWKsWPH2robQlhFZGSkdWfdBHbu3IlKpWLnzp1W3a+Jqc83b94skf0XhgSN7JzBAHv2wC+/GPjllz3s2bMHg8Fg626JEmQwGNizR861sB9yTQoh7IWiGMNGKhW4a50ASEzNuPdGxzfAgpYw/zFY0BzSEsCnBkw8B+OPQWBYifVX7p/CXp05c4aRI0dSo0YNtFotHh4etGzZkjlz5pCammrr7v0rpaSkEBkZWWLBh8TERN59910effRRPD090Wg0VKtWjf79+7N58+YSec6yavDgwahUKvOPo6MjVapUYcCAARw/ftzW3St1MjzNzqWlQatWAGlAKwCSk5Nxc3OzZbdECUpLS6NVKznXwn7INSmEsBemTCMVKty1xrexSffKNDJkGotdJ16yXN9iHDiU/Hencv8U9mjz5s3069cPjUbD888/T2hoKHq9nt27dzNhwgSOHTvGokWLbN3Nf52UlBSioqIAaNOmjVX3ffr0aSIiIjh//jy9evXi+eefR6fTcfHiRbZs2UK3bt346quveO6556z6vGWZRqPh888/ByAzM5MzZ86wcOFCtm7dyvHjx6lYsaKNe1h6JGhk51QqqFkTsrJUqFQ1UamwekqdsC8qlYqaNWuafxfC1uSaFELYi7uJRqhU4GHKNErLJ9Po1hk4shriLxgDRq5+0G8xoAKNDio0LJX+yv1T2Jtz584xYMAAqlWrxo4dO6hQoYL5sTFjxnD69GnJOnnIZGZm0qtXL65du8bPP/9My5YtLR6fMmUK27Ztu2825J07d/5VgW9HR0eeffZZi3WPP/443bp1Y/PmzYwYMcJGPSt9MjzNzrm6wqlTcOaMK6dPn+LUqVO4urrauluiBLm6unLqlJxrYT/kmhRC2AvFnGtEwZlGigKrh8LO9+Dw18Z1jw6FwNbGoWgVGxmjTqVA7p/C3syYMYPk5GS++OILi4CRSc2aNXn55ZfzrF+3bh2hoaFoNBrq1avH1q1bLR4/f/48o0ePpnbt2ri4uODr60u/fv2IjY21aLdkyRJUKhV79uxh/Pjx+Pv74+bmRq9evbhx44ZF26ysLCIjI6lYsSKurq60bduW48ePU716dQYPHmzRNj4+nldeeYUqVaqg0WioWbMm06dPJysrq1Cvyw8//EBYWBhubm64u7vTtWtXjh07lqfdX3/9xVNPPYW/vz8uLi7Url2bN99806LNoUOH6Ny5Mx4eHuh0Op544gn2799/z+ePjY3F398fgKioKPOwqMjISHObHTt2mPvo5eVFjx49OHHixH2P7bvvvuPo0aO89dZbeQJGJh07dqRz587mZdN5+vnnnxk9ejQBAQFUrlzZ/Pgnn3xCvXr10Gg0VKxYkTFjxhAfH2+xz/zOExizqHJmUplqAq1atYr//Oc/lC9fHjc3N7p3787FixfzbP/rr7/SqVMnPD09cXV1JTw8nD179uRpt3v3bpo2bYpWqyUoKIhPP/30Pq/U/ZUvXx4wBpTuZdeuXfTr14+qVaui0WioUqUKr776ar5DPwtzTeV2/vx5atasSWhoKNeuXQPg1KlT9OnTh/Lly6PVaqlcuTIDBgwgISGhiEebTTKNhBBCCCFEmaJSgYeLMdMoyZRpZMiAm6fg+nG4ehgctdDoWWPB61av2KqrQtiVjRs3UqNGDVq0aFHobXbv3s2aNWsYPXo07u7ufPzxx/Tp04cLFy7g6+sLwO+//87evXsZMGAAlStXJjY2lgULFtCmTRuOHz+eJ2j60ksv4e3tzZQpU4iNjeWjjz5i7NixrFq1ytxm0qRJzJgxgyeffJKIiAj++OMPIiIiSEtLs9hXSkoK4eHhXL58mZEjR1K1alX27t3LpEmTuHr1Kh999NE9j2/ZsmUMGjSIiIgIpk+fTkpKCgsWLKBVq1YcOnSI6tWrA/Dnn38SFhaGk5MTL7zwAtWrV+fMmTNs3LiRd999F4Bjx44RFhaGh4cHEydOxMnJiU8//ZQ2bdrw888/89hjj+XbB39/fxYsWMCoUaPo1asXvXv3BqB+/foAbN++nc6dO1OjRg0iIyNJTU1l7ty5tGzZkoMHD5r7mJ+NGzcC5MmaKYzRo0fj7+/P5MmTuXPnDmAszBwVFUX79u0ZNWoUJ0+eZMGCBfz+++/s2bMHJyenB34egHfffReVSsXrr7/O9evX+eijj2jfvj2HDx/GxcUFMAbOOnfuTJMmTZgyZQoODg4sXryYdu3asWvXLpo1awbAkSNH6NixI/7+/kRGRpKZmcmUKVMoV67cA/XJVHzaYDBw9uxZXn/9dXx9fenWrds9t/vuu+9ISUlh1KhR+Pr68ttvvzF37lwuXbrEd999Z25XmGsqtzNnztCuXTt8fHyIiYnBz88PvV5PREQE6enpvPTSS5QvX57Lly+zadMm4uPj8fT0fKDjzkMR93Xx4kUFUC5evGjrrgghhBBC/GsFv7lFqfb6JuXi7TvKukOXlGqvb1IGfLrP+OCq5xRlikf2z4Zxtu2sHZD3sA8uNTVVOX78uJKamprv48nJxp+srOx16enGdWlp+bc1GLLX6fXGdbl3/yBtiyohIUEBlB49ehR6G0BxdnZWTp8+bV73xx9/KIAyd+5c87qUlJQ82+7bt08BlK+++sq8bvHixQqgtG/fXsnK8SK++uqrilqtVuLj4xVFUZR//vlHcXR0VHr27Gmxz8jISAVQBg0aZF73zjvvKG5ubsrff/9t0faNN95Q1Gq1cuHChQKPLykpSfHy8lJGjBhhsf6ff/5RPD09Lda3bt1acXd3V86fP2/RNudx9OzZU3F2dlbOnDljXnflyhXF3d1dad26dYH9UBRFuXHjhgIoU6ZMyfNYw4YNlYCAAOXWrVvmdX/88Yfi4OCgPP/88/fcb6NGjRQvL68865OTk5UbN26YfxISEsyPmc5Tq1atlMzMTPP669evK87OzkrHjh0VQ46Ldd68eQqgfPnll+Z11apVszhPJuHh4Up4eLh5+aefflIApVKlSkpiYqJ5/bfffqsAypw5cxRFMb7OtWrVUiIiIixe85SUFCUwMFDp0KGDeV3Pnj0VrVZrca6OHz+uqNVqpTAhkEGDBikYy+hZ/FSqVEn53//+Z9HW1P+ffvrJok+5TZs2TVGpVBZ9Ksw1NWXKFAVQbty4oZw4cUKpWLGi0rRpU+X27dvmNocOHVIA5bvvvrvvseV0v/udiQxPs3NpadC1K3TqlEbnzl3p2rVrnui6eLikpaXRtauca2E/5JoUQtgLcyFsVY5C2Ol3M4yOrzc+6BYA/nWh1au26WQOcv98+Oh0xp+cs1/PnGlcl3tm+oAA4/oLF7LXzZ9vXDdsmGXb6tWN63OONlqyxLhuwADr9D0xMREAd3f3B9quffv2BAUFmZfr16+Ph4cHZ8+eNa8zZYIAZGRkcOvWLWrWrImXlxcHDx7Ms88XXnjBos5XWFgYBoOB8+fPA/Djjz+SmZnJ6NGjLbZ76aWX8uzru+++IywsDG9vb27evGn+ad++PQaDgV9++aXAY4uJiSE+Pp6nn37aYlu1Ws1jjz3GTz/9BMCNGzf45ZdfGDp0KFWrVrXYh+k4DAYD27Zto2fPntSoUcP8eIUKFRg4cCC7d+82n4MHcfXqVQ4fPszgwYPx8fExr69fvz4dOnRgy5Yt99w+MTERnU6XZ/2bb76Jv7+/+WfgwIF52owYMQK1Wm1e3r59O3q9nldeeQWHHJMJjBgxAg8Pj2LVw3r++ectrs2+fftSoUIF8/EdPnyYU6dOMXDgQG7dumU+V3fu3OGJJ57gl19+ISsrC4PBQHR0ND179rQ4V3Xr1iUiIqLQ/dFqtcTExBATE0N0dDSffvopOp2OLl268Pfff99z25x/D3fu3OHmzZu0aNECRVE4dOgQULhrKqejR48SHh5O9erV2b59O97e3ubHTJlE0dHRpKSkFPoYC0uGp9k5gwGMfycGYMvddTJl68PMYDCYb45yroU9kGtSCGE3TIWwMRbCHqbewqDbO2Dx3SFqtbvA09/YrHu5yf1T2BMPDw8AkpKSHmi73B9oAby9vYmLizMvp6amMm3aNBYvXszly5dRlOz6Y/nVVMm9T9MHYNM+TcEjUyF5Ex8fH4sPy2Cs5fLnn3+aawLldv369QKP7dSpUwC0a9cu38dNr5kpQBYaGlrgvm7cuEFKSgq1a9fO81jdunXJysri4sWL1KtXr8B95Mf0WhS03+jo6HsWqXZ3d+fWrVt51o8ePdo8zKqgoWuBgYGF6ouzszM1atQwP14UtWrVslg2TSRgqotlOleDBg0qcB8JCQmkp6eTmpqaZ3+mft8vyGaiVqtp3769xbouXbpQq1YtJk2axPfff1/gthcuXGDy5Mls2LDB4u/E1Eco3DWV05NPPkm5cuWIjo7OEwQMDAxk/PjxzJ49m+XLlxMWFkb37t159tlniz80DQka2T1nZ1i8GDIznVGpFqNWG/8oxcPL2dmZxYsXm38XwtbkmhRC2AtTIWyVCryURCY6rkKjZMAdABW0GGfT/uUm98+HT3Ky8d+cJXomTIBXXoHctXFNsYocSQeMGQMjRkCO5A0ATPWic7YdPBgGDszbtqg8PDyoWLEiR48efaDt1AV0IGdg6KWXXmLx4sW88sorNG/eHE9PT1QqFQMGDMi3GHVh9llYWVlZdOjQgYkTJ+b7eHBw8D23BWNdI1OR45zuV/C4LKhTpw6HDx/m8uXLVKpUybw+ODjY/Npotdp8t82ZMfOgCpox0mAwFHj+78V0rmbOnEnDhg3zbaPT6UhPT3/gfRdW5cqVqV279j2z1wwGAx06dOD27du8/vrr1KlTBzc3Ny5fvszgwYMLXZw9tz59+rB06VKWL1/OyJEj8zz+wQcfMHjwYNavX8+2bdsYN24c06ZNY//+/RZFzIui7P8VPOScnIz/YYATMNimfRGlw8nJKd+ZBoSwFbkmhRD2QlGgkeoU7nt+xfvGMTSqDI5mBVJv2Ceo3ALAr+b9d1KK5P758MkvmcPZ2fhTmLZOTsaf4rQtjm7durFo0SL27dtH8+bNrbbf1atXM2jQID744APzurS0tDwzahVWtWrVADh9+rRFtsutW7fyZG4EBQWRnJycJyukMEzD7gICAu65vWm42b0Cbv7+/ri6unLy5Mk8j/311184ODhQpUqVArcvKMhiei0K2q+fn1+BWUZgPOcrV65k+fLlBQbWCitnX3IOwdPr9Zw7d87iNfT29s73/J8/f95iWxNTJpGJoiicPn3aXAzcdK48PDzuea5Ms5Dl3p+p38WVmZlJsil6nI8jR47w999/s3TpUp5//nnz+piYGIt2hbmmcpo5cyaOjo7mgvT5DSd85JFHeOSRR/jvf//L3r17admyJQsXLmTq1KmFeo6CSE0jIYQQQghRJjij53PnWeh+m4P23HYAPsvsTGqFZnYXMBLCHk2cOBE3NzeGDx9unqo7pzNnzjBnzpwH3q9arc6TJTR37twiD8t84okncHR0ZMGCBRbr582bl6ftU089xb59+4iOjs7zWHx8PJmZmQU+T0REBB4eHrz33ntkZGTkefzGjRuAMRDRunVrvvzySy7kLFJFdnaUWq2mY8eOrF+/3jykCuDatWusWLGCVq1amYe75cc0w1zuQEuFChVo2LAhS5cutXjs6NGjbNu2jS5duhS4TzC+PiEhIbzzzjvs378/3zaFzfBq3749zs7OfPzxxxbbfPHFFyQkJNC1a1fzuqCgIPbv349erzev27RpExcvXsx331999ZXF0MnVq1dz9epVOnfuDECTJk0ICgpi1qxZ+QZtTOdKrVYTERHBunXrLM7ViRMn8r1GHsTff//NyZMnadCgQYFtTFlUOV8fRVHy/F0V5prKSaVSsWjRIvr27cugQYPYsGGD+bHExMQ81/kjjzyCg4ODVTKvJNPIzhkMcOSIaRz8EdRq4wVQlJQ+UTYYDAaOHDkCyLkW9kGuSSGETaUnw63TAAx12IKvKgmDWzkcQnvz3p5ENmS14D9pmbg629/bWrl/CnsTFBTEihUr6N+/P3Xr1uX5558nNDQUvV7P3r17+e6774qUHdetWzeWLVuGp6cnISEh7Nu3j+3bt+Pr61ukfpYrV46XX36ZDz74gO7du9OpUyf++OMPfvjhB/z8/CyyciZMmMCGDRvo1q0bgwcPpkmTJty5c4cjR46wevVqYmNj8fPzy/d5PDw8WLBgAc899xyNGzdmwIAB+Pv7c+HCBTZv3kzLli3NgaqPP/6YVq1a0bhxY1544QUCAwOJjY1l8+bNHD58GICpU6cSExNDq1atGD16NI6Ojnz66aekp6czY8aMex6zi4sLISEhrFq1iuDgYHx8fAgNDSU0NJSZM2fSuXNnmjdvzrBhw0hNTWXu3Ll4enoSGRl5z/06OTmxdu1aIiIiaNWqFb179yYsLMw8ZGrDhg1cuHDBIuBTEH9/fyZNmkRUVBSdOnWie/funDx5kk8++YSmTZta1EYaPnw4q1evplOnTjz11FOcOXOGr7/+2qKoek4+Pj60atWKIUOGcO3aNT766CNq1qzJiBEjAHBwcODzzz+nc+fO1KtXjyFDhlCpUiUuX77MTz/9hIeHBxs3bgQgKiqKrVu3EhYWxujRo8nMzGTu3LnUq1ePP//8877HCcaMoq+//howDo2LjY1l4cKFZGVlMWXKlAK3q1OnDkFBQbz22mtcvnwZDw8Pvv/++zwZclC4ayonBwcHvv76a3r27MlTTz3Fli1baNeuHTt27GDs2LH069eP4OBgMjMzWbZsGWq1mj59+hTqeO/pgeZk+5ey5XSlycmKYkzGTjZP9ZecnFzq/RClJzlZzrWwL3JNCiFsxpCpKPMeU5QpHhY/iT/OVhRFUepHRivVXt+knLqWeJ8d2Yat75+2fA9bVhV2Cuqy7u+//1ZGjBihVK9eXXF2dlbc3d2Vli1bKnPnzlXS0tLM7QBlzJgxebbPPZ16XFycMmTIEMXPz0/R6XRKRESE8tdff+VpZ5rK/ffff7fYX37TlmdmZipvvfWWUr58ecXFxUVp166dcuLECcXX11d58cUXLbZPSkpSJk2apNSsWVNxdnZW/Pz8lBYtWiizZs1S9Hr9fV+Pn376SYmIiFA8PT0VrVarBAUFKYMHD1YOHDhg0e7o0aNKr169FC8vL0Wr1Sq1a9dW3nrrLYs2Bw8eVCIiIhSdTqe4uroqbdu2Vfbu3XvfPiiKouzdu1dp0qSJ4uzsrADKlClTzI9t375dadmypeLi4qJ4eHgoTz75pHL8+PFC7VdRFCU+Pl55++23lUaNGik6nU5xdnZWqlSpovTt21fZuHGjRduCzpPJvHnzlDp16ihOTk5KuXLllFGjRilxcXF52n3wwQdKpUqVFI1Go7Rs2VI5cOCAEh4eroSHh5vbmM79N998o0yaNEkJCAhQXFxclK5du+aZil5RjNPL9+7dW/H19VU0Go1SrVo15amnnlJ+/PFHi3Y///yz+bWsUaOGsnDhQvP09fczaNAg873b9OPh4aE88cQTyvbt2y3a5nftHj9+XGnfvr2i0+kUPz8/ZcSIEcoff/yhAMrixYsttr/fNWXq840bN8zrUlJSlPDwcEWn0yn79+9Xzp49qwwdOlQJCgpStFqt4uPjo7Rt2zZPX3Mr7P1OpShFqDb2L3Pp0iWqVKnCxYsXi11E6kGlpECtWpCVlYJKVQuVyjje0zVn9T3xUElJSTFX+5dzLeyBXJNCCJv5azOsHAgOTqAL4EpCKheVAGqM24S/nz+tpu/gUlwqIRU8qBmg4/0+j9hVxpGt75+2fA9bVqWlpXHu3DkCAwMLLA4sbCc+Ph5vb2+mTp3Km2++aevuCCvYuXMnbdu25bvvvqNv37627s6/SmHvd/bzv6rIl6srXL4M4ApctnFvRGlwdXXl8mU518J+yDUphCh1m8bD6e2Qejedv8VYaB9Jizc2A/C7xlgXJNDPjUtxqRy/msjxq4k82aAiHULK2arXecj9U4iiS01NzTN710cffQRAmzZtSr9DQvxLSdBICCGEEELYj4u/w4EvspedddB0hEUTUzmTuU83Yv/Z28z76RRHLyeSkJq3kK0QomxatWoVS5YsoUuXLuh0Onbv3s0333xDx44dadmypa27J8S/hgSNhBBCCCGE7f21GS7sh/N7jMshPaDFOPCsDO7lLWaTMZXA9XJ1plNoeTb9eYWjlxNJlKCREA+N+vXr4+joyIwZM0hMTDQXxy7u9OFCiAcjQSM7l5YGzz0HBkMa8BxqNSxbtkzGWD/E0tLSeO655wA518I+yDUphChxcbGw6llQsrLXhb0GFeqbF3NW4cw5cxKAh4sTAElpBU+tbQty/xSi6Bo3bsz27dtt3Q1Rwtq0aZPvFPPCfkjQyM4ZDLB6NYABWA3AkiVLbNgjUdIMBgOrV8u5FvZDrkkhRIm5cxPizsP/vjQGjALqQc12UL6BRcAoN1WuZXet8S1tYpp9ZRrJ/VMIIURZJ0EjO+fsDPPmgcHgDMxDrQZnZ2dbd0uUIGdnZ+bNm2f+XQhbk2tSCFEiUm7DvKaQejt7XYe3oVb7fJvf63toD60p08i+gkZy/xRCCFHWSdDIzjk5wZgxAE7AGBv3RpQGJycnxoyRcy3sh1yTQogScfArY8DIyQ3cfKHK4xDUrsDmFjWNcqUaedzNNLK34Wly/xRCCFHWSdBICCGEEEKUjsx0WPUcXDtqHJoG0GUGNHr2vpvmzDRS5Rqg5n4308jehqcJIYQQZZ0EjexcVhacOQNZWVnAGRwcICgoCAcHB1t3TZSQrKwszpw5A8i5FvZBrkkhhNUcXQOnorOXPatAaN9CbapYRo0seLjYZ6aR3D+FEEKUdRI0snOpqRAcDJAKBAOQnJyMm5ubLbslSlBqairBwXKuhf2Qa1IIUWSKAr9/DrfPGpdP/mD8t/lYeKQv+NQAp8LNKKZQ8PA0c6ZRqn1lGsn9UwghRFknQaMywNPT+J5LpfK0dVdEKfH0lHMt7Itck0LYn+hj/7DytwtkKVDRS0tk93poHNW27pal09thy2uW6xxdoNV4Yx2jB5Az06ig2dPsLdMI5P4phBCibJOgkZ1zc4P4eAA3IN6mfRGlw83NjXjjSRfCLsg1KYR9+mj7KU5cTTQvR9QrT5vaATbsUQ5xsZD0D+yZY1wObA2Vmhh/r9HmgQNGualypRplz56WiaIoeR63Fbl/CmFdkZGRREVFWRTGF0KULBlYLYQQQghRBukzDQA4OhgDJPEpdjI065+jMLcJfBkBsbtA5QDd50L7SONPjTZWf0pTppHekEV6ZpbV9y/Ew2LJkiWoVCrzj6OjI5UqVWLw4MFcvnzZ1t2zmpMnT/Lqq6/SokULtFotKpWK2NjY+2535swZc/sDBw5YpS9btmwhMjLSKvvKLSUlhcjISHbu3Fki+xcCJGgkhBBCCFEmmb5n93AxZdnYSdBo33zIygQXb/AJgtYTwbt6sXd7r+Fpbs6O3I2dyQxqQhTC22+/zbJly1i4cCGdO3fm66+/Jjw8nLS0NFt3zSr27dvHxx9/TFJSEnXr1i30dq+++iqOjtYdjLNlyxaioqKsuk+TlJQUoqKiJGgkSpQMT7Nz6ekwciQYDOnASNRq+PTTT9FoNLbumigh6enpjBw5EpBzLeyDXJNC2Km7QRRPFydu39GTaMt6PknXYEU/45C0OzeM655ZDZUftdpT3KsQtoODCp3GkcS0TBJTMwlwt9rTFovcP4W96ty5M48+avz7HD58OH5+fkyfPp0NGzbw1FNP2bh3xde9e3fi4+Nxd3dn1qxZHD58+L7bREdHEx0dzcSJE5k6dWrJd7IYsrKy0Ov1tu6G+JeQTCM7l5kJS5fC119n8vXXS1m6dCmZmfZX5FFYT2ZmJkuXyrkW9kOuSSHskznT6O7QLJtm2Oz/BK7+AcnXQMky1jCyYsAIcmca5a1Z5K61s4wr5P4pyo6wsDDAODwrpx07dhAWFoabmxteXl706NGDEydOWLQZPHgw1atXz7PPyMjIPPXFVCoVY8eOZd26dYSGhqLRaKhXrx5bt27Ns/3u3btp2rQpWq2WoKAgPv3000Ifj4+PD+7uhY8eZ2Rk8PLLL/Pyyy8TFBT0QNtFRUVRq1YttFotvr6+tGrVipiYGMD42syfPx/AYligyaxZs2jRogW+vr64uLjQpEkTVq9ened5TK/b8uXLqVevHhqNhoULF+Lv7w9AVFSUed8lNRRO/HtJppGdc3KCGTMgM9MJmIGjIzg5Odm6W6IEOTk5MWPGDPPvQtiaXJNC2CdTIdjs4WmlHJTI1MPuDyHlJvy5yriu24dQ5THwrWn1p8tZ9ja/OtceLk5cjk/lxa//RxVvVxY82wR/d9tm9sj9U5QVpno/3t7e5nXbt2+nc+fO1KhRg8jISFJTU5k7dy4tW7bk4MGD+QaKCmP37t2sWbOG0aNH4+7uzscff0yfPn24cOECvr7GIvlHjhyhY8eO+Pv7ExkZSWZmJlOmTKFcuXLFPdR8ffTRR8TFxfHf//6XNWvWFHq7yMhIpk2bxvDhw2nWrBmJiYkcOHCAgwcP0qFDB0aOHMmVK1eIiYlh2bJlebafM2cO3bt355lnnkGv17Ny5Ur69evHpk2b6Nq1q0XbHTt28O233zJ27Fj8/Pxo0KABCxYsYNSoUfTq1YvevXsDUL9+/eK9GELkIkEjO+fsDBMmADgDE2zcG1EanJ2dmTBBzrWwH3JNCmGfctc0Skwt5Qyb3z+Hne9lL3sHQuNB4KAukae732xJdcu7c+JqItcS07mWmM6uUzfo3bhyifSlsOT++fC5c+cOAK6uruaMEb1eT0ZGBo6OjhZDEE1tXVxccHAwDvDIyMhAr9ejVqvRarVFamsNCQkJ3Lx5k7S0NH799VeioqLQaDR069bN3GbChAn4+Piwb98+fHx8AOjZsyeNGjViypQpLF26tEjPfeLECY4fP27O6Gnbti0NGjTgm2++YezYsQBMnjwZRVHYtWsXVatWBaBPnz488sgjxTnsfP3zzz+88847zJo1Cw8PjwfadvPmzXTp0oVFixbl+3jz5s0JDg4mJiaGZ599Ns/jf//9Ny4uLublsWPH0rhxY2bPnp0naHTy5EmOHDlCSEiIeV3t2rUZNWoU9evXz3f/QliDDE8TQgghhCiDTDGUnNPNl4p/jkLsbvjt7lCR0D4Q/gYMWFFiAaPc8ss0mt63PmtHt6BlTWOmQqkH0cS/gk6nQ6fTcfPmTfO6mTNnotPpzAEPk4CAAHQ6HRcuXDCvmz9/PjqdjmHDhlm0rV69OjqdzmLo15IlS9DpdAwYMMDqx9G+fXv8/f2pUqUKffv2xc3NjQ0bNlC5sjHQevXqVQ4fPszgwYPNASMwZrF06NCBLVu2FOu5cw4Bq1+/Ph4eHpw9exYAg8FAdHQ0PXv2NAeMAOrWrUtERESRn7cgr7/+OjVq1GD48OEPvK2XlxfHjh3j1KlTRXrunAGjuLg4EhISCAsL4+DBg3nahoeHWwSMhCgtkmlk57Ky4OpVY7EzuIqDA1SoUMH8DYR4+GRlZXH16lVAzrWwD3JNCmGfTIWhPU2ZRqVRy+fEJlj1TPay1gu6zwNn1xJ/6nvnGYGT2oFGVb2p6uPKHm6V/nC9fMj9U9ir+fPnExwcTEJCAl9++SW//PKLRZbU+fPnAWMmS25169YlOjqaO3fu4Obm9sDPnTMQZOLt7U1cXBwAN27cIDU1lVq1auVpV7t27WIFrHLbv38/y5Yt48cffyzS3+fbb79Njx49CA4OJjQ0lE6dOvHcc88VeojYpk2bmDp1KocPHyY9Pd28PnctKIDAwMAH7p8Q1iBBIzuXmgrGgH8qYIz8JycnF+kGLcqG1NRU87c8cq6FPZBrUgj7ZM40cjG+nSuVIMmej4z/ulcEFy9o8VKpBIzg/oWwTUwFsW1aGPwuuX8+fJKTkwHj8DSTCRMm8Morr+SZqv369euAZTbJmDFjGDFiBGq1ZVaeqaZQzraDBw9m4MCBedpaQ7Nmzcyzp/Xs2ZNWrVoxcOBATp48iU6ne6B95RfgAGPGUH4KOp77DUEtCRMnTiQsLIzAwEDzOTBlkV29epULFy7kG+Qyad26NWfOnGH9+vVs27aNzz//nA8//JCFCxfeN3Np165ddO/endatW/PJJ59QoUIFnJycWLx4MStWrMjTPue1IURpkqBRGeDoaHyjpFLJ6fq3yP2mQwhbk2tSCPuTd3haCQRJrhyGb58HfbLxCVNvg9oZRv4MugDrP9+95AwaFRwzMs8mZw+ZRiD3z4dNfoE/Z2dnnJ2dC9XWyckp36LoD9LW2tRqNdOmTaNt27bMmzePN954g2rVqgHGOjq5/fXXX/j5+Zn77O3tTXx8fJ52pmylB+Xv74+Li0u+Q77y609xXLhwgfPnz+ebxdO9e3c8PT3zPbacfHx8GDJkCEOGDCE5OZnWrVsTGRlpDhoVFFT7/vvv0Wq1REdHW2R5LV68uND9L2jfQliT/C9m59zcICMDwA2w/TdmouS5ubmRkSHnWtgPuSaFsG/m4WmpJRAk2fk+xOf64NfoudIPGJE9HA+4R56RfWUayf1TlBVt2rShWbNmfPTRR7zyyitUqFCBhg0bsnTpUiZNmoSXlxcAR48eZdu2bRZFl4OCgkhISODPP/80D8u6evUqa9euLVJf1Go1ERERrFu3ziLT58SJE0RHRxfvQHNZtGgRKSkpFut27NjB3LlzmTVrFnXq1Lnn9rdu3TLP+AbGmlc1a9bk4sWL5nWm4Fp8fLz5dQTjcapUKouMrNjYWNatW1fo/psy3u4X2BKiOCRoJIQQQghRhplmT0vNMJBhyMJJXcy6OXduwu4PIT0R/t5qXPf8etCVBwdH8KlRzB4XjcXwtHt8u+5uZ5lGQpQVEyZMoF+/fixZsoQXX3yRmTNn0rlzZ5o3b86wYcNITU1l7ty5eHp6EhkZad5uwIABvP766/Tq1Ytx48aRkpLCggULCA4Ozregc2FERUWxdetWwsLCGD16NJmZmcydO5d69erx559/3nf7hIQE5s6dC8CePXsAmDdvHl5eXnh5eZmLlnfs2DHPtqYATHh4uHkIX0FCQkJo06YNTZo0wcfHhwMHDrB69WqLouhNmjQBYNy4cURERKBWqxkwYABdu3Zl9uzZdOrUiYEDB3L9+nXmz59PzZo1C3WMYByyFhISwqpVqwgODsbHx4fQ0FBCQ0MLtb0QhSFBIyGEEEKIMshU/8MUJAFjoMTHLe8wmQfy49twMMdU2rUioEab4u3TCnJWO7lXppGHOdNIgkZCPIjevXsTFBTErFmzGDFiBO3bt2fr1q1MmTKFyZMn4+TkRHh4ONOnT7cYzuXr68vatWsZP348EydOJDAwkGnTpnHq1KkiB43q169PdHQ048ePZ/LkyVSuXJmoqCiuXr1aqIBKXFwcb731lsW6Dz74AIBq1arlmemuqMaNG8eGDRvYtm0b6enpVKtWjalTpzJhwgRzm969e/PSSy+xcuVKvv76axRFYcCAAbRr144vvviC999/n1deeYXAwECmT59ObGxsoYNGAJ9//jkvvfQSr776Knq9nilTpkjQSFiVSrFFxbEy5tKlS1SpUoWLFy+aixmWlvR0GD8eDIZ0YDxqNcyePdti3Kt4uKSnpzN+/HhAzrWwD3JNCmGfmk/7kasJaWwY25IBi/aTojfw84Q2VPMtQrHlLANc+h3SEuHb5yAzDR4bZRyG1uBp8Khg/QN4QLfv6Gn8TgwA56Z1KTDb6Nezt+i/aD81/NzY8VqbUuxhXra+f9ryPWxZlZaWxrlz5wgMDESr1dq6O0IIUWIKe7+zaabRr2dvseiXsxy5nMD1pHQ+fa4JEfXKmx+v/sbmfLeb1LkOI8ODAGj5/g4ux6daPD6xU21Gt6lpXj5xNZHJ64/yx6UEfN2cGdSiOi/e3d7eZWbCJ58AZAKfADBjxgz50PYQy8zM5JNP5FwL+yHXpBD2yfS1nwoVHlonUvSGog/J2j0bdkzNXq7QADpNu3fF6VJW2O85TcP17CHTSO6fQgghyjqbBo1SMgzUreBBv0er8OLX/8vz+G9vPmGxvPPkDV7//k86h1p+2zW+QzADmlUxL+s0OdO0M3jui99oVdOXd3s9wl//JDFx9R94aJ0Y+FjB0yfaCycnmDIFDAYnYApqNaUyi4KwHScnJ6ZMmWL+XQhbk2tSCPtkKgytUhmHqP2TCGNWHKSarxsfD2iIl2shh6llpMH+hcbffYJA6wkd3rargBHkGp5WiJpG9lAIW+6fQgghyjqbBo3a1g6gbe2CZ98IcLdMkYo5fo3mNXyp6utqsd5N45inrcm6w1fIMGQxo28DnB0dCC7nzvEriXy++2yZCBo5O4OxzpwzEGnTvojS4ezsbFFcUAhbk2tSCPuUM/GmVjkdp64nc/5WCudvpfDLqZt0b1Dx3js4sQk2vQoZKaBPBo/KMOY3UNtnycvCFlQwzZ6mz8wiPdOAxlFdgr26N7l/CiGEKOuKOb1G6bmRlM5Pf12nf9MqeR5bsPMMDd/eRpc5u/j05zNkGrLMjx06H0ezQB+cHbMPtXWwH2dv3CEhJf9voNLT00lMTDT/JCUlWf+AhBBCCCGKwRRDUalg9lMNWTHiMZpV9wEgMfU+WTZZWbA9Eu5cNwaMAFq9YrcBI7DMrLoXd42juY3MoCaEEEIUj/2+M8jl+4OXcNM4WtQ8AhjSsjr1Knri5erE/87HMWPrX1xPSuetbiEA3EhOp7K3ZWaSv05z97E0PF3zpgpPmzaNqKioEjqSB6MokJBgGsefgEoFnp6e90zLFmWboigkJCQAcq6FfZBrUgj7lLOmkdZJTYsgP9YcvMxvsbcLHpp18xTsXwB3bsCtU6DxgKFbjUPSPO28ULL5eO/NwUGFztmRpPRMElMz8NPZro6Q3D+FEEKUdWUmaPTtgYv0bFgRrZNlivHwsBrm3+tW8MBZ7cB/1h5hYqfaRU5HnjRpknmmC4DLly8TEhJStI4XU0oKeHsDpADeACQnJ+PmVoSZUUSZkJKSgre3nGthP+SaFMJe5c28MU03X2CGzcaX4fye7OXGz0O5eiXVQavKzqy6f+DFXWsMGtk600jun0IIIcq6MhE0+u3cbc7euMO8pxvft23Dql5kZilcikslyF+Hv07DzeR0izY37i776/Kvg6TRaCxmt0hMTCxG74UQQgghrM+caZQjhmIuAm0anqZPgYu/QpYBkq8ZA0YOjhD+BmjcodGzpdzr4itMro6HixNXEtJsHjQqc2L3wN6P4cphSP4H+i+Hut2yH/9pGhz9HhIvg9oJKjSEJyZD5Uez26Tchh8mwsmtoHKAkCeh03TQ6LLb/HMUtrwGlw+Cmx80e8E4PFIIIYTdKRNBo1W/X+SRSp6EVPS4b9vjVxJxUIGfmzHo06iaN7OiT5JhyMJJbaxrtPvUTWr4u+U7NM3euLqCXg+K4groUanA0bFMnDZRRK6uruj1ekDOtbAPck0KYZ/MmTc5wiimoJE5WLLhJTi62nLDkJ4QPqHkO2hlhS2EDdmvw9Alv+PvruGrYc0I8tfdZyvrK3P3z4wUKBdqDCauyieg6FsTuswE7+qQmQb75sOyXjDukDH4A7BmBCRdg+fXgSED1o82Zrj1/cL4eFqicZsabaDbh3DtGKwfaxwi+eiQUjpQIYQQhWXT/73upGcSe+uOefni7RSOXUnAy9WZSl4uACSlZbDlyFXe7Fo3z/b/Ox/H4YvxNK/hi07jyMELcbyz6Tg9G1UyB4R6NKzInO2neH31n7zYJoiT/ySxeE+sueaRvVOpwDhDqwqw/yCXKD6VSiXT8gq7ItekEPZJUfIZnuZiGp6WAXHn4dga4wPl6xsbOrtD2/+UdletorCFsAGaVvfh99g49IYsLsensvvUTZsEjcrc/bNWB+NPQer3s1yOeA8OLYNrR41BoBsn4fR2GPETVLo7QqDzTFjeFzpOBY8KcORbMOihx3xwdIaAuvDPEWMASoJGQghhd2waNPrzUgJPf7bfvDx18wkA+jSuzAdPNQBg4x9XUVDo3jDvtLEaRwc2/nGFj7b/jT4ziyo+rgxtFcjwsEBzGw+tE8uGNWPy+qN0m7sbH1dnxj1Ri4GPVS3hoxNCCCGEKDnZmUbZPLSOvKDeyMsX1sO8TFCyjB/mn19vgx5aV87C3/czsVMdnm9enambj7Ppz6vGIJqwrkw9/G8JaDyh3CPGdRd/M2YMVcpRUqJGG+MwtcsHwONJuPg7VGtpDBiZ1HwC9nwEqXHg4l2KByGEEOJ+bBo0ah7kS+z7Xe/ZZuBjVQsM8IRW8mTdmJb3fZ66FTz47sUWReqjren18OabYDDogTdRq+Hdd9/F2dn5vtuKskmv1/Pmm28Ccq6FfZBrUgj7lF9NI2+HVF52XIObkg4GABWE/Z8tumd15tFphZyArLynlvIexvqVtqptZC/3z6SkJIsanbnrdz6Qk1th9VDjUDb38vD8WnDzNT6WfA3c/C3bqx2NgaDka9ltvKtZtnELuPvYdQkaCSGEnXGwdQfEvWVkwKxZ8OGHGXz44SxmzZpFRoZ8W/Ywy8jIYNYsOdfCfsg1KYR9UpQcuUaxu2HdGOrtGoObKp3Tqqrw8h8w8SwEtrZpP63FPBzvAbYxDddLtFHQyF7unyEhIXh6epp/pk2bVvSdBYbBi7tgWIwxQ+i7wZB8w2p9FeJeIiMjCzWDohDCespARb5/NycneO01MBicgNdQqylbY+PFA3NycuK1114z/y6Erck1KYR9MoeMsvTw/XBIuoqpas9SQ2fe8a4OwL4zt/jxhDHLw9PFiSGtAtFpyt5bwPwyq+7HPJucjYan2cv98/jx41SqVMm8XOQsIwBnN/ANMv5UaQofN4JDXxkz2nTl4E6uAJIh0zjsTFfOuKwrlzfIdOf63ccCit4vUShLlixhyJDs2lFqtZpy5crRoUMH3n33XYvrpCw7efIkCxcu5Ndff+XgwYOkp6dz7tw5qlevnqdt9erVOX/+fJ71I0eOZOHChcXuy5YtW/jtt9+IjIws9r5yS0lJYcaMGbRp04Y2bdpYff9CgASN7J6zM8ycCeAMzLRxb0RpcHZ2ZuZMOdfCfsg1KYR98lSSeNThL3z+dwCSroJbACmPjmLy9n/43hDGFEMWjmoHxn97mKsJaebtvNycee7xavfYs30rTE0jEw+tqTC4bTKN7OX+6e7ujofH/WchLhIly1jfCKBKM0hLgCuHoGIj47pzPxvbVHr0bpum8OM7xpnV1HcDaWd+At9aMjStFL399tsEBgaSlpbG/v37WbJkCbt37+bo0aNotVpbd6/Y9u3bx8cff0xISAh169bl8OHD92zfsGFD/u//LIfyBgcHW6UvW7ZsYf78+SUWNIqKigKQoJEoMRI0EkIIIYQoaxSFT3mXes5n4fe765q9gFOrV1gd/QMAyemZeLk6c/uO8QN9oJ8b527e4UZSuo06XfrMmUapMrS2UNKT4fbZ7OX483D1T2Mwx9UHfpkFtbuAezlIuQW/fQ6JV6FeT2N7/9pQsz1sGAfdPoKsDNgyAUL7GGdOA3ikH+ycDuvHQqtX4Ppx+HWhcSY2UWo6d+7Mo48aA3nDhw/Hz8+P6dOns2HDBp566ikb9674unfvTnx8PO7u7syaNeu+QaNKlSrx7LPPlk7nrCArKwu9Xm/rboh/CalpZOcUxVjXSK9X0OszyMjIyFHDQDyMFEUhI0POtbAfck0KYYfO/UI91VnSFSfSyzWCOt2g2Qic1A5onYxv75LSMlEUhfTMLACaVfcBym4ApWjD00yZRrY55jJ3/7xyCD4NM/4ARP/H+PtP74FKDTf/hm+fg7lNYMUASL0NQ3+AgLrZ++j9GfgFw1fdYXk/qNocnpyT/bjWE55bawxIfRoO0f+F8Inw6BCE7YSFGc/5mTNnLNbv2LGDsLAw3Nzc8PLyokePHpw4ccKizeDBg/Md9pVf/SGVSsXYsWNZt24doaGhaDQa6tWrx9atW/Nsv3v3bpo2bYpWqyUoKIhPP/200Mfj4+ODu7t7oduDsXD9nTt3HmibjIwMoqKiqFWrFlqtFl9fX1q1akVMTAxgfG3mz58PGI/d9GMya9YsWrRoga+vLy4uLjRp0oTVq1fneR7T67Z8+XLq1auHRqNh4cKF+PsbC89HRUWZ910SGU3i300yjexcSgrodAApcLdSQXJyMm5ubrbslihBKSkp6HRyroX9kGtSCDuyYRz88Q1kGYdbrTS0oc1TS6nmm/136aF1Ii0jncS0DNIzs2vX+Lsbf7fVUK3iUihKIWzjW11bHXOZu38GhkFkQsGPD1h+/324+kDfL+7dpnwoDM0bJBC2ExsbC4C3d/YQwe3bt9O5c2dq1KhBZGQkqampzJ07l5YtW3Lw4MF8A0WFsXv3btasWcPo0aNxd3fn448/pk+fPly4cAFfX+NMfEeOHKFjx474+/sTGRlJZmYmU6ZMoVy5csU91Hzt2LEDV1dXDAYD1apV49VXX+Xll1++73aRkZFMmzaN4cOH06xZMxITEzlw4AAHDx6kQ4cOjBw5kitXrhATE8OyZcvybD9nzhy6d+/OM888g16vZ+XKlfTr149NmzbRtavlLOM7duzg22+/ZezYsfj5+dGgQQMWLFjAqFGj6NWrF7179wagfv361nlRhLhLgkZCCCGEEGXB9RNwcKl58Y6i5UtDZ9rkauaudeR6UjqJqZmkZ2SZ12cHjcp6ptGD1zSyVSFs8fAxZaK4urqar0W9Xk9GRgaOjo4WRcZNbV1cXHBwMGYAZmRkoNfrUavVFrWDHqStNSQkJHDz5k3S0tL49ddfiYqKQqPR0K1bN3ObCRMm4OPjw759+/DxMWYq9uzZk0aNGjFlyhSWLl1a0O7v6cSJExw/fpygoCAA2rZtS4MGDfjmm28YO3YsAJMnT0ZRFHbt2kXVqlUB6NOnD4888khxDjtf9evXp1WrVtSuXZtbt26xZMkSXnnlFa5cucL06dPvue3mzZvp0qULixYtyvfx5s2bExwcTExMTL7D3/7++29cXFzMy2PHjqVx48bMnj07T9Do5MmTHDlyhJCQEPO62rVrM2rUKOrXr1+mhteJskWGp9k5V1eIi4Pbt125fTuOuLg4XF1dbd0tUYJcXV2Ji5NzLeyHXJNC2NCxdcaZ0b4fDmtGGNfV7gKvHqN11qecV8rnKQxtmmb+ha8O0HrmTwA4qMDbzRkouwEU82xxD7CNqaZRWkYWGYas+7S2Prl/Pnx0Oh06nY6bN2+a182cOROdTmcOeJgEBASg0+m4cOGCed38+fPR6XQMGzbMom316tXR6XQWQ7+WLFmCTqdjwIABVj+O9u3b4+/vT5UqVejbty9ubm5s2LCBypUrA3D16lUOHz7M4MGDzQEjMAZYOnTowJYtW4r13KaAkWmfHh4enD1rrKdlMBiIjo6mZ8+e5oARQN26dYmIiCjy8xZkw4YNTJw4kR49ejB06FB+/vlnIiIimD17NpcuXbrntl5eXhw7doxTp04V6blzBozi4uJISEggLCyMgwcP5mkbHh5uETASorRI0MjOqVTg5QXe3iq8vb3w8vJ6oG/YRNmjUqnw8pJzLeyHXJNC2NDGl+HId8aff44Y17UYB56VuYMxoyH3n2WDyl4AJKVnknC3fpHWSY2H1rZDtazmAW5DOk12Ur0tjlvun8JezZ8/n5iYGFavXk2XLl24efOmRZaUaQr62rVr59m2bt263Lx584Hr/5jkDASZeHt7ExcXB8CNGzdITU2lVq1aedrl1x9rU6lUvPrqq2RmZrJz5857tn377beJj48nODiYRx55hAkTJvDnn38W+rk2bdrE448/jlarxcfHB39/fxYsWEBCQt5hooGBgQ96KEJYhQxPE0IIIYSwJ3GxxgCRokBavHFd2/+Csyv41IBqzYHs4Vq5TXkyhOebV+P9H/5i2/FrgDFo5F7Gh2qZCkk/SOjFUe2Am7OaO3oDiakZ+NzNthKiqJKTkwEsMscmTJjAK6+8gqOj5Uer69evA5bZJGPGjGHEiBGo1WqLtqaaQjnbDh48mIEDB+Zpaw3NmjUzz57Ws2dPWrVqxcCBAzl58qS5DldhFRQQNRgM+a4v6HjsqVh8lSpVALh9+/Y927Vu3ZozZ86wfv16tm3bxueff86HH37IwoULGT58+D233bVrF927d6d169Z88sknVKhQAScnJxYvXsyKFSvytM95bQhRmiRoZOf0enjvPTAY9MB7qNXwn//8B2dnedPzsNLr9bz3nnHaWTnXwh7INSlEKUpPgkVtjbNSmamg5Thw1Fg0NQ/XyvV5TaVSUcNfR0Wv7A8YGkeHMp9pVNSPkx4uTtzRG2xy3HL/fPjkV8zc2dk533ObX1snJyecnJyK1dba1Go106ZNo23btsybN4833niDatWqAcY6Orn99ddf+Pn5mfvs7e1NfHx8nnambKUH5e/vj4uLS75DvvLrT0kwDZUzzU52Lz4+PgwZMoQhQ4aQnJxM69atiYyMNAeNCgqqff/992i1WqKjoy2yvBYvXlzofkoGoygNMjzNzmVkQFQUTJ2awdSpUURFRZGRUTa/IRSFY5q6U861sBdyTQpRig6vMAaMXLyhyuPGn3Zv5gkYAeYoSkEfGkxBIrg7PM3FNP18pl19o19YRSmEDdl1jWyRYSX3T1FWtGnThmbNmvHRRx+RlpZGhQoVaNiwIUuXLrUICB09epRt27bRpUsX87qgoCASEhIshmVdvXqVtWvXFqkvarWaiIgI1q1bZ1EP6sSJE0RHRxdpnwW5fft2noyojIwM3n//fZydnWnbtu09t79165bFsk6no2bNmqSnp5vXmYJruQNrarUalUpl8fyxsbGsW7eu0P03ZbzlF7QTwlok08jOOTrC6NFgMDgCo1GryZP6Kh4ujo6OjB492vy7ELYm16QQJcyQCV91hwv7QLlbrLntm9BsxD03u98U9KYgERgzjUzBE0OWQoregJumrP093z3eB/xi3TQs73pSGolpGTirHdA6WX+4T37k/inKkgkTJtCvXz+WLFnCiy++yMyZM+ncuTPNmzdn2LBhpKamMnfuXDw9PYmMjDRvN2DAAF5//XV69erFuHHjSElJYcGCBQQHB+db0LkwoqKi2Lp1K2FhYYwePZrMzEzmzp1LvXr1ClUzKCEhgblz5wKwZ88eAObNm2euMWYqWr5hwwamTp1K3759CQwM5Pbt26xYsYKjR4/y3nvvUb58+Xs+T0hICG3atKFJkyb4+Phw4MABVq9ebVEUvUmTJgCMGzeOiIgI1Go1AwYMoGvXrsyePZtOnToxcOBArl+/zvz586lZs2ah6yK5uLgQEhLCqlWrCA4OxsfHh9DQUEJDQwu1vRCFIf972TmNBubPB9AA823cG1EaNBoN8+fLuRb2Q65JIUrYX5vg/J7sZa+q0ODp+26WnXmT/+PuOTKNNE5qXJzUODqoyMxSSErLLHNBI/PxPuB2poyrV1f9ARgDaEuHNuPxGr5W7F3+5P4pypLevXsTFBTErFmzGDFiBO3bt2fr1q1MmTKFyZMn4+TkRHh4ONOnT7coyuzr68vatWsZP348EydOJDAwkGnTpnHq1KkiB43q169PdHQ048ePZ/LkyVSuXJmoqCiuXr1aqIBKXFwcb731lsW6Dz74AIBq1aqZgzqPPPIIISEhfP3119y4cQNnZ2caNmzIt99+S79+/e77POPGjWPDhg1s27aN9PR0qlWrxtSpU5kwYYK5Te/evXnppZdYuXIlX3/9NYqiMGDAANq1a8cXX3zB+++/zyuvvEJgYCDTp08nNjb2gYppf/7557z00ku8+uqr6PV6pkyZIkEjYVUqpSzmJ5eyS5cuUaVKFS5evGiehlIIIYQQosgUBfbMgauH4fL/IP6CcVa05mPB1QfU969jEvSfLRiyFPZPeoLynto8j285cpXRy40f2B4L9GHVyOY0ensbcSkZbHu1NcHl3K19VCXq72tJdPzwF3zcnDn4VodCb7ds/3kiNxzDkJX9lvfV9sG83D7vzEwPG3kP++DS0tI4d+4cgYGBaLV5/66EEOJhUdj7Xdn6ikkIIYQQ4mFwYT9sn5K9rNZA8zHgXq7QuzDPJlZA6o2HNjvwZBqO5a51Ii4lg6QyOoMaPHim0XOPV+PpplXIUmDG1r/4fPe5Mn38QgghRGmSoJGdu3MHvLxAUe6gUnkBxkJn+c2wIB4Od+7cwcvLC5BzLeyDXJNCWNE/R+DWaThwd3acoHYQ3BkqNgL3e9fOyM08e1oBj1sMT3M0zn3i4WIqCl32ZlArTm68o9p4/F6uxkBaaRXFlvunEEKIsk6CRmVApvl9Xdl7gyeKJjNTzrWwL3JNCmEFt87AoraQlSNg0fFdKBdSpN0p94kaueeaPQ3AXXM3aJJa9jJtlCIWws7JVBQ7qRSDZnL/FEIIUZZJ0MjOubjApUuQleUCXMLBwVglXzy8XFxcuHTpkvl3IWxNrkkhiiFnesyvC40BI49K4B0IQW2LHDDKSVVA1Cjn7GlOastMoy93n+PXc7d55YlaBHiUjbot2S9l0aNGpuMvraCR3D+FEEKUdRI0snMODlCpEoADUMnGvRGlwcHBgUqV5FwL+yHXpBBFdOcmfNYO4s9bru/5CdRoU6xd55zHpDCzp2VmZQFQycsVgD8uJfDHpQQqebkwpm3NYvWltNxvtrjCMGdaldLwNLl/CiGEKOskaCSEEEIIURJ+/zxvwKhaSwgML/aucyYwFRRD0Tiqzb/rM41Bo5efqEXNAB0/HL3KrlM3uZWsL3ZfSot5eFox9mEKpJXm8DQhhBCiLJOgkZ3T62HOHMjM1ANzcHSEl19+GWdnZ1t3TZQQvV7PnDlzADnXwj7INSnEA8hMh5gpkHARYncZ1/WYbyx2DeDqU7xUmbty1oRWFWJ/pqCRp6sTAx+rSnyqnl2nbpapWcSskWlkGrJXWsct908hhBBlnQSN7FxGBkycCJABTARg9OjR8qbjIZaRkcHEiXKuhf2Qa1KIB3B4Ofy6IHvZozLU7w9qp4K3KQKL4WmFaK83ZFksmwpCl9YwLWsqqIZTYZgyjRJTSyfTSO6fQgghyjoJGtk5R0cYNAgMBkdgEGo1ODrKaXuYOTo6MmjQIPPvQtiaXJNCFMK5XyDpGuz7xLjc8Fmo3MQ4FM3KAaPcCpN5Y8o0MvH4lw7TMmUa6Q1ZpGUYzLPKlRS5fwohhCjr5H8vO6fRwJIlABpgiU37IkqHRqNhifGkC2EX5JoU4j7+3gYr+mUvO+ug03ug9Syxp1Tu38SCIctyCw8bTD1fXNYYnqZzdkSlMu4rMS2jxINGcv8UQghR1jnYugNCCCGEEGVSlgEy9bD3Y+OyX23jrGjdPirRgBHkLoRdcBTlzS510Wkciexez2K9eZhWGRqeZo1C2A4OKnTO/84sKyGEEKIoJNNICCGEEOJBXTsOiztBWoJxWaWGZ78Hryql8vQKhZg+DRjRugZDWwWidrBslF0QuuwETrIzjYpXSNzDxYmk9MwydexCCCGErUimkZ27cwe8vMDT8w5eXl54eXlx584dW3dLlKA7d+RcC/si16QQ+dg9OztgBNBkcKkFjCBXptF9Yii5A0aQsyB0hkVRbXtmrV7mPPaSJvdPIawrMjKy2IFjIcSDkaBRGZCQAImJkJCQQEJCwv03EGWenGthb+SaFGVFYloGi345wwfbTrJkz7k8BaCLJeU2rH0RVgyAY2uN64ZshTcuQrfZ1nueB1SUj0+m2dMysxTSMqz4GpUgU3CruJ8XS7uek9w/hT1ZsmQJKpXK/OPo6EilSpUYPHgwly9ftnX3rObkyZO8+uqrtGjRAq1Wi0qlIjY2tsD2SUlJTJw4kcDAQDQaDZUqVaJv376kpKQUuy9btmwhMjKy2PvJT0pKCpGRkezcubNE9i8EyPA0u+fiAn//DVlZLsDfODiAi4uLrbslSpCLiwt///23+XchbE2uSVGWfPv7Rd7b8pd5ubynlk6hFayz810fwB/fZC9XawXVmltn3w/IMtPowaMobs5qHFSQpUBSWgYuziVbENqaihs0MmUanbmRzNWEVCp4ltx9Te6fwl69/fbbBAYGkpaWxv79+1myZAm7d+/m6NGjaLVaW3ev2Pbt28fHH39MSEgIdevW5fDhwwW2TUhIIDw8nEuXLvHCCy9Qs2ZNbty4wa5du0hPT8fV1bVYfdmyZQvz588vkcBRSkoKUVFRALRp08bq+xcCJGhk9xwcoFYtMCaF1bJxb0RpcHBwoFYtOdfCfsg1KcqShFxDjv5JSCv6zgyZ8PcPkJ4MKHDwK+P6sNfApwbU6lD0fRdTzppGRYmhqFQq3LVOJKRmkJiWQYCH/X9ItNbwNFM9p9kxfzM75m/mDWxEt/oVrbR3S3L/FPaqc+fOPProowAMHz4cPz8/pk+fzoYNG3jqqads3Lvi6969O/Hx8bi7uzNr1qx7Bo0mTZrE+fPnOXjwIIGBgeb1r7/+ein0tGiysrLQ6/W27ob4l5DhaUKIf60r8am8t+UE7//wFzeT023dHSGEFeSeWr5YQ5D2z4dVz8K6F2HdKEhPBN+a0PZNaPQM6AKK2duie5CaRgXxcDHNoFY2CkKbC2EXa/406Fa/AhU8tWgcjW+D/7wkQ8eECAsLA+DMmTMW63fs2EFYWBhubm54eXnRo0cPTpw4YdFm8ODBVK9ePc8+86s/pFKpGDt2LOvWrSM0NBSNRkO9evXYunVrnu13795N06ZN0Wq1BAUF8emnnxb6eHx8fHB3d79vu/j4eBYvXswLL7xAYGAger2e9PTCvyfMyMggKiqKWrVqodVq8fX1pVWrVsTExADG12b+/PkAFsMCTWbNmkWLFi3w9fXFxcWFJk2asHr16jzPY3rdli9fTr169dBoNCxcuBB/f38AoqKizPsuqaFw4t9LMo3sXEYGLFoEBkMGsAi1Gl544QWcnJxs3TVRQjIyMli0aBEg57qkLd5zjs92nQPA1VnNuCfk2+D8yDUpyhJDrqLOSelFCIhk6iEzDfYvNC5XbgpaT3BwghZjjWnANpbzKIsaRHHXOAGppVIQ2jqsU9PoibrleKJuOT7+8RSzY/4u0eOX+6coK0z1fry9vc3rtm/fTufOnalRowaRkZGkpqYyd+5cWrZsycGDB/MNFBXG7t27WbNmDaNHj8bd3Z2PP/6YPn36cOHCBXx9fQE4cuQIHTt2xN/fn8jISDIzM5kyZQrlypUr7qHm6UtaWho1a9akb9++rFu3jqysLJo3b878+fNp2LDhPbePjIxk2rRpDB8+nGbNmpGYmMiBAwc4ePAgHTp0YOTIkVy5coWYmBiWLVuWZ/s5c+bQvXt3nnnmGfR6PStXrqRfv35s2rSJrl27WrTdsWMH3377LWPHjsXPz48GDRqwYMECRo0aRa9evejduzcA9evXt9rrIwRI0Mju6fUwdiyAHhgLGCPW8qbj4aXX6xk7Vs51abijN5h/l0yjgsk1KcqS3BOBPXBA4K/N8O0gyLq7nVsADN4MjhrrdNBKcs54VtQgiqm2T1mZej4708g6SuP45f758DHNgufq6mrOGNHr9WRkZODo6IhGo8nT1sXFBYe7weaMjAz0ej1qtdqidtCDtLWGhIQEbt68SVpaGr/++itRUVFoNBq6detmbjNhwgR8fHzYt28fPj4+APTs2ZNGjRoxZcoUli5dWqTnPnHiBMePHycoKAiAtm3b0qBBA7755hvz38vkyZNRFIVdu3ZRtWpVAPr06cMjjzxSnMPO49SpU4BxiFpQUBBfffUVCQkJREVF0a5dO44dO0aFCgXXxdu8eTNdunQxB4dza968OcHBwcTExPDss8/mefzvv/+2qHc2duxYGjduzOzZs/MEjU6ePMmRI0cICQkxr6tduzajRo2ifv36+e5fCGuw/Vdl4p7UaujbF3r1UtOrV1/69u2LWl12ilWKB6dWq+nbV851acj54bKsfGiyBbkmRVmw8rcLhM3YwaJfzgLG7EF4wL9tRYGfpmUHjFBB6wl2FzAC69T3MdX2KSv3P9MxW2u6bdMsaolpJZdpJPfPh49Op0On03Hz5k3zupkzZ6LT6cwBD5OAgAB0Oh0XLlwwr5s/fz46nY5hw4ZZtK1evTo6nc5i6NeSJUvQ6XQMGDDA6sfRvn17/P39qVKlCn379sXNzY0NGzZQuXJlAK5evcrhw4cZPHiwOWAExiyWDh06sGXLlmI9tylgZNqnh4cHZ88a798Gg4Ho6Gh69uxpDhgB1K1bl4iIiCI/b36Sk5MB433lxx9/ZODAgYwaNYp169YRFxdnHlpWEC8vL44dO2YOPj2onAGjuLg4EhISCAsL4+DBg3nahoeHWwSMhCgtkmlk57Ra+O47AC3wnY17I0qDVqvlu+/kXJeO7I9dZWd4RumTa1KUBSt/v8jF26nmZW9XZ1L0qYULCFz/C36aCmkJcO0IOLrAuEPg4g1O9lkg2ho1jUyZNgt/PkPM8X94s2tdagbcvwaIrZRUplFJ1nSS+6ewV/Pnzyc4OJiEhAS+/PJLfvnlF4ssqfPnzwPGTJbc6tatS3R0NHfu3MHNze2BnztnIMjE29ubuLg4AG7cuEFqamq+ReRr165drIBVbqagzZNPPolOpzOvf/zxxwkMDGTv3r333P7tt9+mR48eBAcHExoaSqdOnXjuuecKPURs06ZNTJ06lcOHD1vUUsovOJ6zSLcQpUkyjYQQ/1pZWdm/l5Vv2oUQ+VNyjUvzdDFlkRTib3vbm3BiI5z7xbjc6FnwqGC3ASPAItWoqDWNAn2NH/Yu3E7hp5M3+PbAJWv0rORZKWqUnWklXxqIwktOTiY5ORk/Pz/zugkTJpCcnMy8efMs2l6/fp3k5GSLIMmYMWNITk7miy++sGgbGxtLcnIydevWNa8bPHgwycnJrFy50urH0axZM9q3b0+fPn3YsGEDoaGhDBw40Jx58yAKyv4zGAz5ri8o6y73fbw0VKxonDkxv1pJAQEB5kBWQVq3bs2ZM2f48ssvCQ0N5fPPP6dx48Z8/vnn933uXbt20b17d7RaLZ988glbtmwhJiaGgQMH5vta5MxKEqI0SaaREOJfK+eU1SU5PEEIUfJyv732cr1PQCA9Cf7aAqm34fR2QAVdZxmzi4I7l2hfrSHn/auomUYvhNcgtJIn6w5fZv3hKySk2Pd90NofKM2ZRqnypYEovPwya5ydnXF2di5UWycnp3xrWz1IW2tTq9VMmzaNtm3bMm/ePN544w2qVasGGOvo5PbXX3/h5+dn7rO3tzfx8fF52pmylR6Uv78/Li4u+Q75yq8/xdGkSRMALl++nOexK1euUKdOnfvuw8fHhyFDhjBkyBCSk5Np3bo1kZGRDB8+HCg4qPb999+j1WqJjo62yPJavHhxoftvreG6QtyLZBrZuZQUqFQJKlRIoWLFSlSqVImUlBRbd0uUoJSUFCpVknNdGqSmUeHINSnKgtzxBG9X4we4AgMCWyfB2hdg6xvG5dpdoOlwCO0Dzq4l2FPrsBieVsR9aBzVtK0TQJNqxtmS7D14bq5pZKX9mWoalWSmkdw/RVnRpk0bmjVrxkcffURaWhoVKlSgYcOGLF261CIgdPToUbZt20aXLl3M64KCgkhISODPP/80r7t69Spr164tUl/UajURERGsW7fOoh7UiRMniI6OLtI+C1K7dm0aNGjA+vXrLepUbdu2jYsXL9KhQ4d7bn/r1i2LZZ1OR82aNS2GmpmCa7kDa2q1GpVKZZGRFRsby7p16wrdf1dX13z3LYQ1SaaRnVMUuHIFjG+VrtxdV/qpm6L0KIrClStyrktDzlfX3j8s2ZJck6IsUHLlGnkWlGmkvwN3bsCfq4zLQe1A6wlt/1sa3bSanEdb3G+as4Mn9h08N9c0snIh7PTMLNIzDWgcrV+oWu6foiyZMGEC/fr1Y8mSJbz44ovMnDmTzp0707x5c4YNG0Zqaipz587F09OTyMhI83YDBgzg9ddfp1evXowbN46UlBQWLFhAcHBwvgWdCyMqKoqtW7cSFhbG6NGjyczMZO7cudSrV88iOFWQhIQE5s6dC8CePXsAmDdvHl5eXnh5eVkULf/www/p0KEDrVq1YuTIkSQkJDB79myCg4MZNWrUPZ8nJCSENm3a0KRJE3x8fDhw4ACrV6+22L8pm2ncuHFERESgVqsZMGAAXbt2Zfbs2XTq1ImBAwdy/fp15s+fT82aNQt1jGAcshYSEsKqVasIDg7Gx8eH0NBQQkNDC7W9EIUhQSM7p9XCoUNgMGiBQ6jVWH3KTWFftFothw4dMv8uSk7O9+/J6ZlkZSk4OEiab25yTYqyIG+mUT4Bgb1zYVuO4FClR+G5on0Tbms5AxDFvWtlF4S27+C5KTBorbu0Tpv9NjgpLRONzvpBI7l/irKkd+/eBAUFMWvWLEaMGEH79u3ZunUrU6ZMYfLkyTg5OREeHs706dMtijL7+vqydu1axo8fz8SJEwkMDGTatGmcOnWqyEGj+vXrEx0dzfjx45k8eTKVK1cmKiqKq1evFiqgEhcXx1tvvWWx7oMPPgCgWrVqFkGdtm3bsnXrVt566y3+85//4OrqSs+ePZkxY4ZFcez8jBs3jg0bNrBt2zbS09OpVq0aU6dOZcKECeY2vXv35qWXXmLlypV8/fXXKIrCgAEDaNeuHV988QXvv/8+r7zyCoGBgUyfPp3Y2NhCB40APv/8c1566SVeffVV9Ho9U6ZMkaCRsCqVIl973NelS5eoUqUKFy9eNE9DKYQo+8Z/e5g1B7PHsP8Z2dH8zbMQomzpMmcXx68mmpff6FyH93/4C4AD/22Pn7MBPgyB1LtFTdUaGLACarW3RXeL7WZyOo9O3Q7AuWldipV989u52zz16T4C/dz46bU2Vuqh9e09fZOBn/9KcDkd214Nt8o+Q6dEk5yeyU+vtSHQ78FngbJ38h72waWlpXHu3DkCAwMl0CeEeKgV9n4nmUZCiH+vXCHzpLRMCRoJUUbl/gbM0UFFR81xBmZtxG3VAshMMgaMvKrB6P2gdgb1w/E2qNjD01yMr4O9zyKWXdPIehmh7lpHktMzSUy172MXQgghbOXheLf0EMvIgOXLITMzA5VqOWo1PPPMM6Uyk4KwjYyMDJYvXw7IuS5pWbkSLRNTM6jkJdOZ5ibXpCgLcidOO2IgSvUpFdQ34GKOB5qPKROFru/Hmnni7neD5WVlFjFrThbkrnXkakLJ1XOS+6cQQoiyToJGdk6vhyFDAPTAEAD69esnbzoeYnq9niFD5FyXhtyfuey9CKytyDUpypIA4mirPkzTM9FU4Aa3FR03WkZRu7wHaNyhVoStu2gVuQt/F4fH3do+ekMWaRkGtE7Wr+1jDSVRUKGkZ1CT+6cQQoiyzqZBo1/P3mLRL2c5cjmB60npfPpcEyLqlTc//n/f/sH3By9ZbNM62J+vhjYzL8en6Jmy4Rg/nriOSgWdQ8sz5cl6uGmyD+3E1UQmrz/KH5cS8HVzZlCL6rwYHlTyB2gFajV06QIGgxqVqgsODsbpGcXDS61Wm6cxlXNdsnJ/ALH3oRm2ItekKAuMf88KC50/pLHDaYg1rl9heIJ9F0KplOiCt6szo6sa8HRxsGFPrcQ8k1jxd+Xm7IhKZXwNE9My7DdoZMVAmUlJFwGX+6cQQoiyzqZBo5QMA3UreNDv0Sq8+PX/8m0THuzPzH71zcuaXP/hvrzyMNeT0lk2rBmZWQoTvvuDSWuO8PHTjQDjh8DnvviNVjV9ebfXI/z1TxITV/+Bh9aJgY9VLbmDsxKtFjZvBtACm23cG1EatFotmzfLuS4NuT9+2PvMQbYi16Swe4qCm5JES4ejNHY4TbrixLXyrbmcpmHRtW4knr5lblrFx5VnH69mw85aR3Z9n+JzcFCh0ziSlJZJUlomAe5W2GkJUMyBMuuNT/NwMWb+HLmcQHC5OBpU9rLqLJpy/xRCCFHW2TRo1LZ2AG1rB9yzjbOjAwHu+VfyPn09iZ//vsGGsS2pX9kLgMju9Riy5Hfe7FqXch5a1h2+QoYhixl9G+Ds6EBwOXeOX0nk891ny0TQSAhRcnLXQJHhaUKUUevHsCZxOTgbF9cZWpIUOouu9Svw0h9XycjKIub4NQ5diOdWst62fbUSawdQPLRO5qCRvbJmoMzENDzt6/0X+Hr/BSZ3C2Foq8D7bCWEEEL8e9h9fvb+s7do8k4M7Wbt5M21R4i7k/1m7+D5eDy0juaAEUCrmn44qFQcuhAPwKHzcTQL9MHZMftQWwf7cfbGHRJSJKtAiH+z3JlGy/ad55WVhzh3845N+iOEKILbZ+HwCvPiLcWdTw3dSNEbqODpwojWNRjdpibNAn2Ahyej0DRUy1oBFPMwLTueRcwU6LdmIey+TSrTqKoXfjoNAKeuJ1tv50IIIcRDwK4LYYfX9qdTaHmq+Lhw/lYKM6NPMnjxb6wZ3RK1g4obyenm/+RNHNUOeLk4cSM5HYAbyelU9racJcX/7jY3ktPwdM1bkDA9PZ309HTzclJSkrUPrdBSUqBBA8jKSkGlaoBKBX/88QeurmV/5heRv5SUFBo0aADIuS5xd6NG/u4abiSlc+p6MqeuJ+Or0/BWtxDb9s2OyDUp7NKhr+GPlZB0FVD4Td2Y/nfGo6ACVNxJt8yYKemCx6VNsWJNI8j5+pSBTCMrBo0aVPFi7eiWLN5zjqiNx61+fcj9UwghRFln10Gj7g0qmn+vU96DuuU9aD3zJ/afvUXLmn4l9rzTpk0jKiqqxPb/IBQFTp8G41ul03fXlcD0IcJuKIrC6dNyrkuD6Zv64a0CKe+pJfrYP2w58g9xKQ/H8BVrkWtS2J2U27D5NchMNa/61rknyp3srOLkPEEj41seew6KPIjsoVpWGp7mYnp97D+oZq1jzsn9btAs0crXh9w/hRBClHV2HTTKraqvKz5uzsTeukPLmn746zTcTE63aJNpyCI+NcOcTZRfG1MWkr8u/1pJkyZNYvz48ebly5cvExJim6wDrRZ27waDQQvsRq02FlUUDy+tVsvu3bvNv4uSk5Vl/NdV40iPhpW4k25gy5F/SEx9OD5UWotck8Ju3DgJ536BS78bA0YBIdB6ArhX4ODqdCB7aGnuTKPsoID9B0UKwxyAsFL8pEy8PiUYc3HXlkzQTO6fQgghyroyFTS6mpBKXIreXBi7cTUvEtMyOXIpgUcqewKw98wtshSFRlW9AGhUzZtZ0SfJMGThpDZ+A7n71E1q+LvlOzQNQKPRoNFkD3tLTEwswaO6N7UaWrYEUAMtbdYPUXrUajUtW8q5Lg25a4KUpW/aS5Nck8IuZKbD0u6Q/E/2uhYvQWhv4+/KTgDqVfTg72tJjG1X02Lz7L/vhyMobOWYkTkT65OdZ1h76ApR3euZ60DZC/M92/qJRiU2PE/un0IIIco6mxbCvpOeybErCRy7kgDAxdspHLuSwOX4VO6kZ/LelhMcvBDHxdsp7Dl9kxFfHaC6rxutg41D02oGuBMe7M8ba/7k8MV4DsTeZsqGYzxZvyLlPIyBpR4NK+KkduD11X/y97UkNv5xhcV7YhneqobNjlsIYR9y1wRxLwM1PYT4V8nKguTrxp9Dy4wBIxcfCOkJLcbBI/3MTU1JKJHd63E0KoKaueaNN2fS2HGh56KwVgCldnkPAOJTMjhxNZG1hy5ZZ8dWZO1AWU5loRC4EAIiIyOtNmukEKJwbJpp9OelBJ7+bL95eermEwD0aVyZd3uFcuJqIt//7xKJaRkEuGtpHezH+A610TiqzdvMGdCQyeuP8cxn+3FQqegUWp7I7vXMj3tonVg2rBmT1x+l29zd+Lg6M+6JWgx8rGrpHWgxZGbC2rVgMGQCa1GroVevXjg6lqkkMfEAMjMzWbt2LSDnuqTlrgli/tAgmUYW5JoUNpGVBV+0h8v/s1zfchy0ejVPc/PMWmDxPsHE/WGraWQOoFjnw9PTzarQqKoX3//vEp/vPmeXw3SVkqiEfZenS8l8aSD3T2FvlixZwpAhQ8zLarWacuXK0aFDB959910qVapkw95Zz8mTJ1m4cCG//vorBw8eJD09nXPnzlG9enWLdjt37qRt27YF7mfq1Km8+eabxerLli1b+O2334iMjCzWfvKTkpLCjBkzaNOmDW3atLH6/oUAGweNmgf5Evt+1wIfXzbssfvuw8vVmY+fbnTPNnUrePDdiy0euH/2ID0dnnoKIB14CoDk5GR50/EQS09P56mn5FyXhtyZRmVh9iBbkGtS2MTpmBwBo7t/pL41ocngfJvfL56Q8+9bUZQy/021tYdqqVQq6lbwIKSiMePIHoPn2YF+6zMFFVMzDBYlDYpL7p/CXr399tsEBgaSlpbG/v37WbJkCbt37+bo0aMPRf2tffv28fHHHxMSEkLdunU5fPhwvu3q1q3LsmXL8qxftmwZ27Zto2PHjsXuy5YtW5g/f36JBY1MEzhJ0EiUFPmfy845OEB4OBgMDqhU4Tg4gIODTUcVihLm4OBAeHi4+XdRknLVNMpRCPVh+FBpLXJNilK1czqc2QFxscblFi9Bx6n33Sx7Yqr8/25NQQG9IYv0zCy0TnmzkcqSkhqqVVKziFmDOZusBG7NOk32W+KktEx83Jytsl+5fwp71blzZx599FEAhg8fjp+fH9OnT2fDhg3mQGdZ1r17d+Lj43F3d2fWrFkFBo3KlSvHs88+m2d9VFQUtWrVomnTpiXc06LJyspCr5fZfkXpkP+97JyLC+zcCbt2ufDLLzvZuXMnLi4utu6WKEEuLi7s3CnnujSYPnQ5qEzD04wflrIUuKM32KpbdkeuSVFqEi7Dzvfg4n5j/SK1BpqOKNSm98u8cXN2xOHuY/aYRfOgsjOrrBtB8SihWcSsqSTC+Y5qB9ycjYFEax673D9FWREWFgbAmTNnLNbv2LGDsLAw3Nzc8PLyokePHpw4ccKizeDBg/MM+4L86w+pVCrGjh3LunXrCA0NRaPRUK9ePbZu3Zpn+927d9O0aVO0Wi1BQUF8+umnhT4eHx8f3N3d798wH7/99hunT5/mmWeeuW/bjIwMc4BJq9Xi6+tLq1atiImJAYyvzfz58wHjsZt+TGbNmkWLFi3w9fXFxcWFJk2asHr16jzPY3rdli9fTr169dBoNCxcuBB/f3/AGOQy7bskMprEv5tkGgkh/rWycn1Vr3VywEmtIsOgkJSWYfHNsxCiBJ3fC1cOw7VjxuWAEGgzCfxqgXe1Qu3ifpk3Dg4qdBpHEtMySUzNJKBonyXsRs4aTtaUXTDcDjONSnj/7lon7ugNdnnsQpS02NhYALy9vc3rtm/fTufOnalRowaRkZGkpqYyd+5cWrZsycGDB/MNFBXG7t27WbNmDaNHj8bd3Z2PP/6YPn36cOHCBXx9fQE4cuQIHTt2xN/fn8jISDIzM5kyZQrlypUr7qHe1/LlywEKFTSKjIxk2rRpDB8+nGbNmpGYmMiBAwc4ePAgHTp0YOTIkVy5coWYmJh8h8HNmTOH7t2788wzz6DX61m5ciX9+vVj06ZNdO1qWcZlx44dfPvtt4wdOxY/Pz8aNGjAggULGDVqFL169aJ3b+NsovXr17fCqyBENvlEJIT418pdH0OlUuGudeL2HT2JqZlU8LRVz4QonhtJ6Ry6EEf7uuVwcLDzYZbxF2Dpk5CV44N6nW4Q0v2BdpNdo6zg43XXOpGYlmnXWTSFdZ/ReEXmbseZRoU5x8Xh4eLIP4n2eezCfty5cwcAV1dX87Wo1+vJyMjA0dERjUaTp62Li4t5eGJGRgZ6vR61Wm1RO+hB2lpDQkICN2/eJC0tjV9//ZWoqCg0Gg3dunUzt5kwYQI+Pj7s27cPHx8fAHr27EmjRo2YMmUKS5cuLdJznzhxguPHjxMUFARA27ZtadCgAd988w1jx44FYPLkySiKwq5du6ha1TiBUZ8+fXjkkUeKc9j3ZTAYWLVqFc2aNaNmzZr3bb9582a6dOnCokWL8n28efPmBAcHExMTk+8wuL///tsiC3Hs2LE0btyY2bNn5wkanTx5kiNHjhASEmJeV7t2bUaNGkX9+vXz3b8Q1iDD0+xcaio0bAj166dSv35DGjZsSGpqqq27JUpQamoqDRvKuS4N+X0AKQtDM0qbXJNlT7e5u3hh2f9Y8dsFW3elYJnpxuFoe+cZA0begRDaFx4dCo+9WOTd3iuc4FFCM2TZkrXDJ6bXKD0zi/RMexumWzLZVSbZ9Zysd/+X++fDR6fTodPpuHnzpnndzJkz0el05oCHSUBAADqdjgsXsu/F8+fPR6fTMWzYMIu21atXR6fTWQz9WrJkCTqdjgEDBlj9ONq3b4+/vz9VqlShb9++uLm5sWHDBipXrgzA1atXOXz4MIMHDzYHjMCYxdKhQwe2bNlSrOc2BYxM+/Tw8ODs2bOAMXATHR1Nz549zQEjMBatjoiIKPLzFsaPP/7ItWvXCpVlBODl5cWxY8c4depUkZ4vZ8AoLi6OhIQEwsLCOHjwYJ624eHhFgEjIUqLZBrZuaws+OMPgCzgj7vrsmzZJVHCsrKy+OMPOdelIb+ZeEriQ0NZJ9dk2XMtMR2AjX9c4dnHCze8q1SlJcD8xyHpSva6Tu9D7U5F3mVhiiSbsmgehr9vpYTGauUuCK3R2U/B8NwzXlpb9vVhvaCi3D+FvZo/fz7BwcEkJCTw5Zdf8ssvv1hkSZ0/fx4wZrLkVrduXaKjo7lz5w5ubm4P/Nw5A0Em3t7exMXFAXDjxg1SU1OpVatWnna1a9cuVsDqfpYvX45araZ///6Fav/222/To0cPgoODCQ0NpVOnTjz33HOFHiK2adMmpk6dyuHDh0lPTzevzy+jMjAwsHAHIYSVSdDIzmm1sG0bGAxaYBtqNQ/FNJiiYFqtlm3btpl/FyUnvw+Z2UMzHp5MhOKSa7LsSk630+v44LK7ASMVqJ2gWkuoVfxpjQFU98hDMWUSzv3xNNHHrjGpcx0qepXV4sSm+5d1Iyjqu7WfktMzSUrLxE+nuf9GpSQ70F9Cw9O01s9Ek/vnwyc5ORkwDk8zmTBhAq+88gqOjpYfra5fvw5YZpOMGTOGESNGoFZbBmRNNYVyth08eDADBw7M09YamjVrZp49rWfPnrRq1YqBAwdy8uRJdDrdA+2roPuQwZB/tmJBx6OUVDS8kFJTU1m7di3t27cvdO2k1q1bc+bMGdavX8+2bdv4/PPP+fDDD1m4cCHDhw+/57a7du2ie/futG7dmk8++YQKFSrg5OTE4sWLWbFiRZ72Ukxf2IoEjeycWg0dOgCogQ427o0oDWq1mg4d5FyXppzvdTzseLppW5Frsuyyq6CRIQM2vgI3/oKbd9P4n/wImgy2yu4L81Gjqo/xG/GT15I4eS2JOuXdGdP2/jUr7FFJZt14aE1Bo+yMrD8vxfP9/y6hUql45rGq1CpX+pXElfzSQ63I9KXB7+duU8FTS9vaAbg4F+/Dutw/Hz75Zdb8P3vnHd5Wef7vW1uyLTlx4uydEJIQCCthJ+xRNpSW0tKyoZRSyrfQAi3jV1q6oC2zbCib0jLKnmUkQMIKCUlIyN6x49iWbG3p98expCPHQ+MsOc99Xb4sHR2d8x69r87R+znP83ncbjdut7ugdV0uFy6Xq6x1tcbhcHDTTTdxyCGHcPvtt/OrX/2K0aOVKNWvv/56u/WXLFnCwIEDs23u378/zc3N262XiVYqlvr6enw+X5cpX121RyteeOEFgsFgwalpGerq6jj77LM5++yzCYVCzJw5k+uvvz4rGnUnqv373//G6/Xy2muv5UV5PfjggwXvWy+PN0FQI55GgiDssGQmIHbVBTebnhCu/PQVQbBUxNyi5+GLR2H9JxBtgZrBsOt3NNt8ISLK5UdO5Lbv7cEhOyslipvbY5rt32j01E+6qqB240uLefjD1Tw0ZxW3vLFUh70Wjl5TpP5VyqT/1a82cfFjn3HHO9/otCdBsB4HH3wwM2bM4G9/+xuRSIShQ4ey++678/DDD+cJQgsXLuT111/nW9/6VnbZ+PHjaWlp4csvv8wu27hxI88++2xJbXE4HBx11FE899xzeX5Qixcv5rXXXitpm4Xw+OOPU1VVxcknn1zwe7Zu3Zr3vKamhgkTJuSlmmXEtc7CmsPhwGaz5UVkrVq1iueee67g/Wci3roS7QRBKyTSyOIkEvDaa5BMJoDXcDjgqKOO2i78Veg7JBKJ7AVR+lpfUl2EQWdMYP/54SpeX7QZj9POL4/emb1G12237o6CjMnKJWS2aLToedjWcaf5i45Q+73OgolHw5DdwF3V7VuLJV1ArFGNx8nx04axemsb73zdYC1RrUj0rCQW8G1fEEAtpDeGotu9xwgK6eNyOG3vEaxoDPH1piDLG9pY09Re9jbl/ClUEldccQWnnXYaDz30EBdddBF//vOfOeaYY9hvv/0499xzCYfD3HbbbdTW1nL99ddn33f66afzy1/+kpNPPplLL72U9vZ27rrrLiZOnNiloXMh3HDDDbz66qscdNBBXHzxxSQSCW677TZ22WWXPHGqO1paWrjtttsAmD17NgC33347/fr1o1+/ftuZljc1NfHKK69w6qmnFpWeN2XKFA4++GD22msv6urq+OSTT3jmmWfytr/XXnsBcOmll3LUUUfhcDg4/fTTOfbYY7nllls4+uijOeOMM9iyZQt33HEHEyZMKOgYQUlZmzJlCk899RQTJ06krq6OqVOnMnXq1IKPQRB6Q65cFicaBaXyZRRQSmCGQiH50dGHiUaj2XKn0tf60tWka1y9cjdoc2s0ayb82EdrdmjRSMZk5RJLmmi8u+J/8PQP85c53HDIr6GmXvPdFZOu1RcM79M6VhLr6vOJJXJjySyxTW8j7NEDqrnz+3vx9CdrufKZLzUZH3L+FCqJU045hfHjx/OXv/yF888/n8MPP5xXX32V6667jmuvvRaXy8WsWbP44x//mGfKPGDAAJ599lkuv/xyrrzySsaOHctNN93EsmXLShaNdtttN1577TUuv/xyrr32WkaMGMENN9zAxo0bCxJUtm3bxm9+85u8ZTfffDMAo0eP3k40+te//kU8HueMM84oqp2XXnopL7zwAq+//jrRaJTRo0dz4403csUVV2TXOeWUU/jpT3/Kk08+yaOPPko6neb000/n0EMP5f777+cPf/gDl112GWPHjuWPf/wjq1atKlg0Arjvvvv46U9/ys9//nNisRjXXXediEaCpsiVy+LY7bD33pBK2YG9sdvBbpeswr6M3W7PGhNKX+tLV5Ou06ePYnx9DaFIgg++aeShOato2cFT1WRMCkURaYXwNph9q/J82B5QP0l5PPFoXQQjKM4kuS8Y3uspoHT1+URVopFZ6bt6G2Fn0NIQW86fgtU466yzOOuss7p8zW638803+WmZhx12GIcddliv2z3iiCNYsGDBdsvVEUnQvdl1xghczcyZM/nkk0963WZXjBkzpihj7QsvvJALL7yw4PUzXHPNNVxzzTU9ruNwOLj11lu59dZbt3vtnHPO4ZxzztlueaGfG8B+++3X5eckCFohopHF8flg3jwAHzDP5NYIRuDz+Zg3T/raCLqadDnsNvYdNwBQojQemrOqoieWWiBjUiiYhq/h7lmQCHcssMGp98OA8brvuhgRpS8Y3ufmDzqkp3Xx+aij1syLNNq+4qUeBDT0tpPzpyAIglDpyC0PQRB2WHq7a501xa7gFBZhx8TlMKmaypzbFMHI7gRXNexzoSGCkULhgkI2kqaCowjTRRxvsWQ+nzvf+YY9/t/rvPTlxrz0tFAsQSplXmlsvUUjv4aRRoIgCIJQ6UikkSAIOy69RCbIxEGoVPxeF01tBlUGa2+CZy+E0BbY/JWy7KyXYdQ+xuy/g2ykUQGRNxnD+74QaaSHfrLnqP7YbJBIpdnWHuflhRuJJnLVfdJpCEYT1Pr0LwXeFbqnp3VhBC4IgiAIOyoSaWRxwmE44ADYd98w++13AAcccADhcLj3NwoVSzgc5oADpK+NIHOn3t7N/CMgkUaAjMlKpMaTuyeU1Dsi5MM7YNnrsPELSMVh5D4wcoa+++yCbORgEZFGfeG7rUfUzeFTBvPpr4/gmm9NBhThXB1ppCwz/rPT2wg7Q+aGQVssSaJMM3k5fwqCIAiVjkQaWZxUCubMAUgBczqWmVgNR9CdVCrFnDnS10aQ6sUTJDNxCEWVVAx7d+pSH0fGZOWhFo1CkQS1VRpHhKSS8Nk/IdwEnzygLDvsWhg6DYbvrf+svguyfjcFrJv5bscSKaKJJB6nQ8eW6UMxkVWlUFftZtSAKgCa22PZ86XTbiORStMaTkB/XXbdLWmMSYnLiIqgnP/7VblL3pacPwVBEIRKR0Qji+PxwLPPQjLpAZ7F4QCPx2N2swQd8Xg8PPvss9nHgn70ZqqamTik04qHR8YcdkdDxmTlofY0ao3EtReNPnkAXv5F7nntSNj/Z+Aw72dFUZFGHic2W0eaVSSBp6YCRSMdPY0yZM55jcFodtmAGjebW6OmRhrpjcthx+dyEI4nCUbKE40q7vy5ajbMuRU2fAGhTfDdx2DyccpryTi8/VtY9gZsWwWeAIw7GA6/HgJDc9tob4JXroSvXwWbHaYcD0f/ETw1uXU2LVTOIes/g+qBMOMCOPAyww5TEARBKBwRjSyO0wknnQRKV51kalsEY3A6nZykdLqgMzkj7K7xuhy4nXZiiRSt4fgOKxrJmKw81HNrTT25Qlsg0gof3aU8H3cI9BsFu3/fVMEIiqsmZrfbqHE7CUYTtIbjDKypgMl8J/T0NMqQEc4bQzl/rIE1ng7RyHg/qFx6mv6RbH6vk3A8SUs4zsgytlNx5894OwyeCnv8AJ76wfavbZwPM6+AIbtCuBle/SU8cTpc+G5uvf+cD8HN8MPnFKHp+Yvhvz+Db9+vvB5phUdOVgSn4/6qeKE9fwl4a2Hvsw060N4pply7IAhCJVLoeU5EI0EQdlgKmYAEvE4aQzExwxYqCvVvAM18e5a9CY+dmnvuqYXvPpofPWAixZZj93sV0ahSv9u5yCr9BJSMUB7r8PWx26B/R9SNGX5QvQn9WhLwudgSNEccM5WdjlD+usJbCz98Pn/Zt/4M9x4KzWuh30ho+Bq+eRPOfweG76msc8yf4bFvw5E3KhFJC56GZAxOvAOcbhg0GTYtUPzRLCAaORxK5GE8Hsfn85ncGkEQBP1IJJRrnNPZsywkopHFSSbh/fchmUwC7+NwwEEHHZS9oAl9j2Qyyfvvvw9IX+tNIROQgNe1w4tGMiYrD7X3yyMfreb9ZQ30r3Lzg31H43WV2H/v36z8d/rA5YVZv7KMYKSmUEEh4HOxoSVSsd9tI6IgMlXEMriddlVlMTMijfRPycuglVm6Vc6fwWCQ1tbW7HOPx6NNulykFbApghLA2rnK44xgBEpEkc0O6z+BwPGwdh6MPkARjDJMOAxm/w3C28BnsFlWJ1wuFx6Ph5aWFvx+vyGRbYIgCGbQ2tqKw+Ho9dokopHFiUTgkEMAIsAhAIRCIaqrq81slqAjkUiEQw6RvjaEAiYg2YlDuPKrLJWKjMnK5qUvN2Yf1/pcnLZ3gck2W5bAfy+FWJvyXdnyFdidcOnn+f4lFqFYCaXSK6gV4+FUKmpDdQC3w47fo0QfmXlONGIKnzFLL1ccs8r5c8qUKXnPr7vuOq6//vryNhqPwJvXwa7fBm9AWRbaDNX1+es5nIoQFNqcW6f/6Px1qgd1vLbFdNEIYODAgaxfv55169ZRW1uLy+US8UgQhD5DOp2mra2N1tZWhg4d2uv5TUQji2OzwZQpkErZsNmmYLMZk8svmIfNZsv+uJO+*********************************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", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["game_state = b2.GameState(initial_state_game)\n", "game_state.fastForward(target_round = 17)\n", "dd.viewH<PERSON><PERSON>(game_state)\n", "print(\"The current cash and eco are (%s,%s)\"%(game_state.cash, game_state.eco))\n", "b2.writeLog(game_state.logs, filename = 'tut_log')"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## A Note on Eco Impact\n", "\n", "Eco impact measures the usefulness of a farm in terms of eco. It essentially answers the following question: How much eco would I need to make as much money as my farm did over the course of its lifetime during the simulation? Let $M$ be the amount of money the farm made during the simulation. Define $B$ to be the minimum between the time the farm was sold and the end of simulation time and define $A$ to be the maximum between the time the farm was purchased and the start of simulation time. Then, the eco impact $I_F$ of the farm is given by $$I_F = \\frac{6M}{B-A}$$\n", "Eco impact is a useful way of measuring the effectiveness of a farm and can be helpful in weighing the benefits/downsides of forgoing eco for more farms."]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}